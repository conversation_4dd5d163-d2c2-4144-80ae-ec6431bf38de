import GenericModal from '@/components/genericModal';
import { useEffect, useState } from 'react';
import EditTemplateExerciseList from '../editTemplateExcerciseList';
import tw from 'twin.macro';
import EditIcon from '@assets/svgs/exercise-library/edit-icon.svg';
import { useForm } from 'react-hook-form';
import { showError } from '@/libs/react.toastify';
import EditTemplateModalDuration from '../editTemplateModalDuration';
import ExerciseDetailsModelHeader from '../exerciseDetailsModelHeader';
import TextArea from '@/components/shared/textarea';
import EditTemplateContainerSections from '../editTemplateContainerSections';
import ExerciseDetailsModelTags from '../exerciseDetailsModelTags';
import Input from '@/components/shared/input';
import { exerciseLibraryActions } from '@/reducers/exercise-library';
import { useExerciseLibrary } from '@/zustand/exercise-library';
import { prescriptionMode } from '@/constants/constants';

const EditTemplateModel = ({
  openModel,
  handelCloseMode,
  onSubmit,
  exerciseList,
  template,
  setValue,
  handleAddSelectedExercises,
  dispatch,
  firstTemplateIds,
  setSelectedCard,
  selectedCard,
}) => {
  const [selectedExercise, setSelectedExercise] = useState([]);
  const [exerciseListTemplate, setExerciseListTemplate] = useState([]);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [templateTitle, setTemplateTitle] = useState('Whole body stiffness relief');
  const { setMode, setEditTemplateData, mode } = useExerciseLibrary();
  const { register, reset, watch } = useForm({
    defaultValues: { frequency_week: 1, frequency_day: 1, duration_template: 1 },
  });

  useEffect(() => {
    if (exerciseList?.length > 0) {
      setExerciseListTemplate(exerciseList);
      reset({
        duration_template: template.duration,
        frequency_day: template.frequency.day,
        frequency_week: template.frequency.week,
        description: template.description,
      });
    }
  }, [exerciseList]);

  useEffect(() => {
    if (
      watch('duration_template') ||
      watch('frequency_day') ||
      watch('frequency_week') ||
      watch('description')
    ) {
      const updated = selectedCard.map(_item => ({ ..._item, templateId: null }));
      setSelectedCard(updated);
    }
  }, [
    watch('duration_template'),
    watch('frequency_day'),
    watch('frequency_week'),
    watch('description'),
  ]);

  const editTemplate = () => {
    try {
      const newItem = [
        { id: 0, text: 'prescription' },
        { id: 1, text: 'Template' },
      ];
      dispatch({ type: exerciseLibraryActions.prescriptionTabArray, payload: newItem });
      dispatch({ type: exerciseLibraryActions.activePrescriptionTab, payload: 1 });
    } catch (error) {
      showError(error.message);
    }
  };

  const Tags = [
    { id: 0, title: 'Region: Chest' },
    { id: 1, title: 'Region: Back' },
    { id: 2, title: 'Region: Knees' },
    { id: 3, title: 'Joints: Hips' },
    { id: 4, title: 'Region: Chest' },
  ];

  const handelEditTemplate = (exercises, template) => {
    const newExercises = exercises.map(exercise => ({
      ...exercise,
      id: `target-id-${exercise.id}-${Date.now()}-${Math.random()}`,
      isDuplicate: false,
      fromTemplate: true,
    }));

    setValue('frequency_day', template.frequency?.day || 1);
    setValue('frequency_week', template.frequency?.week || 1);
    setValue('duration', template.duration || 1);
    dispatch({
      type: exerciseLibraryActions.setFirstTemplateIds,
      payload: [...(firstTemplateIds || []), { tab: 1, id: template.id }].filter(
        (v, i, arr) => arr.findIndex(e => e.tab === v.tab && e.id === v.id) === i
      ),
    });
    setEditTemplateData(newExercises);
  };

  return (
    <GenericModal
      PrimaryButtonStyle={tw`basis-[50%] rounded-[6px] font-semibold`}
      SecondaryButtonStyle={tw`basis-[50%] rounded-[6px] font-semibold`}
      backgroundModal={'rgba(0,0,0,0.4)'}
      handelCloseMode={handelCloseMode}
      handelFormSubmit={onSubmit}
      modalStyle={tw`backdrop-blur-[0px]`}
      containerModalStyle={tw`rounded-card! w-[40vw]`}
      PrimaryButtonText={`Add ${selectedExercise.length > 0 ? 'selected' + `(${selectedExercise.length})` : ''} to prescription`}
      secondaryButtonText={'Edit Template'}
      disableSecondaryButton={mode === prescriptionMode.TEMPLATE}
      openModel={openModel}
      element="div"
      typeOfPrimaryButton={'button'}
      typeOfSecondaryButton={'button'}
      clickOnPrimaryButton={() => {
        if (selectedExercise.length === 0) {
          handleAddSelectedExercises(template?.exercises, template.id);
          if (selectedCard.length === 0) {
            setValue('frequency_day', template.frequency.day);
            setValue('frequency_week', template.frequency.week);
            setValue('duration', template.duration);
          }
        } else {
          handleAddSelectedExercises(selectedExercise);
        }
        handelCloseMode();
      }}
      clickOnSecondaryButton={() => {
        handelEditTemplate(exerciseListTemplate, template);
        editTemplate();
        handelCloseMode();
        setMode(prescriptionMode.TEMPLATE);
      }}
      containerButtonsStyle={tw`p-6!`}
      content={
        <div tw="max-h-[65vh] overflow-y-auto" className="element">
          <EditTemplateExerciseList
            setSelectedExercise={setSelectedExercise}
            exerciseListTemplate={exerciseListTemplate}
            selectedExercise={selectedExercise}
            setExerciseListTemplate={setExerciseListTemplate}
          />
          <EditTemplateModalDuration register={register} />
          <EditTemplateContainerSections>
            <TextArea
              label={'Description'}
              name={'description'}
              register={register}
              rows={6}
              labelStyle={tw`mb-2`}
              textAreaStye={tw`rounded-[6px] border border-border_stroke p-[12px] focus:(border border-Primary_600) text-[1rem]`}
              containerSTyle={tw`p-0 border-none bg-[#EEF0F3] duration-300 transition-all`}
              disabled
              placeholder={'description'}
            />
          </EditTemplateContainerSections>
          <EditTemplateContainerSections>
            <ExerciseDetailsModelTags tagsList={Tags} containerStyle={tw`border-none`} />
          </EditTemplateContainerSections>
        </div>
      }
      title={
        <ExerciseDetailsModelHeader
          handelCloseClick={handelCloseMode}
          customTitle={
            <div tw="flex gap-2">
              {isEditingTitle ? (
                <div tw="w-[220px]">
                  <Input
                    name="template_title"
                    onBlur={e => {
                      if (e.target.value.length < 3) {
                        showError('must add at least 3 charters');
                        return;
                      } else {
                        setIsEditingTitle(false);
                        setTemplateTitle(e.target.value);
                        setValue('template_title', e.target.value);
                      }
                    }}
                    defaultValue={templateTitle}
                    placeholder={templateTitle}
                    inputStye={tw`font-bold text-[1.1rem] rounded border border-border_stroke px-2 py-1 outline-none`}
                    containerSTyle={tw`m-0 p-0 border-none bg-transparent rounded border border-border_stroke`}
                    autoFocus
                    register={register}
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        setIsEditingTitle(false);
                        setTemplateTitle(e.target.value);
                        setValue('template_title', e.target.value);
                      }
                    }}
                  />
                </div>
              ) : (
                <p tw="font-bold text-[1.1rem]">{templateTitle}</p>
              )}
              <img
                src={EditIcon}
                tw="w-5 cursor-pointer"
                alt="edit"
                onClick={() => setIsEditingTitle(true)}
              />
            </div>
          }
          hideArrow
          containerStyle={tw`bg-neutral-50`}
        />
      }
    />
  );
};

export default EditTemplateModel;
