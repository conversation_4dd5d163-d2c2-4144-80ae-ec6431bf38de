import Input from '@/components/shared/input';
import SearchIcon from '@assets/svgs/exercise-library/search-icon.svg';
import tw from 'twin.macro';

const SearchPage = ({ register }) => {
  return (
    <div tw="w-[17%]">
      <Input
        name="search"
        register={register}
        firstIcon={
          <div>
            <img src={SearchIcon} tw="opacity-50" alt="search" />
          </div>
        }
        placeholder="Search"
        containerSTyle={tw`bg-white px-[16px] h-[35px]`}
      />
    </div>
  );
};

export default SearchPage;
