import PaymentCard from '@/components/settings/paymentCard';
import tw from 'twin.macro';
import Radio from '@/components/shared/radio';
import { paymentMethodsCards } from '@/components/staff/constant';
function PaymentMethodsList({ control, setMehtod }) {
  return (
    <div tw="border-b border-border_stroke space-y-6 p-6 max-h-[23rem] overflow-y-auto">
      {paymentMethodsCards.map(item => (
        <div key={item.id} onClick={() => setMehtod(item.title)}>
          <Radio
            key={item.id}
            inputId={item.id}
            control={control}
            name="paymentMethod"
            value={{ id: item.id, title: item.title }}
            customable={
              <label htmlFor={item.id} tw="flex-1 cursor-pointer">
                <PaymentCard
                  date={item.expire}
                  imageUrl={item.imageUrl}
                  title={item.title}
                  isExpire={item.isExpires}
                  isDefault={item.isDefault}
                  key={item.id}
                  id={item.id}
                  isHasActionsButtons={false}
                  rootElementStyle={tw`p-3 flex-1`}
                />
              </label>
            }
          />
        </div>
      ))}
    </div>
  );
}

export default PaymentMethodsList;
