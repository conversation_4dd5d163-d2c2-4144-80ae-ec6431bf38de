import InsightCardList from '@/components/InsightList';
import { insightsData } from '@/components/patient/constant';
import History from '@/components/patient/history';
import Notes from '@/components/patient/notes';

import PatientProfileForm from '@/components/patient/profile/patiemtProfileForm';
import PrescriptionList from '@/components/prescriptionList';

import TabComponent from '@/components/shared/tabComponent';

import { getPatientById } from '@/mock/patients-data';
import { getTherapistById } from '@/mock/therapists-data';
import { useEffect, useState } from 'react';

import { useParams } from 'react-router-dom';
import 'twin.macro';

const Patient = () => {
  const { id, staff_id } = useParams();

  const patientData = getPatientById(Number(id));
  const [therapist, setTherapist] = useState(null);

  useEffect(() => {
    if (!staff_id) return;
    const therapist = getTherapistById(1, staff_id);
    setTherapist(therapist);
  }, [staff_id]);

  const breadcrumbItems = [
    therapist && { label: 'Staff', to: '/staff' },
    therapist && { label: therapist.Name, to: `/staff/${therapist.id}` },

    { label: 'Patients', to: therapist ? `/staff/${therapist.id}/?tab=Patients` : '/patients' },
    { label: patientData?.Name },
  ].filter(Boolean);

  return (
    <>
      <TabComponent
        breadcrumbItems={breadcrumbItems}
        tabsKeys={['Profile', 'History', 'Prescriptions', 'Notes']}
        tabsContent={[
          <div key={1} tw="flex gap-6 items-start w-full">
            <PatientProfileForm id={id} />
            <InsightCardList data={insightsData} />
          </div>,
          <History key={2} patientData={patientData} />,
          <PrescriptionList key={3} />,
          <Notes key={4} patient_id={id} />,
        ]}
      />
    </>
  );
};
export default Patient;
