import React, { useRef } from 'react';
import 'twin.macro';
import tw from 'twin.macro';

/**
 * ImageUploader Component
 *
 * @param {Object} props
 * @param {boolean} props.disabled
 * @param {(file: File) => void} props.onFileUpload
 * @param {React.ReactNode} props.children
 * @param {function} props.registerTrigger
 */
const ImageUploader = ({ disabled, onFileUpload, children, registerTrigger }) => {
  const inputRef = useRef(null);

  const handleChange = e => {
    const file = e.target.files?.[0];
    if (!file) return;

    // if (!file.type.startsWith('image/')) {
    //   // console.warn('Only image files are allowed');
    //   showError('Only JPG, JPEG, and PNG files are allowed.');
    //   return;
    // }

    onFileUpload?.(file);
    e.target.value = ''; // reset input
  };

  const handleClick = () => {
    if (disabled) return;
    inputRef.current?.click();
  };

  // Expose trigger function to parent if registerTrigger is provided
  React.useEffect(() => {
    if (registerTrigger) {
      registerTrigger(() => {
        if (!disabled) inputRef.current?.click();
      });
    }
  }, [registerTrigger, disabled]);

  return (
    <div tw="h-full">
      <div
        tw="h-full cursor-pointer"
        css={disabled && tw`cursor-not-allowed`}
        onClick={handleClick}
      >
        {children}
      </div>

      <input
        type="file"
        accept="image/*"
        ref={inputRef}
        style={{ display: 'none' }}
        onChange={handleChange}
      />
    </div>
  );
};

export default ImageUploader;
