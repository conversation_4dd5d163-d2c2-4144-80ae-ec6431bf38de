import React from 'react';
import AddIcon from '@assets/svgs/add-icon.svg';
import AddIconHover from '@assets/svgs/add-icon-hover.svg';
import tw from 'twin.macro';

const Add = ({ handleClick }) => (
  <button
    type="button"
    css={tw`relative  w-[18px] h-[18px] p-0 border-none bg-none`}
    className="group"
    onClick={handleClick}
    onPointerDown={e => e.stopPropagation()}
  >
    <img
      src={AddIcon}
      alt="Add Icon"
      css={tw`absolute top-0 left-0  w-[18px] h-[18px] transition-opacity duration-300 opacity-100`}
      draggable={false}
    />
    <img
      src={AddIconHover}
      alt="Add Icon Hover"
      css={tw`absolute top-0 left-0  w-[18px] h-[18px] transition-opacity  duration-300 opacity-0 group-hover:opacity-100`}
      draggable={false}
    />
  </button>
);

export default Add;
