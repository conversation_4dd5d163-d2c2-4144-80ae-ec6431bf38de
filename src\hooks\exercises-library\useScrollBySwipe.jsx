import { useEffect, useRef } from 'react';

const useScrollSwipe = () => {
  const scrollRef = useRef(null);

  useEffect(() => {
    const el = scrollRef.current;
    if (!el) {
      return;
    }

    let isDown = false;
    let startX;
    let scrollLeft;

    const mouseDownHandler = e => {
      isDown = true;
      el.classList.add('cursor-grabbing');
      startX = e.pageX - el.offsetLeft;
      scrollLeft = el.scrollLeft;
    };

    const mouseLeaveOrUpHandler = () => {
      isDown = false;
      el.classList.remove('cursor-grabbing');
      el.classList.remove('select-none');
    };

    const mouseMoveHandler = e => {
      if (!isDown) {
        return;
      }
      e.preventDefault();
      el.classList.add('select-none');
      const x = e.pageX - el.offsetLeft;
      const walk = (x - startX) * 1.5; // speed multiplier
      el.scrollLeft = scrollLeft - walk;
    };

    el.addEventListener('mousedown', mouseDownHandler);
    el.addEventListener('mouseleave', mouseLeaveOrUpHandler);
    el.addEventListener('mouseup', mouseLeaveOrUpHandler);
    el.addEventListener('mousemove', mouseMoveHandler);

    return () => {
      el.removeEventListener('mousedown', mouseDownHandler);
      el.removeEventListener('mouseleave', mouseLeaveOrUpHandler);
      el.removeEventListener('mouseup', mouseLeaveOrUpHandler);
      el.removeEventListener('mousemove', mouseMoveHandler);
    };
  }, []);

  return { scrollRef };
};

export default useScrollSwipe;
