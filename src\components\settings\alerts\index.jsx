import SectionCards from '@/components/sectionsCard';
import AlertsCard from '../alertsCard';
import { useState } from 'react';
import 'twin.macro';
import tw from 'twin.macro';

const alertsList = [
  {
    key: 'alert1',
    title: 'New patient added',
    subtitle: 'Alerts when a new patient is added on the platform.',
  },
  {
    key: 'alert2',
    title: 'Onboarding',
    subtitle: 'Alerts upon successful onboarding of a patient.',
  },
  {
    key: 'alert3',
    title: 'Missed session',
    subtitle: 'Alerts if a patient misses a scheduled session.',
  },
  {
    key: 'alert4',
    title: 'Pain report',
    subtitle: "Alerts following a patient's report of pain after a session.",
  },
  {
    key: 'alert5',
    title: 'Prescription completion',
    subtitle: 'Alerts when a patient completes an assigned prescription.',
  },
  {
    key: 'alert6',
    title: 'Prescription expiry',
    subtitle: "Alerts as a patient's prescription nears or reaches its end date.",
  },
  {
    key: 'alert7',
    title: 'Inactivity',
    subtitle: "Alerts if the patient hasn't logged in for a continuous two-week period.",
  },
];
const Alerts = () => {
  const [alertsState, setAlertsState] = useState(() => {
    const initial = {};
    alertsList.forEach(alert => {
      initial[alert.key] = 1;
    });
    return initial;
  });

  const handleToggle = key => {
    setAlertsState(prev => ({ ...prev, [key]: prev[key] === 1 ? 0 : 1 }));
  };

  return (
    <div>
      <SectionCards customStyle={tw`p-[24px]`}>
        <h1 tw="font-semibold text-[1.25rem] mb-[24px]">Notifications settings</h1>
        {/* <h2 tw="font-semibold text-[1rem]">Toast notifications</h2>
        <h3 tw="font-normal mb-[24px] text-[.875rem]">
          Customize the pop-up messages in the home screens.
        </h3> */}
        {alertsList.map(alert => (
          <AlertsCard
            key={alert.key}
            title={alert.title}
            subtitle={alert.subtitle}
            enabled={alertsState[alert.key] === 1}
            onToggle={() => handleToggle(alert.key)}
          />
        ))}
      </SectionCards>
    </div>
  );
};

export default Alerts;
