import React, { useState, useEffect } from 'react';
import AssignPrescription from './assignPrescription';
import AddPatient from '@/components/addPatientForm';
import PreSessionMessages from './preSessionMessages';
import VoiceRecording from './voiceMessage/voiceRecording';
import TextToSpeech from './voiceMessage/textToSpeech';
import GenericModal from '@/components/genericModal';
import tw from 'twin.macro';
import ModalTitle from './modalTitle';
import { showSuccess } from '@/libs/react.toastify';

export const ModalViews = {
  ASSIGN_PRESCRIPTION: 'assignPrescription',
  ADD_PATIENT: 'addPatient',
  PRE_SESSION_MESSAGES: 'preSessionMessages',
  VOICE_RECORDING: 'voiceRecording',
  TEXT_TO_SPEECH: 'textToSpeech',
};

const PrescriptionModal = ({
  open,
  handleClose,
  exercises,
  register,
  handleSubmit,
  control,
  watch,
  setValue,
  getValues,
  trigger,
  errors,
  isValid,
  reset,
  setSelectedCard,
  currentModalView = ModalViews.ASSIGN_PRESCRIPTION,
}) => {
  const [modalView, setModalView] = useState(currentModalView);
  const [patientsOptions, setPatientsOptions] = useState([]);
  const [voiceField, setVoiceField] = useState('');
  const weeksCount = parseInt(watch('duration'), 10);
  const patientCount = watch('patients')?.length || 0;

  useEffect(() => {
    // Fetch default options (e.g., 10 names)
    fetch('https://api.datamuse.com/words?sp=*&max=10&topics=first+names')
      .then(res => res.json())
      .then(data => {
        const options = data.map(item => ({
          label: item.word.charAt(0).toUpperCase() + item.word.slice(1),
          value: Math.floor(Math.random() * 1000000),
        }));
        setPatientsOptions(options);
      });
  }, []);

  useEffect(() => {
    // Get the current exerciseMessages from the form state
    const currentMessages = getValues('exerciseMessages') || {};

    // Create a set of valid exercise IDs from the exercises prop
    const validExerciseIds = new Set(exercises.map(exercise => exercise.id));

    // Filter the exerciseMessages to only include messages for valid exercise IDs
    const sanitizedMessages = Object.keys(currentMessages)
      .filter(exerciseId => validExerciseIds.has(exerciseId))
      .reduce((acc, exerciseId) => {
        acc[exerciseId] = currentMessages[exerciseId];
        return acc;
      }, {});

    // Only update if sanitizedMessages is different from currentMessages
    const isDifferent =
      Object.keys(sanitizedMessages).length !== Object.keys(currentMessages).length ||
      Object.keys(sanitizedMessages).some(key => sanitizedMessages[key] !== currentMessages[key]);

    if (isDifferent) {
      setValue('exerciseMessages', sanitizedMessages);
    }
  }, [exercises, getValues, setValue]);

  const handleAssignPrescription = handleSubmit(data => {
    // Extract only the desired fields from the form state
    const {
      frequency_day,
      frequency_week,
      duration,
      prescription_name,
      patients,
      startDate,
      generalMessage,
      exerciseMessages,
    } = data;

    // Create a new object with the desired fields and include the exercises prop
    const prescriptionData = {
      frequency_day,
      frequency_week,
      duration,
      prescription_name,
      patients,
      startDate,
      generalMessage,
      exerciseMessages,
      exercises, // Add exercises from the props
    };
    // Reset the form fields to their initial values
    reset({
      frequency_day: 1,
      frequency_week: 1,
      duration: 1,
      prescription_name: '',
      patients: null,
      startDate: new Date(),
      generalMessage: null,
      exerciseMessages: {},
    });

    // Reset the selectedCard state to an empty array
    setSelectedCard?.([]);

    // Close the modal
    handleClose();

    showSuccess('Prescription assigned successfully!');
  });

  return (
    <GenericModal
      openModel={open}
      handelCloseMode={handleClose}
      containerModalStyle={[
        tw`!rounded-[12px] h-fit shadow-[0px_5px_15px_0px_rgba(0,0,0,0.08),0px_15px_35px_-5px_rgba(17,24,38,0.2),0px_0px_0px_1px_rgba(152,161,178,0.1)] overflow-visible`,
        modalView === ModalViews.PRE_SESSION_MESSAGES ? tw`w-[65%]` : tw`w-[47%]`,
      ]}
      title={
        <ModalTitle modalView={modalView} setModalView={setModalView} handleClose={handleClose} />
      }
      titleStyle={tw`!p-0 border-b border-stroke !rounded-t-[12px]`}
      content={
        <>
          {modalView === ModalViews.ASSIGN_PRESCRIPTION && (
            <AssignPrescription
              errors={errors}
              control={control}
              watch={watch}
              patientsOptions={patientsOptions}
              weeksCount={weeksCount}
              setModalView={setModalView}
              exercises={exercises}
            />
          )}
          {modalView === ModalViews.ADD_PATIENT && (
            <AddPatient
              setModalView={setModalView}
              customOnSubmit={data => {
                const fullName = `${data.firstName} ${data.lastName}`;
                // generate a random value (value in react select is a unique identifier for the option) for the new patient
                // this value should come from the backend, but for now we will generate a random number
                const newPatient = { label: fullName, value: Math.floor(Math.random() * 1000000) };
                setPatientsOptions(prev => [...prev, newPatient]);
                // Update the form's patients field directly:
                const prevPatients = getValues('patients') || [];
                setValue('patients', [...prevPatients, newPatient]);
                trigger('patients'); // Trigger validation for the patients field
                setModalView(ModalViews.ASSIGN_PRESCRIPTION);
              }}
              customOnCancel={() => setModalView(ModalViews.ASSIGN_PRESCRIPTION)}
            />
          )}
          {modalView === ModalViews.PRE_SESSION_MESSAGES && (
            <PreSessionMessages
              state={getValues('exerciseMessages')}
              register={register}
              errors={errors}
              setModalView={setModalView}
              setVoiceField={setVoiceField}
              watch={watch}
              setValue={setValue}
              exercises={exercises}
            />
          )}
          {modalView === ModalViews.VOICE_RECORDING && (
            <VoiceRecording setModalView={setModalView} setValue={setValue} field={voiceField} />
          )}
          {modalView === ModalViews.TEXT_TO_SPEECH && (
            <TextToSpeech setModalView={setModalView} setValue={setValue} field={voiceField} />
          )}
        </>
      }
      contetnContainerStyle={tw` flex flex-col min-h-[0px]`}
      element={modalView === ModalViews.ASSIGN_PRESCRIPTION ? 'form' : React.Fragment}
      hasPrimaryButton={
        modalView === ModalViews.ASSIGN_PRESCRIPTION ||
        modalView === ModalViews.PRE_SESSION_MESSAGES
      }
      hasSecondaryButton={modalView === ModalViews.ASSIGN_PRESCRIPTION}
      PrimaryButtonText={
        modalView === ModalViews.PRE_SESSION_MESSAGES
          ? 'Back to assignment'
          : modalView === ModalViews.ASSIGN_PRESCRIPTION && patientCount > 1
            ? 'Assign to patients'
            : modalView === ModalViews.ASSIGN_PRESCRIPTION && patientCount <= 1
              ? 'Assign to patient'
              : ''
      }
      typeOfPrimaryButton={modalView === ModalViews.ASSIGN_PRESCRIPTION ? 'submit' : 'button'}
      PrimaryButtonStyle={[
        tw` !py-[10px] !px-[10px] !rounded-[8px] border-Primary_800 text-Primary_800 !font-bold `,
        modalView === ModalViews.PRE_SESSION_MESSAGES ? tw`` : tw`flex-1`,
      ]}
      PrimaryButtonDisable={modalView === ModalViews.ASSIGN_PRESCRIPTION ? !isValid : false}
      clickOnPrimaryButton={
        modalView === ModalViews.PRE_SESSION_MESSAGES
          ? () => setModalView(ModalViews.ASSIGN_PRESCRIPTION)
          : null
      }
      secondaryButtonText="Edit prescription"
      typeOfSecondaryButton="button"
      SecondaryButtonStyle={tw`flex-1 !py-[10px] !rounded-[8px] !font-bold border-Primary_800 text-Primary_800 text-sm`}
      clickOnSecondaryButton={handleClose}
      containerButtonsStyle={[
        modalView === ModalViews.ADD_PATIENT ||
        modalView === ModalViews.VOICE_RECORDING ||
        modalView === ModalViews.TEXT_TO_SPEECH
          ? tw`!p-0 border-none`
          : tw`px-8 py-6 gap-3 !border-transparent !border-t-stroke  rounded-b-[12px]`,
        modalView === ModalViews.PRE_SESSION_MESSAGES ? tw`justify-end` : tw``,
      ]}
      handelFormSubmit={
        modalView === ModalViews.ASSIGN_PRESCRIPTION ? handleAssignPrescription : null
      }
    />
  );
};
export default PrescriptionModal;
//
