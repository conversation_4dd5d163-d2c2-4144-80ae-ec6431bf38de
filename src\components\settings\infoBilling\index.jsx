import Info from '@assets/svgs/settings/Info Icon.svg';
import 'twin.macro';

const InfoBilling = () => {
  return (
    <div tw="flex gap-4 items-center bg-Primary_100 rounded-[6px] p-[16px]">
      <img src={Info} alt="Info" />
      <div tw="flex-1 space-y-[8px]">
        <p tw="font-bold text-[1rem]">Your trial ends in 10 days</p>
        <p tw="font-normal text-[1rem] ">
          If you cancel your subscription before<strong>Sunday, 15 June 2025,</strong> your paid
          subscription won't start (and you won't be charged).
        </p>
      </div>
      <button tw="font-semibold text-Primary_800 hover:(bg-Primary_800 text-white) transition-all duration-300 text-[1rem] border border-Primary_800 rounded-[8px] px-[24px] py-[10px]">
        Dismiss
      </button>
    </div>
  );
};

export default InfoBilling;
