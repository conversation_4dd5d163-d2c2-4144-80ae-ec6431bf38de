import * as yup from 'yup';

export const schemaPayment = yup.object({
  currentEmail: yup
    .string()
    .email('Please enter a valid email address')
    .nullable('')
    .matches(/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i, 'Please enter a valid email address'),
  firstName: yup.string().required('Required field.'),
  PostalCode: yup.string().required('Required field.'),
  lastName: yup.string().required('Required field.'),
  streetName: yup.string().required('Required field.'),
  houseName: yup.string().required('Required field.'),
  projectName: yup.string().required('Required field.'),
  code: yup.string().nullable(),
  city: yup
    .object()
    .shape({
      label: yup.string().required(),
      value: yup.number().required(),
    })
    .required('Required field.'),
  country: yup
    .object()
    .shape({
      label: yup.string().required(),
      value: yup.number().required(),
    })
    .required('Required field.'),
});
