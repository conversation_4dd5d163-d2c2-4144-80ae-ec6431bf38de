import InputNumber from '@/components/shared/inputNumber';
import tw from 'twin.macro';

const DurationInput = ({
  register,
  hasTooltip,
  placeholder,
  name,
  label,
  maxWidth,
  inputStyle,
  labelStyle,
}) => {
  return (
    <InputNumber
      register={register}
      name={name}
      type="number"
      showArrow={false}
      placeholder={placeholder}
      label={label}
      inputStye={[tw`py-[10px]`, inputStyle]}
      hasTooltip={hasTooltip}
      max={999}
      maxWidth={maxWidth}
      labelStyle={[tw`text-[0.8rem]`, labelStyle]}
      disabled
      containerSTyle={tw`text-[0.8rem] placeholder:text-[0.8rem] py-[2px] bg-[#EEF0F3]`}
    />
  );
};
export default DurationInput;
