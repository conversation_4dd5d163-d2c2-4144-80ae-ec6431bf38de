import React, { useState, useRef, useEffect } from 'react';
import Arrow from '@assets/svgs/collapse-arrow.svg';
import Menue from '@components/shared/menue';
import tw from 'twin.macro';

/**
 * DropDown component
 *
 * @param {Object} props
 * @param {Array<React.ReactNode>} props.items - Elements to display as the dropdown trigger (icons, title, etc.)
 * @param {Object} props.dropCss - Tailwind styles for the dropdown trigger button
 * @param {Object} props.openDropCss - Tailwind styles for the dropdown trigger button when open
 * @param {Array} props.options - List of options to choose from, each option is an `Object` with `id`, `label`, and `value`
 * @param {*} props.selected - Currently selected item
 * @param {Function} props.onSelect - Callback when an option is selected
 * @param {string} [props.selectedIcon] - Optional icon source to display beside the selected item
 * @param {boolean} [props.hasOutline] - Whether the dropdown should have an outline
 * @param {boolean} [props.hasToolTip] - Whether to show a tooltip with the option value on hover
 */
const DropDown = ({
  selected,
  items,
  dropCss,
  openDropCss,
  options,
  onSelect,
  onDeselect,
  selectedIcon,
  hasOutline,
  hasToolTip,
  menueStyle,
  menueItemStyle,
  hasArrow,
}) => {
  const [open, setOpen] = useState(false);
  const ref = useRef();

  // Close dropdown on outside click
  useEffect(() => {
    const handleClick = e => {
      if (ref.current && !ref.current.contains(e.target)) setOpen(false);
    };
    if (open) document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [open]);

  return (
    <div ref={ref} css={[tw`relative inline-block`]}>
      <button
        css={[
          tw`
            flex items-center justify-center gap-[8px] text-[14px] font-medium
            border border-border_stroke border-opacity-50 hover:border-opacity-100
            px-[12px] py-[6px] shadow-[0px_1px_0px_0px_rgba(0,0,0,0.1)]`,
          dropCss, // allow additional/override styles (e.g. rounded)
          open && openDropCss, // Apply when open
          hasOutline && tw`focus:ring-2 focus:ring-Primary`,
        ]}
        type="button"
        onClick={() => setOpen(v => !v)}
      >
        {items}
        {hasArrow && (
          <img
            src={Arrow}
            alt="arrow"
            tw="w-[10px] h-[10px] transition-transform duration-200"
            style={{
              transform: open ? 'rotate(0deg)' : 'rotate(180deg)',
            }}
          />
        )}
      </button>
      {open && (
        <Menue
          options={options}
          selected={selected}
          onSelect={onSelect}
          onDeselect={onDeselect}
          setOpen={setOpen}
          hasToolTip={hasToolTip}
          selectedIcon={selectedIcon}
          menueStyle={menueStyle}
          menueItemStyle={menueItemStyle}
        />
      )}
    </div>
  );
};

export default DropDown;
