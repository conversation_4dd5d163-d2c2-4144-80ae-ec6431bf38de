import { useState } from 'react';
import Input from '@/components/shared/input';
import tw from 'twin.macro';

const AuthInput = ({
  name,
  register,
  label,
  placeholder,
  type,
  errorMessage,
  inputRef,
  hideErrorMessage,
  showArrow,
  containerSTyle,
  inputCalsses,
  ...res
}) => {
  const FocusStyle = tw` border-border_stroke transition-all duration-500 font-['Inter'] w-full rounded-[6px]`;
  const blurStyle = tw` border-Primary_600 border transition-all duration-1000 font-['Inter'] w-full rounded-[6px]`;
  const errorStyle = tw`border-[rgba(209, 46, 60, 1)]! border transition-all duration-1000 font-['Inter'] w-full rounded-[6px]`;
  const [isFocus, setIsFocus] = useState(false);
  return (
    <div tw="grid gap-2 w-full">
      {label && <label tw="font-[500] text-[1rem] font-['Inter']">{label}</label>}
      <Input
        name={name}
        register={register}
        errorMessage={errorMessage}
        containerSTyle={[
          errorMessage ? errorStyle : !isFocus ? FocusStyle : blurStyle,
          containerSTyle,
        ]}
        showArrow={showArrow}
        hideErrorMessage={hideErrorMessage}
        placeholder={placeholder}
        type={type}
        className="no-arrows"
        inputRef={inputRef}
        inputStye={inputCalsses}
        {...res}
      />
    </div>
  );
};

export default AuthInput;
