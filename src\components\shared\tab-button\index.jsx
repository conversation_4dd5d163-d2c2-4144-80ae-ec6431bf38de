import tw from 'twin.macro';

/**
 *
 * @param {string} prop.type - Type of button
 * @param {boolean} prop.disable - Whether the button is disabled
 * @param {string} prop.otherStyle - Additional CSS styles for the button
 * @param {string} prop.title - Text content of the button
 *
 * and there is a rest parameter for any other props that may be passed to the button
 *
 * @returns {JSX.Element} - The rendered button element
 */

const TabButton = ({ type = 'button', disable, otherStyle, title, isSelected, ...rest }) => {
  return (
    <button
      type={type}
      tw="w-fit border-primary rounded-[6px] h-full font-[600] px-[10px] py-[6px] text-[0.8rem] duration-500 ease-in-out"
      css={[
        otherStyle,
        disable && tw`bg-disable border-border_stroke`,
        isSelected
          ? tw`bg-Primary_800  border-Primary_800 text-white`
          : tw`bg-none border-Primary_800 text-Primary_800 hover:(bg-Primary_800  border-Primary_800 text-white)`,
      ]}
      disabled={disable}
      {...rest}
    >
      {title}
    </button>
  );
};

export default TabButton;
