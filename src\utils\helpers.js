/* eslint-disable curly */
export const getWindowDimensions = () => {
  const { innerWidth: width, innerHeight: height } = window;
  return {
    width,
    height,
  };
};

export function formatDate(value, format = 'DD/MM/YYYY') {
  if (!value) {
    return '';
  }

  const date = new Date(value);

  if (isNaN(date.getTime())) {
    return '-';
  }

  // replace all "-" with "/"
  const replaceDashWithSlash = str => str.replace(/-/g, '/');

  if (format === 'MM/DD/YYYY') {
    return new Intl.DateTimeFormat('en-US').format(date);
  }
  if (format === 'DD/MM/YYYY') {
    return new Intl.DateTimeFormat('en-GB').format(date);
  }
  if (format === 'YYYY/MM/DD') {
    return replaceDashWithSlash(date.toISOString().slice(0, 10));
  }
  // Fallback: use locale string and replace "-" with "/"
  return replaceDashWithSlash(date.toLocaleDateString());
}

export function calculateEndDate(startDate, no_of_weeks) {
  // Validate startDate
  const date = new Date(startDate);
  if (isNaN(date.getTime())) {
    return '-';
  }

  // Validate no_of_weeks
  if (typeof no_of_weeks !== 'number' || !Number.isInteger(no_of_weeks) || no_of_weeks < 1) {
    return '-';
  }

  // Add (no_of_weeks * 7 - 1) days to include the start date in the count
  date.setDate(date.getDate() + (no_of_weeks * 7 - 1));

  // Format as YYYY-MM-DD (handles month/year rollovers, leap years, etc.)
  const year = date.getFullYear();
  const month = date.toLocaleString('default', { month: 'long' });
  const day = String(date.getDate());

  return `${day} ${month}, ${year}`;
}

export function truncateText({ text, maxLength = 20 }) {
  if (typeof text !== 'string') {
    return '';
  }
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength) + '...';
}

//  2023-05-10 => "Joined on Wednesday, May 10th 2023"
export function formatJoinDate(dateString) {
  const date = new Date(dateString);

  const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
  const formatted = date.toLocaleDateString('en-US', options);

  const day = date.getDate();
  const suffix = getDaySuffix(day);

  // Replace the numeric day with day + suffix (e.g., 10 → 10th)
  const withSuffix = formatted.replace(/\d+/, `${day}${suffix}`);

  return `Joined on ${withSuffix}`;
}
function getDaySuffix(day) {
  if (day >= 11 && day <= 13) return 'th';
  switch (day % 10) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
}

export function toggleItemInArray(array, item) {
  const index = array.indexOf(item);
  if (index === -1) {
    return [...array, item]; // add
  } else {
    return array.filter((_, i) => i !== index); // remove
  }
}

export function addIfNotExists(array, item) {
  if (!array.includes(item)) {
    return [...array, item];
  }
  return array;
}

export function removeIfExists(array, item) {
  return array.filter(i => i !== item);
}

export function isExistInsideArray(array, item, conditionFn) {
  if (!Array.isArray(array)) return false;

  const isSelected = array.some(selectedItem => {
    if (typeof conditionFn === 'function') {
      return conditionFn(selectedItem, item); // use parent condition
    }
    // default condition: compare by `id`
    return selectedItem.id === item.id;
  });

  return isSelected;
}

export function findItemInArray(array, targetItem, conditionFn) {
  if (!Array.isArray(array)) return null;

  return (
    array.find(item => {
      if (typeof conditionFn === 'function') {
        return conditionFn(item, targetItem);
      }
      // Default condition: compare by id
      return item.id === targetItem.id;
    }) || null
  );
}

export const formatTime = timeInSeconds => {
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = Math.floor(timeInSeconds % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

export const formatFileSize = size => {
  if (size < 1024) return `${size} bytes`;
  else if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
  else return `${(size / (1024 * 1024)).toFixed(2)} MB`;
};

export const mergedRefs = (...refs) => {
  return node => {
    refs.forEach(ref => {
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    });
  };
};
