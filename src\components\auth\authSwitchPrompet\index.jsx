import { useAuth } from '@/zustand/auth-store';
import { useNavigate } from 'react-router-dom';
import 'twin.macro';

const AuthRedirectFooter = ({ normalText, linkedText, link, hideBorder = false }) => {
  const removeEmail = useAuth(state => state.removeNewEmail);
  const navigate = useNavigate();
  return (
    <>
      {normalText && !hideBorder && <div tw="border border-stroke" />}
      <p tw="font-['Inter'] text-[1rem] font-[600] text-center text-[#383737]">
        {normalText}
        <span
          onClick={() => {
            removeEmail();
            localStorage.removeItem('rehab-email');
            navigate(`${link}`, { replace: true });
          }}
          tw="font-['Inter'] ms-1 font-semibold text-Primary_600 cursor-pointer"
        >
          {linkedText}
        </span>
      </p>
    </>
  );
};

export default AuthRedirectFooter;
