import React from 'react';
import tw, { css } from 'twin.macro';
import ProfileIcon from '@assets/svgs/patient/prfile.svg';
import ClinicIcon from '../clinicIcon';

const MenuHeader = ({ profileImg, name, activeClinic }) => {
  return (
    <div
      css={tw`pt-[1rem] pb-[0.5rem] px-[0.8rem] flex flex-col justify-center items-center text-center bg-neutral_100 border-b border-stroke `}
    >
      <div
        css={[
          tw`mb-[0.5rem] w-[2.4rem] h-[2.4rem] rounded-full flex justify-center items-center`,
          profileImg &&
            css`
              background-image: url(${profileImg});
              background-size: cover;
              background-position: center;
            `,
        ]}
      >
        {!profileImg && <img src={ProfileIcon} alt="profile-icon" css={tw`w-full h-full`} />}
      </div>
      <div css={tw`w-full truncate text-[1rem] font-semibold pb-[0.4rem]`}>{name}</div>
      <p css={tw`font-medium text-[0.7rem] pb-[0.3rem]`}>Current Clinic</p>
      <div
        css={tw`w-full px-[0.4rem] py-[0.25rem] flex items-center justify-center gap-[0.4rem] text-[0.7rem] font-medium`}
      >
        <ClinicIcon clinicName={activeClinic} />
        <p css={tw`truncate max-w-[48%]`}>{activeClinic}</p>
      </div>
    </div>
  );
};

export default MenuHeader;
