import Icon1 from '@assets/svgs/settings/Calendar Icons.svg';
import PersonIcon from '@assets/svgs/settings/person.svg';
import Icon2 from '@assets/svgs/settings/solar_clipboard-add-outline.svg';
import Icon3 from '@assets/svgs/settings/solar_filter-outline.svg';
import Icon4 from '@assets/svgs/settings/solar_users-group-rounded-outline.svg';

const today = [];
//const yesterday = [];
//const older = [];
const yesterday = [
  {
    actor: '<PERSON>',
    action: 'discharged',
    target: '<PERSON>',
    details: 'from Revalidatie',
    icon: Icon1,
  },
  {
    actor: '<PERSON>',
    action: 'has been flagged as high adherence',
    target: '',
    details: 'this month',
    icon: PersonIcon,
  },
  {
    actor: '<PERSON>',
    action: 'missed 1 out of 8 sessions',
    target: '',
    details: 'this week',
    icon: Icon2,
  },
  {
    actor: '<PERSON>',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon3,
  },
  {
    actor: '<PERSON>',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon4,
  },
  {
    actor: '<PERSON>',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon4,
  },
  {
    actor: 'Alice <PERSON>',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
];

// Update the `date` field for each log
const yesterdayDate = new Date();
yesterdayDate.setDate(yesterdayDate.getDate() - 1); // Move to yesterday
yesterdayDate.setHours(0, 0, 0, 0); // Set to start of yesterday

yesterday.forEach((log, index) => {
  // Distribute logs throughout yesterday (e.g., every 2 hours)
  const updatedDate = new Date(yesterdayDate.getTime() + index * 2 * 60 * 60 * 1000);
  log.date = updatedDate.toISOString(); // Convert to ISO string
});

const older = [
  {
    actor: 'Lim Tangko',
    action: 'discharged',
    target: 'Alice Johnson',
    details: 'from Revalidatie',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'has been flagged as high adherence',
    target: '',
    details: 'this month',
    icon: Icon2,
  },
  {
    actor: 'Alice Johnson',
    action: 'missed 1 out of 8 sessions',
    target: '',
    details: 'this week',
    icon: Icon3,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon4,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon4,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
];

// Update the `date` field for each log
const oneWeekAgo = new Date(); // Get the current date
oneWeekAgo.setDate(oneWeekAgo.getDate() - 7); // Subtract 7 days to get one week ago

older.forEach((log, index) => {
  const updatedDate = new Date(oneWeekAgo.getTime() - index * 24 * 60 * 60 * 1000); // Decrement by 1 day
  log.date = updatedDate.toISOString(); // Convert to ISO string
});

export { today, yesterday, older };
