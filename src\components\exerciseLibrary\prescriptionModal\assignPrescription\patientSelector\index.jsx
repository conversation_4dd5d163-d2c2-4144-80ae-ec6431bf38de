import React, { useMemo } from 'react';
import GenericSelect from '@/components/shared/select';
import TooltipIcon from '@/components/shared/tooltipIcon';
import SearchIcon from '@assets/svgs/exercise-library/search-icon.svg';
import UserPlusIcon from '@assets/svgs/user-plus.svg';

import { ModalViews } from '../..';
import tw from 'twin.macro';

const PatientSelector = ({ control, errors, patientsOptions, setModalView }) => {
  // the fixed addPatient option
  const addPatientOption = useMemo(
    () => ({
      label: (
        <div css={tw`flex items-center gap-1`}>
          <img src={UserPlusIcon} alt="add patient icon" css={tw`w-5 h-5`} />
          <span>Add patient</span>
        </div>
      ),
      value: 'add patient',
      customClickHandler: () => {
        setModalView(ModalViews.ADD_PATIENT);
      },
    }),
    [setModalView]
  );
  const defaultOptions = useMemo(
    () => [addPatientOption, ...(patientsOptions || [])],
    [addPatientOption, patientsOptions]
  );
  const loadOptions = async inputValue => {
    const pattern = inputValue ? `*${inputValue}*` : '*';
    const res = await fetch(
      `https://api.datamuse.com/words?sp=${encodeURIComponent(pattern)}&max=10&topics=first+names`
    );
    const data = await res.json();
    const fetchedOptions = data.map(item => ({
      label: item.word.charAt(0).toUpperCase() + item.word.slice(1),
      value: Math.floor(Math.random() * 1000000),
    }));
    return [addPatientOption, ...fetchedOptions];
  };

  return (
    <div>
      <GenericSelect
        control={control}
        label={
          <>
            <p css={tw`text-black`}>Name</p>
            <TooltipIcon
              text="You can only assign to patient that don't have a currently active prescription."
              isActiveOnHover
            />
          </>
        }
        labelStyle={tw`flex items-center gap-[6px] font-medium text-[16px]`}
        name="patients"
        placeholder={
          <div css={tw`flex items-center gap-[6px]`}>
            <img src={SearchIcon} alt="search-icon" />
            <span>Patient Name</span>
          </div>
        }
        isAsync
        loadOptions={loadOptions}
        defaultOptions={defaultOptions}
        errorMessage={errors.patients ? errors.patients.message : ''}
        customStyles={{
          control: () => ({
            cursor: 'pointer',
            padding: '8px',
          }),
          placeholder: () => ({
            fontWeight: '500',
          }),
          menuList: () => ({
            maxHeight: '190px',
          }),
        }}
        inputLength={20}
        isMulti
        filterOption={(option, inputValue) => {
          // Always show addPatientOption
          if (option.data.value === 'add patient') {
            return true;
          }
          // Default react-select filtering for other options
          return option.label.toLowerCase().includes(inputValue.toLowerCase());
        }}
      />
    </div>
  );
};

export default PatientSelector;
