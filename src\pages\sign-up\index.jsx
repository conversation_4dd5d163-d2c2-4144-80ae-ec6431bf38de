import { useForm } from 'react-hook-form';
import { useEffect, useRef, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { yupResolver } from '@hookform/resolvers/yup';
import AuthInput from '@/components/auth/authInput';
import PrimaryButton from '@/components/shared/primaryButton';
import PhoneInput from '@/components/shared/phoneInput';
import AuthRecaptchaButton from '@/components/auth/authRecaptchaButton';
import AuthLayout from '@/components/auth/authLayout';
import { createFormErrorHandler } from '@/utils/form-error-handler';
import Checkbox from '@/components/shared/checkbox';
import tw from 'twin.macro';
import * as yup from 'yup';
import PhoneNumber from '@/components/phoneNumber';
import 'twin.macro';

// access demo
const initialSignUp = {
  email: '',
  firstName: '',
  lastName: '',
  clinic_name: '',
  otp: '',
  recaptchaToken: '',
  phone_ch: '20',
};

const schemaRequestDemo = yup
  .object({
    email: yup.string().email('Must be a valid email.').required('Required field.'),

    firstName: yup
      .string()
      .required('Name is required')
      .test(
        'is-valid-first-name',
        'Enter a valid name (letter s only)',
        value => !value || /^[\u0600-\u06FFa-zA-Z]+(?: [\u0600-\u06FFa-zA-Z]+){0,2}$/.test(value)
      ),

    lastName: yup
      .string()
      .required('Name is required')
      .test(
        'is-valid-last-name',
        'Enter a valid name (letters only)',
        value => !value || /^[\u0600-\u06FFa-zA-Z]+(?: [\u0600-\u06FFa-zA-Z]+){0,1}$/.test(value)
      ),

    phoneNumber: yup.string().required('Field required field.'),

    clinic_name: yup.string().required('Clinic name is required'),
  })
  .required();

const SignUp = () => {
  const navigate = useNavigate();
  const inputRef = useRef(null);
  const recaptchaRef = useRef(null);
  const [recaptchError, setRecaptchaError] = useState(false);
  const [errorMessage, setErrorMessage] = useState(false);

  // const countryCode = useRequestDemoSteps((state) => state.countryCode);
  // useFetchCountries();

  useEffect(() => {
    localStorage.removeItem('from');
    localStorage.removeItem('rehab-email');
  }, []);

  const {
    register,
    handleSubmit,
    reset,
    control,
    setError,
    formState: { errors, isSubmitting },
    watch,
  } = useForm({
    defaultValues: initialSignUp,
    resolver: yupResolver(schemaRequestDemo),
    mode: 'onBlur', // Validate on blur
  });

  const handleContinue = async data => {
    try {
      setErrorMessage('');
      setRecaptchaError(false);
      const token = await recaptchaRef.current.executeAsync();
      //   const { detail } = await AuthApis.signup(
      //     {
      //       email: data.email,
      //       firstName: data.firstName,
      //       lastName: data.lastName,
      //       phoneNumber: "+" + data.phone_ch + data.phoneNumber,
      //     },
      //     token
      //   );
      recaptchaRef.current.reset();
      // showSuccess(detail);
      localStorage.setItem('rehab-email', JSON.stringify(data.email));
      localStorage.setItem('from', JSON.stringify('signUp'));
      navigate('/otp-page');
      reset();
    } catch (error) {
      recaptchaRef.current.reset();
      if (error?.response?.data?.detail) {
        setErrorMessage(error.response.data.detail);
      }
      if (error.response.data.recaptchaToken) {
        setRecaptchaError(true);
      } else {
        // Use the error handler
        const handleError = createFormErrorHandler(setError);
        handleError(error);
      }
    }
  };

  return (
    <AuthLayout
      authSection={
        <>
          <div>
            <label tw="font-[600] text-[0.95rem] font-['Inter']">Your name</label>
            <div tw="flex gap-6 mt-2">
              <AuthInput
                name={'firstName'}
                placeholder={'First name'}
                register={register}
                errorMessage={errors.firstName?.message}
              />
              <AuthInput
                name={'lastName'}
                placeholder={'Last name'}
                register={register}
                errorMessage={errors.lastName?.message}
              />
            </div>
          </div>
          <div tw="flex gap-6 mt-2">
            <div tw="basis-[50%]">
              <AuthInput
                label={'Email address'}
                name={'email'}
                placeholder={'Email address'}
                register={register}
                errorMessage={errors.email?.message}
                type={'email'}
                inputRef={inputRef}
              />
            </div>
            <div tw="basis-[50%] h-full pt-1">
              <PhoneNumber
                control={control}
                defaultCountry={'EG'}
                label={'Phone number'}
                name={'phoneNumber'}
                phoneName={'phoneCh'}
                placeholder={'Phone number'}
                register={register}
                errors={errors?.phoneNumber?.message}
              />
            </div>
          </div>
          <AuthInput
            label={'Clinic name'}
            name={'clinic_name'}
            placeholder={'Clinic name'}
            register={register}
            errorMessage={errors.clinic_name?.message}
            inputRef={inputRef}
          />
          <Checkbox
            containerStyle={tw`px-0!`}
            label={
              <span tw="text-text_secondary text-[0.875rem] font-medium">
                Please send me occasional emails about Rehabitaire products and services. (You can{' '}
                unsubscribe anytime)
              </span>
            }
            register={register}
            name="marketingConsent"
            checked={watch('marketingConsent')}
          />

          <div tw="[line-height: 120%]">
            <span tw="font-[500] text-[0.875rem] font-['Inter']">
              By submitting my personal information, I understand and agree that Rehabitaire may
              collect, process, and retain my data pursuant to the
              <Link to="/terms" target="_blank" tw="font-['Inter'] font-[600] underline text-info">
                {' '}
                Rehabitaire Privacy Policy.
              </Link>
            </span>
          </div>
          <AuthRecaptchaButton
            control={control}
            recaptchError={recaptchError}
            recaptchaRef={recaptchaRef}
          />
          <PrimaryButton
            disable={isSubmitting}
            text={'continue'}
            tw="w-full py-[12px] px-[16px] rounded-[6px] text-[1rem] font-[500] font-['Inter']"
          />
          {errorMessage && (
            <span tw="pt-2 text-sm font-['Inter']" style={{ color: '#D12E3C' }}>
              {errorMessage}
            </span>
          )}
        </>
      }
      footerLinkText={'Let us know'}
      footerNormalText={'Facing problems?'}
      hasAuthFooter
      // isLogIn
      hasAuthRedirectFooter
      redirectLink={'/login'}
      redirectLinkText={'Log in'}
      redirectNormalText={'Already have an account?'}
      subtitle="Enter your personal information and get started!"
      title={'Sign up'}
      handelSubmit={handleSubmit(handleContinue)}
    />
  );
};

export default SignUp;
