import { useState } from 'react';
import AddTag from '../addTag';
import Tag from '../tag';
import 'twin.macro';

const ExerciseDetailsModelTags = ({
  tagsList,
  containerStyle,
  showCloseIcon,
  otherStyle,
  handelClick,
  hideName = false,
  hiedAddTags = true,
}) => {
  const [selectedTags, setSelectedTags] = useState([]);

  const options = [
    { id: 0, value: 'Region: Wrists' },
    { id: 1, value: 'Joints: Wrists' },
  ];

  return (
    <div tw="flex gap-4 items-start py-4 border-b border-[#D9D9D9]" css={containerStyle}>
      {!hideName && <p tw="font-medium text-[1rem]">Tags</p>}

      <ul tw="flex flex-wrap gap-4 items-center">
        {/* Render tags from props */}
        {tagsList.map(item => (
          <Tag
            key={item.id}
            text={item.title}
            handelClick={handelClick}
            otherStyle={otherStyle}
            showCloseIcon={showCloseIcon}
          />
        ))}

        {/* Render selected tags from AddTag component */}
        {selectedTags.map(item => (
          <Tag
            key={item.id}
            text={`${item.value}`}
            handelClick={handelClick}
            otherStyle={otherStyle}
            showCloseIcon={showCloseIcon}
          />
        ))}
        {/* The + button */}
        {!hiedAddTags && (
          <AddTag
            options={options}
            title={'wrist'}
            selectedTags={selectedTags}
            setSelectedTags={setSelectedTags}
          />
        )}
      </ul>
    </div>
  );
};

export default ExerciseDetailsModelTags;
