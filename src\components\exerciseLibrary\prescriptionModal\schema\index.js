import * as yup from 'yup';

const messageSchema = yup
  .object({
    note: yup.string().max(255, 'Maximum 255 characters.').notRequired(),
    voice: yup
      .object({
        audioUrl: yup.string().notRequired(),
        duration: yup.number().notRequired(),
        isGenerated: yup.boolean().default(false).notRequired(),
      })
      .notRequired(),
  })
  .notRequired();

//The dynamicMessagesSchema allows you to validate an object with arbitrary keys (e.g., exercise IDs), where each value is a message object.
//This is useful when you don’t know in advance how many exercises there will be or what their IDs are.
//Yup’s .lazy() lets you build the schema dynamically at validation time, so it matches the actual structure of the data.
export const dynamicMessagesSchema = yup.lazy(obj => {
  if (!obj) {
    return yup.object({}); // Return an empty object schema if obj is null/undefined
  }

  const schemaFields = Object.keys(obj).reduce((acc, key) => {
    acc[key] = messageSchema; // Apply the messageSchema to each dynamic key
    return acc;
  }, {});

  return yup.object().shape(schemaFields).notRequired();
});

export const schemaAssignPrescription = yup.object({
  patients: yup
    .array()
    .of(
      yup.object({
        label: yup.string().required('Invalid patient(s).'),
        value: yup.number().typeError('Invalid patient(s).').required('Invalid patient(s).'),
      })
    )
    .min(1, 'Required field.')
    .required('Required field.')
    .typeError('Invalid patient(s).'),
  startDate: yup.date().typeError('Invalid date.').required('Required field.'),
  generalMessage: messageSchema.notRequired(),
  exerciseMessages: dynamicMessagesSchema,
});
