const notes = {
  pateint_id: 1,
  therapist: {
    id: 1,
    name: '<PERSON>',
  },
  data: [
    {
      id: 1,
      published_date: 'Oct 12th, 2024',
      author: 'PT Princess <PERSON>',
      images: [],
      documents: [],
      content: `<p><span>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabit</span>ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.</p>`,
    },
    {
      id: 2,
      published_date: 'Oct 12th, 2024',
      author: 'PT Princess Fiona',
      images: [],
      documents: [],
      content: `<p><span>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabit</span>ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.</p>`,
    },
    {
      id: 3,
      published_date: 'Oct 12th, 2024',
      author: 'PT Princess Fiona',
      images: [],
      documents: [],
      content: `<p><span>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabit</span>ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.</p>`,
    },
    {
      id: 4,
      published_date: 'Oct 12th, 2024',
      author: 'PT Princess Fiona',
      images: [],
      documents: [],
      content: `<p><span>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabit</span>ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.</p>`,
    },
    {
      id: 5,
      published_date: 'Oct 12th, 2024',
      author: 'PT Princess Fiona',
      images: [],
      documents: [],
      content: `<p><span>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabit</span>ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.</p>`,
    },
    {
      id: 6,
      published_date: 'Oct 12th, 2024',
      author: 'PT Princess Fiona',
      images: [],
      documents: [],
      content: `<p><span>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabit</span>ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.</p>`,
    },
    {
      id: 7,
      published_date: 'Oct 12th, 2024',
      author: 'PT Princess Fiona',
      images: [],
      documents: [],
      content: `<p><span>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabit</span>ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.</p>`,
    },
    {
      id: 8,
      published_date: 'Oct 12th, 2024',
      author: 'PT Princess Fiona',
      images: [],
      documents: [],
      content: `<p><span>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabit</span>ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.</p>`,
    },
    {
      id: 9,
      published_date: 'Oct 12th, 2024',
      author: 'PT Princess Fiona',
      images: [],
      documents: [],
      content: `<p><span>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabit</span>ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.</p>`,
    },
    {
      id: 10,
      published_date: 'Oct 12th, 2024',
      author: 'PT Princess Fiona',
      images: [],
      documents: [],
      content: `<p><span>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabit</span>ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.ur maximus est sed porta scelerisque.</p>`,
    },
  ],
};

export const getAllNotesByPatientId = patient_id => {
  return notes;
};
export const createNewNote = note => {
  const newData = { ...notes, data: [note, ...notes.data] };
  console.log(note, newData);

  return newData;
};
