import closeIcon from '@assets/svgs/close-icon.svg';
import 'twin.macro';

const Tag = ({ otherStyle, text, handelClick, showCloseIcon }) => {
  return (
    <div
      tw="bg-neutral_200 rounded-[28px] px-[12px] py-[4px] font-semibold text-[0.8rem] flex items-center gap-1"
      css={otherStyle}
    >
      {text}
      {showCloseIcon && (
        <img src={closeIcon} alt="close" tw="w-[8px] cursor-pointer" onClick={handelClick} />
      )}
    </div>
  );
};
export default Tag;
