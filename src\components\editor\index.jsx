import { useEditor, EditorContent } from '@tiptap/react';

import { extensions } from './editorConfig';
import { useEffect, useRef, useState } from 'react';
import MenuBar from './menuBar';
import 'twin.macro';
import { css } from 'twin.macro';
export default function Editor({
  children,
  initialContent,
  height = 'min-h-[200px]',
  placeholder = ' <p>Your content starts here…</p>',
  handleFileUpload,
  onContentChange,
}) {
  const [linkText, setLinkText] = useState('');
  const [linkUrl, setLinkUrl] = useState('');
  const [isFocused, setIsFocused] = useState(true);

  const boxRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = event => {
      if (boxRef.current && !boxRef.current.contains(event.target)) {
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const editor = useEditor({
    extensions: extensions,
    // placeholder: placeholder,
    content: initialContent,
    autofocus: true,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      if (onContentChange) {
        onContentChange(html);
      }
    },
    editorProps: {
      attributes: {
        class: `p-4 w-full  outline-none focus-visible:outline-none focus-visible:border-0 text-sm `,
      },
    },
  });
  useEffect(() => {
    if (editor && isFocused) editor.commands.focus('end');
  }, [editor, isFocused]);
  return (
    <div className="editor-wrapper">
      <MenuBar
        editor={editor}
        linkText={linkText}
        linkUrl={linkUrl}
        setLinkUrl={setLinkUrl}
        setLinkText={setLinkText}
        handleFileUpload={handleFileUpload}
      />
      <div
        ref={boxRef}
        className={` rounded-lg overflow-hidden transition-all  ${isFocused ? 'ring-1 ring-Primary' : 'ring-1 ring-border_stroke'}`}
        onClick={e => {
          e.stopPropagation();
          setIsFocused(true);
        }}
      >
        <div className={`flex flex-col overflow-auto ${height}`}>
          {children && <div>{children}</div>}
          <div tw="flex-1">
            <EditorContent editor={editor} />
          </div>
        </div>
      </div>
    </div>
  );
}
