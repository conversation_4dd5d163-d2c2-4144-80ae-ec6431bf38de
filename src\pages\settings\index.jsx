import TabComponent from '@/components/shared/tabComponent';
import ProfileForm from '@/components/settings/profileForm';
import ClinicInfo from '@/components/settings/clinicInfo';
import Logs from '@/components/settings/logsTab';
import Billing from '@/components/settings/biling';
import Alerts from '@/components/settings/alerts';
import 'twin.macro';

const Settings = () => {
  const breadcrumbItems = [{ label: <p tw="text-[1.5rem] font-semibold"> Settings</p>, to: '' }];

  const tabsContent = [
    <ProfileForm key={0} />,
    <Logs key={2} />,
    <Alerts key={4} />,
    <Billing key={3} />,
  ];

  return (
    <div tw="h-full">
      <TabComponent
        breadcrumbItems={breadcrumbItems}
        tabsKeys={['Profile', 'Logs', 'Notifications', 'Billing', 'Permissions']}
        tabsContent={tabsContent}
      />
    </div>
  );
};

export default Settings;
