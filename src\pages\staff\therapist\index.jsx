import InsightCardList from '@/components/InsightList';
import PatientTable from '@/components/patient/patientTable';
import PrescriptionList from '@/components/prescriptionList';

import Breadcrumb from '@/components/shared/breadcrumb';
import TabComponent from '@/components/shared/tabComponent';
import { insightsData } from '@/components/staff/constant';

import TherapistProfileForm from '@/components/staff/profile/staffProfileForm';
import { getTherapistById } from '@/mock/therapists-data';
import { useEffect, useState } from 'react';

import { useParams } from 'react-router-dom';
import 'twin.macro';

const Therapist = () => {
  const { id } = useParams();
  const [therapist, setTherapist] = useState(null);

  useEffect(() => {
    const therapist = getTherapistById(1, id);
    setTherapist(therapist);
  }, [id]);

  const breadcrumbItems = [{ label: 'Staff', to: '/staff' }, { label: therapist?.Name }];

  return (
    <>
      {therapist && (
        <TabComponent
          breadcrumbItems={breadcrumbItems}
          tabsKeys={['Profile', 'Patients', 'Prescriptions', 'Logs']}
          tabsContent={[
            <div key={1} tw="flex gap-6  w-full">
              <TherapistProfileForm therapist={therapist} />
              <InsightCardList data={insightsData} />
            </div>,

            <PatientTable key={2} />,
            <PrescriptionList key={3} pov="therapist" />,
            <>Therapist {id}</>,
          ]}
        />
      )}
    </>
  );
};
export default Therapist;
