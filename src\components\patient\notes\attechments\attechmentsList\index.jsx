import docIcon from '@/assets/svgs/tiptap-editor/docIcon.svg';
import textFile from '@/assets/svgs/patient/text-file.svg';
import wordFile from '@/assets/svgs/patient/word-file.svg';
import 'twin.macro';
import tw from 'twin.macro';

const AttechmentsList = ({
  list = [],
  type = 'img', // 'img' or 'doc'
  removeFile,
  itemStyle,
  imageStyle,
  onClickOnAttechment,
}) => (
  <div tw="flex flex-wrap gap-2">
    {list.map(item => (
      <div
        key={item.previewUrl}
        css={
          type === 'img'
            ? tw`relative h-[6.25rem]  flex items-center border border-border_stroke rounded-lg`
            : tw`relative`
        }
        style={
          itemStyle ||
          (type === 'img' ? { width: 'calc(25% - .5rem)' } : { width: 'calc(25% - 8px)' })
        }
      >
        <div
          onClick={() => removeFile(item, type)}
          tw="flex absolute -top-2 -right-2 justify-center items-center w-5 h-5 text-sm text-white rounded-full cursor-pointer bg-error"
        >
          x
        </div>
        {type === 'img' ? (
          <div
            onClick={() => onClickOnAttechment(item.previewUrl)}
            tw="w-full h-full"
            // href={item.previewUrl}
            // target="_blank"
            // rel="noopener noreferrer"
          >
            <img
              src={item.previewUrl}
              alt={item.name}
              tw="w-full h-full object-contain cursor-pointer  aspect-[16/12]"
              style={imageStyle}
            />
          </div>
        ) : (
          <a
            href={item.previewUrl}
            target="_blank"
            rel="noopener noreferrer"
            tw="flex w-full gap-2 p-2 items-center cursor-pointer border border-border_stroke bg-neutral_100 rounded-[.5rem]"
          >
            <img
              src={
                item.file.type === 'application/pdf'
                  ? docIcon
                  : item.file.type === 'application/msword' ||
                      item.file.type ===
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                    ? wordFile
                    : item.file.type === 'text/plain' && textFile
              }
              alt={'doc icon'}
            />
            <p
              tw="overflow-hidden whitespace-nowrap text-ellipsis"
              style={{
                width: 'calc(100% - 1.75px - .1rem)',
              }}
            >
              {item.name}
            </p>
          </a>
        )}
      </div>
    ))}
  </div>
);

export default AttechmentsList;
