import { useMemo } from 'react';
import NameCell from '@/components/table/cells/nameCell';
import StatusCell from '@/components/table/cells/statusCell';
import NameHeader from '@/components/table/headers/nameHeader';
import { formatDate } from '@/utils/helpers';
import { TableActionTypes } from '@/reducers/table';
import PatientActions from '@/components/table/cells/nameCell/patientActions';

export function usePatientsColumns({ state, dispatch, hoverEnabledRef, handleOpen }) {
  const columnSize = 300;
  return useMemo(
    () => [
      {
        accessorKey: 'Name',
        header: () => (
          <NameHeader
            currentRows={state.currentRows}
            selectedRows={state.selectedRows}
            setSelectedRows={rows =>
              dispatch({ type: TableActionTypes.SET_SELECTED_ROWS, payload: rows })
            }
            loading={state.loading}
          />
        ),
        cell: info => (
          <NameCell
            value={info.getValue()}
            rowId={info.row.id}
            selectedRows={state.selectedRows}
            setSelectedRows={rows =>
              dispatch({ type: TableActionTypes.SET_SELECTED_ROWS, payload: rows })
            }
            hoverEnabledRef={hoverEnabledRef}
          >
            {({ handleMenuClose }) => (
              <PatientActions
                handleOpen={type => {
                  handleOpen({ type, id: info.row.id });
                  handleMenuClose();
                }}
              />
            )}
          </NameCell>
        ),
        size: columnSize,
        meta: {
          hasBorder: true,
          sticky: true,
          left: 0,
        },
      },
      {
        accessorKey: 'Status',
        header: 'Status',
        cell: info => <StatusCell value={info.getValue()} />,
        size: columnSize,
        meta: { Sorting: true },
      },
      {
        accessorKey: 'Prescription',
        header: 'Prescription',
        size: columnSize,
        meta: { Sorting: true },
      },
      {
        accessorKey: 'Overall adherence',
        header: 'Overall adherence',
        cell: info => {
          return `${info.getValue()}%`;
        },
        size: columnSize,
        meta: {
          Sorting: true,
          Tooltip:
            'Total percentage of scheduled sessions completed by a patient since the beginning of their prescription.',
        },
      },
      {
        accessorKey: 'Adherence',
        header: 'Adherence',
        cell: info => {
          return `${info.getValue()}%`;
        },
        size: columnSize,
        meta: {
          Sorting: true,
          Tooltip: 'Percentage of scheduled sessions a patient completes over the last two weeks',
        },
      },
      {
        accessorKey: 'Pain level',
        header: 'Pain level',
        size: columnSize,
        meta: {
          Sorting: true,
          Tooltip:
            'This column in the table reflects the reported pain levels, aiding in the assessment of treatment effectiveness and necessary adjustments.',
        },
      },
      {
        accessorKey: 'Assigned PT',
        header: 'Assigned PT',
        size: columnSize,
        meta: { Sorting: true },
      },
      {
        accessorKey: 'Email address',
        header: 'Email address',
        size: columnSize,
      },
      {
        accessorKey: 'Date joined',
        header: 'Date joined',
        cell: info => formatDate(info.getValue(), state.dateFormat),
        size: columnSize,
        meta: { Sorting: true },
      },
      {
        accessorKey: 'Last visit',
        header: 'Last visit',
        cell: info => formatDate(info.getValue(), state.dateFormat),
        size: columnSize,
        meta: { Sorting: true },
      },
      {
        accessorKey: 'Overall period',
        header: 'Overall period',
        size: columnSize,
        meta: { Sorting: true },
      },
      {
        accessorKey: 'Time left',
        header: 'Time left',
        size: columnSize,
        meta: { Sorting: true },
      },
      {
        accessorKey: 'Expiration date',
        header: 'Expiration date',
        cell: info => formatDate(info.getValue(), state.dateFormat),
        size: columnSize,
        meta: { Sorting: true },
      },
    ],
    [
      dispatch,
      hoverEnabledRef,
      state.currentRows,
      state.dateFormat,
      state.loading,
      state.selectedRows,
    ]
  );
}
