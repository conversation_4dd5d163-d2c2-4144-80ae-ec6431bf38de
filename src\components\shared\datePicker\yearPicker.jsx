/* eslint-disable react-refresh/only-export-components */
import { useState, useRef, useEffect } from "react";
import ActionButtons from "./actionButton";
import YearsList from "./yearList";
import { styles } from "./datePicker";
import "twin.macro";

// Calendar Icon Component
const CalendarIcon = ({ iconColor }) => (
  <svg
    tw="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 4H4C3.45 4 3 4.45 3 5V13C3 13.55 3.45 14 4 14H12C12.55 14 13 13.55 13 13V5C13 4.45 12.55 4 12 4Z"
      stroke={iconColor || styles.primaryColor}
      strokeWidth="1.5"
    />
    <path
      d="M11 2V6"
      stroke={iconColor || styles.primaryColor}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M5 2V6"
      stroke={iconColor || styles.primaryColor}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M3 8H13"
      stroke={iconColor || styles.primaryColor}
      strokeWidth="1.5"
    />
  </svg>
);

// Year Picker Component
const YearPicker = ({
  // Base props
  onChange,
  value,
  label,

  // Customization props
  className,
  inputClassName,
  pickerClassName,
  labelClassName,
  primaryColor,
  secondaryColor,
  borderColor,
  borderRadius,
  iconColor,

  // Behavior props
  yearRange = { start: 1900, end: new Date().getFullYear() },

  // Format props
  locale = "en-US",

  // Placeholder & Label Text
  placeholder = "Select Year",
  applyButtonText = "Apply",
  cancelButtonText = "Cancel",

  // Width options
  inputWidth = "100%",
  pickerWidth = "100%",

  // Calendar display settings
  closeOnSelect = false,

  // Custom handlers
  onCancel,

  // Parent controlled state
  isOpen: isOpenFromProps,
  onOpenChange,
}) => {
  // Compute custom colors for the component
  const customColors = {
    primaryColor: primaryColor || styles.primaryColor,
    secondaryColor: secondaryColor || styles.secondaryColor,
    borderColor: borderColor || styles.borderColor,
    borderRadius: borderRadius || styles.borderRadius,
  };

  // State Management
  const [selectedDate, setSelectedDate] = useState(value || new Date());
  const [isPickerOpen, setIsPickerOpen] = useState(isOpenFromProps || false);

  // Update internal state when controlled from parent
  useEffect(() => {
    if (isOpenFromProps !== undefined) {
      setIsPickerOpen(isOpenFromProps);
    }
  }, [isOpenFromProps]);

  // Update selected date when value changes
  useEffect(() => {
    if (value) {
      setSelectedDate(value);
    }
  }, [value]);

  // Refs for click-outside detection
  const pickerRef = useRef(null);
  const inputRef = useRef(null);

  // Generate years based on range
  const years = Array.from(
    { length: yearRange.end - yearRange.start + 1 },
    (_, i) => yearRange.start + i
  ).reverse();

  // Toggle picker visibility
  const togglePicker = () => {
    const newIsOpen = !isPickerOpen;
    setIsPickerOpen(newIsOpen);

    if (onOpenChange) {
      onOpenChange(newIsOpen);
    }
  };

  // Format date for input display - only show year
  const formatDateForInput = (date) => {
    if (!date) return "";
    return date.toLocaleDateString(locale, { year: "numeric" });
  };

  // Navigate to a specific year
  const navigateToYear = (year) => {
    // Update the date with the selected year
    const updatedDate = new Date(selectedDate);
    updatedDate.setFullYear(year);
    setSelectedDate(updatedDate);

    // Close picker if closeOnSelect is true
    if (closeOnSelect) {
      if (onChange) {
        onChange(updatedDate);
      }
      setIsPickerOpen(false);
      if (onOpenChange) {
        onOpenChange(false);
      }
    }
  };

  // Handle Apply button
  const handleApply = () => {
    // Apply the selected year
    const finalDate = selectedDate;
    if (onChange) {
      onChange(finalDate);
    }
    setIsPickerOpen(false);
    if (onOpenChange) {
      onOpenChange(false);
    }
  };

  // Handle Cancel button
  const handleCancel = () => {
    // Close the picker without updating the value
    setIsPickerOpen(false);
    if (onOpenChange) {
      onOpenChange(false);
    }

    // Call custom onCancel if provided
    if (onCancel) {
      onCancel();
    }
  };

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target) &&
        inputRef.current &&
        !inputRef.current.contains(event.target)
      ) {
        setIsPickerOpen(false);
        if (onOpenChange) {
          onOpenChange(false);
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onOpenChange]);

  const actionButtonsComponent = (
    <ActionButtons
      onApply={handleApply}
      onCancel={handleCancel}
      applyButtonText={applyButtonText}
      cancelButtonText={cancelButtonText}
      primaryColor={customColors.primaryColor}
      secondaryColor={customColors.secondaryColor}
      borderColor={customColors.borderColor}
    />
  );

  return (
    <div tw="relative" className={className}>
      {label && (
        <label
          tw="block text-[14px] font-medium text-gray-700 mb-1"
          className={labelClassName}
        >
          {label}
        </label>
      )}

      <div tw="relative">
        <input
          ref={inputRef}
          type="text"
          tw="w-full py-[10px] px-[12px] border rounded-[6px] cursor-pointer"
          css={{
            borderColor: customColors.borderColor,
            borderRadius: customColors.borderRadius,
            width: inputWidth,
          }}
          className={inputClassName}
          placeholder={placeholder}
          value={formatDateForInput(selectedDate)}
          readOnly
          onClick={togglePicker}
        />
        <CalendarIcon iconColor={iconColor} />
      </div>

      {isPickerOpen && (
        <div
          ref={pickerRef}
          tw="absolute z-[100] mt-1"
          style={{ width: pickerWidth }}
          className={pickerClassName}
        >
          <div
            tw="w-full [box-shadow: 0 4px 6.8px 0 #0000000d] border rounded-[12px] overflow-hidden border-text_secondary bg-white relative"
            css={{ borderColor: customColors.borderColor }}
          >
            <YearsList
              years={years}
              activeStartDate={selectedDate}
              navigateToYear={navigateToYear}
              setShowYearsList={() => {}} // No-op since we always show years
              selectedDate={selectedDate}
              actionButtons={actionButtonsComponent}
              primaryColor={customColors.primaryColor}
              borderColor={customColors.borderColor}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default YearPicker;
