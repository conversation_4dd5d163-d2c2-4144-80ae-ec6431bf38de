import SmallLogo from '@assets/svgs/small-logo.svg';
import Library from '@assets/svgs/library-icon.svg';
import ActiveLibrary from '@assets/svgs/sidebar/library-in-active.svg';
import outLineStuffIcon from '@assets/svgs/sidebar/stuff-in-active.svg';
import outLinePatientIcon from '@assets/svgs/sidebar/paitient-in-active.svg';
import outLineInsightsIcon from '@assets/svgs/sidebar/insights-inactive.svg';
import outLineSettingsIcon from '@assets/svgs/sidebar/settings-in-active.svg';
import outLineHelpIcon from '@assets/svgs/sidebar/help-inactive.svg';
import ActivePatientIcon from '@assets/svgs/sidebar/paitients-active.svg';
import ActiveStuffIcon from '@assets/svgs/sidebar/stuff-active.svg';
import ActiveInsightIcon from '@assets/svgs/sidebar/insights-active.svg';
import ActiveSettingsIcon from '@assets/svgs/sidebar/setting-active.svg';
import ActiveHelpIcon from '@assets/svgs/sidebar/question.svg';
import Logo from '@assets/svgs/logo.svg';
import CollspseIcon from '@assets/svgs/collapse-icon.svg';
import LayoutSection from '@/components/layoutSection';
import { BrowserRouter, Routes, Route, Navigate, useLocation, Link } from 'react-router-dom';
import { cssTransition, ToastContainer } from 'react-toastify';
import PrivateRoute from './components/privateRoute';
import useRouter from '@/hooks/useRoutes';
import { useSidebarCollapse } from './zustand/sidebar-collapse';
import 'twin.macro';

const AppRoutes = () => {
  const { isCollapsed, setIsCollapsed } = useSidebarCollapse();
  const location = useLocation();
  const { privateRoute, puplicRoute, sherdRoute } = useRouter();

  const sidebarList = [
    {
      id: 0,
      title: 'Exercise library',
      icon: location.pathname === '/exercise-library' ? Library : ActiveLibrary,
      route: '/exercise-library',
    },
    {
      id: 1,
      title: 'Patient',
      icon: location.pathname === '/patients' ? ActivePatientIcon : outLinePatientIcon,
      route: '/patients',
    },
    {
      id: 2,
      title: 'Staff',
      icon: location.pathname === '/staff' ? ActiveStuffIcon : outLineStuffIcon,
      route: '/staff',
    },
    {
      id: 3,
      title: 'Insights',
      icon: location.pathname === '/insights' ? ActiveInsightIcon : outLineInsightsIcon,
      route: '/insights',
    },
    {
      id: 4,
      title: 'Settings',
      icon: location.pathname === '/settings' ? ActiveSettingsIcon : outLineSettingsIcon,
      route: '/settings',
    },
    {
      id: 5,
      title: 'Help',
      icon: location.pathname === '/help' ? ActiveHelpIcon : outLineHelpIcon,
      route: '/help',
    },
  ];

  return (
    <Routes>
      {puplicRoute.map(route => (
        <Route path={route.route} element={route.element} key={route.route} />
      ))}
      {sherdRoute.map(item => (
        <Route element={item.element} key={item.route} path={item.route} />
      ))}
      {/* Main routes with sidebar layout */}
      <Route
        path="/"
        element={
          <LayoutSection
            CollapseIcon={CollspseIcon}
            Logo={Logo}
            sidebarList={sidebarList}
            isCollapsed={isCollapsed}
            setIsCollapsed={setIsCollapsed}
            SmallLogo={SmallLogo}
            currentRoute={location.pathname}
            footerSection={
              <div tw="flex flex-col gap-2 text-white">
                <Link tw="text-[0.75rem]" to="/">
                  Terms & Conditions
                </Link>
                <Link tw="text-[0.75rem]" to="/">
                  Privacy Policy
                </Link>
                <p tw="text-[0.75rem] mt-1">© Copyright 2025 Rehabitaire.</p>
              </div>
            }
          />
        }
      >
        <Route element={<PrivateRoute />}>
          <Route index element={<Navigate to="/exercise-library" replace />} />
          {privateRoute.map(route => (
            <Route key={route.route} path={route.route} element={route.element}>
              {route.children &&
                route.children.map(route => (
                  <Route key={route.route} path={route.route} element={route.element} />
                ))}
            </Route>
          ))}
        </Route>
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Route>
    </Routes>
  );
};

function App() {
  // Define fade transition
  const Fade = cssTransition({
    enter: 'fadeIn', // Uses CSS class `.fadeIn`
    exit: 'fadeOut', // Uses CSS class `.fadeOut`
    duration: [3, 3], // [enter duration, exit duration]
  });

  return (
    <BrowserRouter>
      <ToastContainer transition={Fade} autoClose={3000} />
      <div tw="w-screen h-screen">
        <AppRoutes />
      </div>
    </BrowserRouter>
  );
}

export default App;
