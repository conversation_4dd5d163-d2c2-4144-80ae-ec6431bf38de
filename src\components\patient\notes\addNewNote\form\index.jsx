import Editor from '@/components/editor';

import 'twin.macro';
import Attechments from '../../attechments';
import { showError } from '@/libs/react.toastify';
const NoteForm = ({
  setSelectedImage,
  setContent,
  setDocuments,
  setImages,
  content,
  documents,
  images,
}) => {
  const removeFile = (file, type) => {
    const updatedFiles =
      type === 'doc'
        ? documents.filter(doc => doc.previewUrl !== file.previewUrl)
        : images.filter(img => img.previewUrl !== file.previewUrl);

    if (type === 'doc') setDocuments(updatedFiles);
    else setImages(updatedFiles);
  };

  const uploadAttachment = e => {
    const input = e.target;
    const files = Array.from(input.files);
    const imageFiles = [];
    const docFiles = [];
    const invalidFiles = [];

    const allowedTypes = [
      /^image\//,
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      // 'application/vnd.ms-excel',
      // 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      // 'application/vnd.ms-powerpoint',
      // 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
    ];

    // Combine existing files in state
    const existing = [...images.map(i => i.file), ...documents.map(d => d.file)];

    files.forEach(file => {
      // 1️⃣ Validate file type
      const isValid = allowedTypes.some(type =>
        type instanceof RegExp ? type.test(file.type) : file.type === type
      );
      if (!isValid) {
        invalidFiles.push(file.name);
        return;
      }

      // 2️⃣ Prevent duplicates: match by name + size
      const isDup = existing.some(f => f.name === file.name && f.size === file.size);
      if (isDup) {
        console.warn(`Duplicate ignored: ${file.name}`);
        return;
      }

      // 3️⃣ Prepare preview and categorize
      const previewUrl = URL.createObjectURL(file);
      const fileData = { file, previewUrl, name: file.name };

      if (file.type.startsWith('image/')) {
        imageFiles.push(fileData);
      } else {
        docFiles.push(fileData);
      }
    });

    // 4️⃣ Notify about invalid files
    if (invalidFiles.length > 0) {
      showError(`Unsupported file type:\n${invalidFiles.join('\n')}`);
    }

    // 5️⃣ Add valid non-duplicates to state
    setImages(prev => [...prev, ...imageFiles]);
    setDocuments(prev => [...prev, ...docFiles]);

    // 6️⃣ Reset input to allow re-upload of same file later
    input.value = null;
  };

  const handleContentChange = e => {
    setContent(e);
  };

  return (
    <div tw=" p-6 max-h-[25rem] ">
      <Editor
        handleFileUpload={uploadAttachment}
        onContentChange={handleContentChange}
        placeholder="Start typing here..."
        height={'h-[15.625rem] '}
        initialContent={content}
      >
        {(documents.length > 0 || images.length > 0) && (
          <Attechments
            images={images}
            documents={documents}
            removeFile={removeFile}
            setSelectedImage={setSelectedImage}
          />
        )}
      </Editor>
    </div>
  );
};

export default NoteForm;
