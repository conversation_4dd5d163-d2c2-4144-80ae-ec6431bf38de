import TooltipIcon from '../shared/tooltipIcon';
import 'twin.macro';

const LabelWithTooltip = ({
  label,
  tooltipText,
  hasTooltip = false,
  labelStyle,
  containerStyle,
  toolTipActiveOnHover,
}) => {
  return (
    <div tw="flex gap-2 items-center mb-1" css={containerStyle}>
      <span tw="font-[500] text-[0.95rem] font-['Inter']" css={labelStyle}>
        {label}
      </span>
      {hasTooltip && <TooltipIcon text={tooltipText} isActiveOnHover={toolTipActiveOnHover} />}
    </div>
  );
};

export default LabelWithTooltip;
