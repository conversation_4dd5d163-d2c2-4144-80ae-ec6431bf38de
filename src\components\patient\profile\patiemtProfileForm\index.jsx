import { getPatientById } from '@/mock/patients-data';
import { useForm } from 'react-hook-form';
import 'twin.macro';
import tw from 'twin.macro';
import { useReducer } from 'react';
import PrimaryButton from '@/components/shared/primaryButton';
import { initialPetientData } from './module';
import { patientValidationSchema } from './schema';
import { yupResolver } from '@hookform/resolvers/yup';
import { initialState, reducer } from '@/reducers/patient-profile';
import { fieldsThPermistions, isSubmit } from '../../constant';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '@tailwind';
import SectionCards from '@/components/sectionsCard';
import PatientInformation from './patientInformation';
import PatientClinicInformation from './patientClinicInformation';
import PatientFeedBack from './patientFeedback';

const fullConfig = resolveConfig(tailwindConfig);
const { text_secondary } = fullConfig.theme.colors;
const PatientProfileForm = ({ id }) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const patientData = getPatientById(Number(id));

  const defaultValues = initialPetientData({
    ...patientData,
    medical_conditions:
      'Costochondritis - Inflammation of the cartilage that connects ribs to the sternum',
    pain_areas: 'Neck - Back',
  });

  const {
    register,
    control,
    getValues,
    setValue,
    formState: { errors, isValid, isDirty, touchedFields },
    trigger,
    handleSubmit,
  } = useForm({
    defaultValues,
    resolver: yupResolver(patientValidationSchema),
    mode: 'onChange',
  });

  const formData = getValues();

  const onSubmit = obj => {
    // console.log('Submit Object=> ', obj, isValid);
  };

  return (
    <SectionCards customStyle={tw`w-[55%]`}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <PatientInformation
          {...{
            dispatch,
            patientData,
            formData,
            setValue,
            register,
            trigger,
            errors,
            fieldsThPermistions,
            state,
            id,
          }}
        />
        <PatientClinicInformation
          {...{
            control,
            register,
            errors,
            touchedFields,
            formData,
            fieldsThPermistions,
            text_secondary,
          }}
        />

        <PatientFeedBack {...{ register, trigger, errors, fieldsThPermistions }} />

        {/* SUBMIT BUTTON */}
        {isSubmit && (
          <div tw="flex justify-end px-6 py-5">
            <PrimaryButton
              text="Save Changes"
              type="submit"
              disable={!isValid || (formData.image_file === defaultValues.image_file && !isDirty)}
              customStyle={tw` !py-2.5 !rounded-[0.375rem] !font-medium`}
            />
          </div>
        )}
      </form>
    </SectionCards>
  );
};

export default PatientProfileForm;
