// import ClearAllIcon from '@assets/svgs/exercise-library/clear.svg';
// import DisableClearAllIcon from '@assets/svgs/exercise-library/disable-truch.svg';
// import tw from 'twin.macro';

// const ClearButton = ({ handelClearAll, disable }) => {
//   return (
//     <button
//       onClick={handelClearAll}
//       css={
//         disable
//           ? tw`bg-neutral_300`
//           : tw`bg-error_50 hover:(text-white bg-error) transition-all duration-300`
//       }
//       tw="rounded-[6px] px-[10px] py-[4px] flex items-center gap-1"
//     >
//       <img src={disable ? DisableClearAllIcon : ClearAllIcon} alt="clear" />
//       <span css={disable ? tw`opacity-50` : tw`text-error`} tw="text-[0.8rem]  font-medium">
//         Clear All
//       </span>
//     </button>
//   );
// };

// export default ClearButton;

/** @jsxImportSource @emotion/react */
import { useState } from 'react';
import ClearAllIcon from '@assets/svgs/exercise-library/clear.svg';
import DisableClearAllIcon from '@assets/svgs/exercise-library/disable-truch.svg';
import tw from 'twin.macro';

const ClearButton = ({ handelClearAll, disable }) => {
  const [hovered, setHovered] = useState(false);

  const isDisabled = disable;

  const icon = isDisabled ? DisableClearAllIcon : ClearAllIcon;

  const iconStyles = [
    tw`w-4 h-4`, // size
    !isDisabled && hovered && tw`text-white`, // white on hover if not disabled
  ];

  return (
    <button
      onClick={handelClearAll}
      disabled={isDisabled}
      css={[
        tw`rounded-[6px] px-[10px] py-[4px] flex items-center gap-1 transition-all duration-300`,
        isDisabled ? tw`bg-neutral_300 ` : tw`bg-error_50 hover:(bg-error)`,
      ]}
      className="group"
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      <img
        src={icon}
        alt="clear"
        css={[
          tw`w-4 h-4`,
          !isDisabled && hovered && tw`filter brightness-0 invert`, // for white color if SVG is not colorable
        ]}
      />
      <span
        css={[tw`text-[0.8rem] font-medium`, isDisabled ? tw`opacity-50` : tw`text-error`]}
        className={isDisabled ? '' : 'group-hover:text-white'}
      >
        Clear All
      </span>
    </button>
  );
};

export default ClearButton;
