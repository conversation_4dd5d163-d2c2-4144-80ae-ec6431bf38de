import { CardNumberElement, IbanElement, useElements, useStripe } from '@stripe/react-stripe-js';
import { useState } from 'react';
import CheckoutSection from '../checkoutSection';
import PaymentDetails from '../paymentDetails';
import 'twin.macro';

const PaymentForm = ({
  setOPen,
  watch,
  handleSubmit,
  setValue,
  remove,
  register,
  getValues,
  fields,
  control,
  append,
  memberCount,
  totalValue,
  subtotal,
  errors,
  IncreaseValue,
  setSelectedPlan,
  selectedPlan,
  setError,
  clearErrors,
}) => {
  const [message, setMessage] = useState('');
  const [isApplying, setIsApplying] = useState(false);
  const listOfRules = [
    { id: 0, title: 'Free 14-day trial, cancel any time' },
    { id: 1, title: "We'll email you 2 days before your trial ends" },
    { id: 2, title: "After the trial ends, you'll be charged" },
  ];

  const handelApply = () => {
    setIsApplying(true);

    setTimeout(() => {
      setIsApplying(false);
    }, 4000);
  };

  const handelPaymentDone = async data => {
    try {
      clearErrors();
      let hasErrors = false;

      // Validate member emails
      if (data.members && data.members.length > 0) {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        data.members.forEach((member, index) => {
          if (!member.email) {
            setError(`members.${index}.email`, {
              type: 'manual',
              message: 'Email is required',
            });
            hasErrors = true;
          } else if (!emailRegex.test(member.email.trim())) {
            setError(`members.${index}.email`, {
              type: 'manual',
              message: 'Please enter a valid email address',
            });
            hasErrors = true;
          }
        });
      }

      if (watch('paymentMethod') === 'card') {
        if (!data.paymentLastName) {
          setError('paymentLastName', {
            type: 'manual',
            message: 'Required field.',
          });
          hasErrors = true;
        }
        if (!data.paymentFirstName) {
          setError('paymentFirstName', {
            type: 'manual',
            message: 'Required field.',
          });
          hasErrors = true;
        }
      } else if (watch('paymentMethod') === 'sepa') {
        if (watch('PayAs') === 'individual') {
          if (!data.paymentLastName) {
            setError('paymentFirstName', {
              type: 'manual',
              message: 'Required field.',
            });
            hasErrors = true;
          }
          if (!data.paymentFirstName) {
            setError('paymentLastName', {
              type: 'manual',
              message: 'Required field.',
            });
            hasErrors = true;
          }
        } else {
          if (!data.companyName) {
            setError('companyName', {
              type: 'manual',
              message: 'Required field.',
            });
            hasErrors = true;
          }
        }
      }

      if (hasErrors) {
        return;
      }
      if (!stripe || !elements) return;
      try {
        let paymentMethodResult;

        if (watch('paymentMethod') === 'card') {
          const card = elements.getElement(CardNumberElement);
          paymentMethodResult = await stripe.createPaymentMethod({
            type: 'card',
            card,
          });
        } else if (watch('paymentMethod') === 'sepa') {
          const iban = elements.getElement(IbanElement);
          paymentMethodResult = await stripe.createPaymentMethod({
            type: 'sepa_debit',
            sepa_debit: iban,
            billing_details: {
              name: `${data.paymentFirstName} ${data.paymentLastName}`,
            },
          });
        }

        if (paymentMethodResult.error) {
          setMessage(paymentMethodResult.error.message);
        } else {
          // Send paymentMethod.id and user details to your backend
          const response = await fetch('/api/payment', {
            method: 'POST',
            body: JSON.stringify({
              payment_method: paymentMethodResult.paymentMethod.id,
              paymentFirstName: data.firstName,
              lastName: data.paymentLastName,
            }),
            headers: {
              'Content-Type': 'application/json',
            },
          });

          const result = await response.json();

          if (result.error) {
            setMessage(result.error);
          } else {
            setMessage('Payment successful!');
          }
        }
      } catch (error) {
        setMessage(error.message);
      }
    } catch (error) {
      console.log('asd', error);
    }
  };
  return (
    <form tw="flex" onSubmit={handleSubmit(handelPaymentDone)}>
      <CheckoutSection
        append={append}
        control={control}
        fields={fields}
        getValues={getValues}
        register={register}
        remove={remove}
        selectedPlan={selectedPlan}
        setSelectedPlan={setSelectedPlan}
        setValue={setValue}
        watch={watch}
        message={message}
        errors={errors}
        setError={setError}
        clearErrors={clearErrors}
      />
      <PaymentDetails
        IncreaseValue={IncreaseValue}
        listOfRules={listOfRules}
        register={register}
        selectedPlan={selectedPlan}
        subtotal={subtotal}
        totalValue={totalValue}
        watch={watch}
        isApplying={isApplying}
        handelApply={handelApply}
        setOPen={setOPen}
        memberCount={memberCount}
      />
    </form>
  );
};

export default PaymentForm;
