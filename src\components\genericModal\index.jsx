import tw from 'twin.macro';
import NormalModal from '../shared/normalModal';
import PrimaryButton from '../shared/primaryButton';
import SecondaryButton from '../shared/secondaryButton';
import 'twin.macro';

const GenericModal = ({
  typeOfPrimaryButton,
  PrimaryButtonText,
  clickOnPrimaryButton,
  secondaryButtonText,
  typeOfSecondaryButton,
  clickOnSecondaryButton,
  handelFormSubmit,
  handelCloseMode,
  openModel,
  backgroundModal,
  containerModalStyle,
  modalStyle,
  containerButtonsStyle,
  PrimaryButtonStyle,
  SecondaryButtonStyle,
  title,
  hasPrimaryButton = true,
  hasSecondaryButton = true,
  content,
  handelCLick,
  hasDeleteButton,
  deleteText,
  customDeleteButtonStyle,
  titleStyle,
  contetnContainerStyle,
  element: Element = 'form',
  PrimaryButtonDisable,
  disableSecondaryButton,
}) => {
  return (
    <NormalModal
      backgroundModal={backgroundModal}
      open={openModel}
      modalStyle={modalStyle}
      containerModalStyle={[tw`h-fit`, containerModalStyle]}
      titleStyle={titleStyle}
      title={title}
      handleClose={handelCloseMode}
      content={
        <Element {...(handelFormSubmit ? { onSubmit: handelFormSubmit } : {})}>
          <div tw="max-h-[70vh]" css={contetnContainerStyle}>
            {content}
          </div>
          <div
            css={containerButtonsStyle}
            tw="flex sticky bottom-0 left-0 gap-4 w-full px-[16px] py-[14px] bg-neutral_50 border border-[#D9D9D9]"
          >
            {hasDeleteButton && (
              <PrimaryButton
                text={deleteText}
                type="button"
                onClick={handelCLick}
                customStyle={[
                  tw`flex-1 !py-[10px] !rounded-[6px] !font-medium bg-error_50 text-error border-error hover:bg-error_50`,
                  customDeleteButtonStyle,
                ]}
              />
            )}
            {hasPrimaryButton && (
              <PrimaryButton
                type={typeOfPrimaryButton}
                text={PrimaryButtonText}
                disable={PrimaryButtonDisable}
                customStyle={PrimaryButtonStyle}
                handleClick={clickOnPrimaryButton}
              />
            )}
            {hasSecondaryButton && (
              <SecondaryButton
                text={secondaryButtonText}
                type={typeOfSecondaryButton}
                otherStyle={SecondaryButtonStyle}
                handelClick={clickOnSecondaryButton}
                disable={disableSecondaryButton}
              />
            )}
          </div>
        </Element>
      }
    />
  );
};

export default GenericModal;
