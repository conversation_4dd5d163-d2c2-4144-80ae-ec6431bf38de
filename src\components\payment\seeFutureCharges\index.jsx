import Model from '@/components/shared/model';
import MemberSubscription from '../memberSubscription';
import DIvider from '../divider';
import SubTotal from '../subtotalItem';
import TotalItem from '../totalItem';
import DateIcon from '@assets/svgs/payment/date-icon.svg';
import Close from '@assets/svgs/close-icon.svg';
import tw from 'twin.macro';

const SeeFutureCharges = ({ IncreaseValue, selectedPlan, totalValue, open, handelCloseModel }) => {
  return (
    <Model
      open={open}
      children={
        <div tw="bg-white w-[40vw] rounded-[12px] [box-shadow: 0 2px 4px 0 rgba(0,0,0,0.1)] overflow-hidden">
          <div tw="flex justify-between items-center py-[32px] px-[48px] border-b-2 border-stroke">
            <h3 tw="font-bold text-[1.5rem]">See Future Charges</h3>
            <img tw="cursor-pointer" src={Close} alt="close" onClick={handelCloseModel} />
          </div>
          <div tw="px-[48px]  pt-[20px] py-[48px] grid gap-[24px]">
            <div tw="bg-Primary_50 rounded-[10px] py-[24px] px-[24px]  grid h-full gap-3">
              <MemberSubscription
                IncreaseValue={IncreaseValue}
                title="Member subscription"
                hasSubtitle
                // containerStyle={tw`py-[8px]`}
              />
              <DIvider />

              <SubTotal
                subtotal={selectedPlan.badge}
                title="Subtotal (1 member)"
                // containerStyle={tw`py-[8px]`}
              />
              <DIvider />
              <div tw="flex gap-4 items-center">
                <img src={DateIcon} alt="Date" />
                <TotalItem
                  contentStyle={tw`w-full`}
                  title="On July 1, 2025"
                  totalValue={totalValue}
                />
              </div>
            </div>
            <DIvider />
            <div tw="bg-Primary_50 rounded-[10px] py-[20px] px-[20px]  grid h-full gap-3">
              <MemberSubscription
                IncreaseValue={IncreaseValue}
                title="Member subscription"
                hasSubtitle
                // containerStyle={tw`py-[8px]`}
              />
              <DIvider />

              <SubTotal
                subtotal={selectedPlan.badge}
                title="Subtotal (1 member)"
                // containerStyle={tw`py-[8px]`}
              />
              <DIvider />
              <div tw="flex gap-4 items-center">
                <img src={DateIcon} alt="Date" />
                <TotalItem
                  contentStyle={tw`w-full`}
                  title="On July 1, 2025"
                  totalValue={totalValue}
                />
              </div>
            </div>
          </div>
        </div>
      }
    />
  );
};

export default SeeFutureCharges;
