import PrimaryButton from '@/components/shared/primaryButton';
import Logo from '@/assets/svgs/auth/black-logo.svg';
import tw from 'twin.macro';

const PaymentNavbar = () => {
  return (
    <nav tw="flex justify-between items-center w-screen px-[7%] py-[1.4%]">
      <img src={Logo} alt="logo" />
      <PrimaryButton
        text={'Sign Out'}
        customStyle={tw`py-[14px] px-[48px] border border-Primary_600 rounded-[6px] text-[0.9rem]!`}
      />
    </nav>
  );
};

export default PaymentNavbar;
