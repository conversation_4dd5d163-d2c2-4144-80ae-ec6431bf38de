import { AnimatePresence, motion } from 'framer-motion';
import ArrowIcon from '@assets/svgs/auth/left-arrow.svg';
import TimelineItem from './timelineItem';
import TimeLineEmpty from './timeLineEmpty';
import TimeLineLoading from './timeLineLoading';
import 'twin.macro';
import tw from 'twin.macro';

const Timeline = ({ label, useStore, emptyLabel, emptySubtitle }) => {
  const { items, head, hasMore, handleShowMore, handleShowLess, loading, step } = useStore();

  const logsToShow = items.slice(0, head);

  if (loading && logsToShow.length === 0) {
    return <TimeLineLoading label={label} length={step} />;
  }

  if (!logsToShow || logsToShow.length === 0) {
    return <TimeLineEmpty label={emptyLabel} subtitle={emptySubtitle} />;
  }

  return (
    <>
      <p tw="font-medium text-text_secondary text-[0.75rem] mb-[0.8rem] opacity-80">{label}</p>
      <AnimatePresence initial={false}>
        {logsToShow.map((item, idx) => (
          <motion.div
            key={idx}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.3 }}
          >
            <TimelineItem
              item={item}
              isLast={idx === logsToShow.length - 1}
              isOlder={label === 'Older'}
            />
          </motion.div>
        ))}
      </AnimatePresence>
      <div tw="flex justify-center mt-2">
        {loading ? (
          <p tw="text-Primary_600 text-[0.875rem] cursor-default">Loading...</p>
        ) : hasMore || logsToShow.length < items.length ? (
          <button
            type="button"
            tw="flex gap-1 items-center font-normal text-Primary_600 text-[0.875rem]"
            onClick={handleShowMore}
          >
            Show More
            <img src={ArrowIcon} alt="Show" css={[tw`w-[0.8rem] h-[0.8rem]`, tw`rotate-90`]} />
          </button>
        ) : (
          logsToShow.length > step && (
            <button
              type="button"
              tw="flex gap-1 items-center font-normal text-Primary_600 text-[0.875rem]"
              onClick={handleShowLess}
            >
              Show Less
              <img src={ArrowIcon} alt="Show" css={[tw`w-[0.8rem] h-[0.8rem]`, tw`-rotate-90`]} />
            </button>
          )
        )}
      </div>
    </>
  );
};

export default Timeline;
