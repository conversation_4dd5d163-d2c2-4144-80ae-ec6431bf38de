import React, { useState } from 'react';
import AddPatient from '@/components/addPatientForm';
import GenericModal from '@/components/genericModal';
import ModalTitle from '@/components/modalTitle';
import tw from 'twin.macro';
import 'twin.macro';
import checkCircle from '@/assets/svgs/patient/check-circle.svg';
import ActionModal from '@/components/actionModal';

import AddButton from '@/components/addButton';
function AddPatientModal() {
  const [openAddPatientModal, setOpenAddPatientModal] = useState(false);
  const [patientName, setPatientName] = useState('');
  return (
    <>
      <AddButton text={'Add patient'} onClick={() => setOpenAddPatientModal(true)} />

      {openAddPatientModal && (
        <GenericModal
          openModel={openAddPatientModal}
          handelCloseMode={() => setOpenAddPatientModal(false)}
          containerModalStyle={[
            tw`!w-[50rem] md:!min-w-0 !min-w-0 !rounded-xl h-fit shadow-[0rem_0.3125rem_0.9375rem_0rem_rgba(0,0,0,0.08),0rem_0.9375rem_2.1875rem_-0.3125rem_rgba(17,24,38,0.2),0rem_0rem_0rem_0.0625rem_rgba(152,161,178,0.1)] overflow-visible`,
          ]}
          title={
            <ModalTitle
              title={'Add New Patient'}
              handleClose={() => setOpenAddPatientModal(false)}
            />
          }
          titleStyle={tw`!p-0 border-b border-stroke !rounded-t-xl`}
          content={
            <AddPatient
              customOnSubmit={data => {
                setPatientName(`${data.firstName} ${data.lastName}`);
                setOpenAddPatientModal(false);
              }}
              customOnCancel={() => setOpenAddPatientModal(false)}
            />
          }
          contetnContainerStyle={tw` min-h-[0px]`}
          element={React.Fragment}
          hasPrimaryButton={false}
          hasSecondaryButton={false}
          containerButtonsStyle={[tw`!p-0 border-none`]}
        />
      )}

      {patientName && (
        <ActionModal
          open={patientName}
          handleClose={() => setPatientName('')}
          customIcon={checkCircle}
          customHeaderStyle={tw`p-3 border-8 mb-2 border-success_50 bg-success_100 rounded-full`}
          description={
            <div tw="max-w-[22rem]">
              <span tw="text-text_primary font-medium">{patientName}</span> has been added to the
              patient list.
            </div>
          }
          PrimaryButtonStyle={tw`flex-1 !py-2.5 !rounded-md !font-medium !bg-white hover:!bg-white border-border_stroke text-text_primary`}
          // PrimaryButtonStyle={tw`!bg-Primary border-Primary_700 hover:!bg-Primary_600 text-text_primary`}
          primaryActionHandler={() => setPatientName('')}
          actionButtonText="Done"
          hasSecondaryButton={false}
        />
      )}
    </>
  );
}

export default AddPatientModal;
