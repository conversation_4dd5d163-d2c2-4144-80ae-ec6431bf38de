import SectionCards from '@/components/sectionsCard';
import 'twin.macro';
import tw from 'twin.macro';

const NotFound = ({ style, notFoundImage, headline, subTitle }) => {
  return (
    <SectionCards
      style={style}
      customStyle={tw`w-2/3 flex justify-center items-center overflow-hidden  p-3 text-text_primary space-y-4`}
    >
      <div tw="text-center">
        <img src={notFoundImage} />
        <p tw=" text-4xl text-text_primary font-semibold mb-4">{headline}</p>
        <p tw=" text-lg   text-text_secondary max-w-[29.5rem] mx-auto">{subTitle}</p>
      </div>
    </SectionCards>
  );
};

export default NotFound;
