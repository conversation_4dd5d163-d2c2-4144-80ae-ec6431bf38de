import Input from '@/components/shared/input';
import SearchIcon from '@assets/svgs/search.svg';
import tw from 'twin.macro';

// Search Input Component
const SearchInput = ({ register }) => (
  <Input
    name="filter_search"
    register={register}
    firstIcon={
      <div>
        <img src={SearchIcon} tw="opacity-50" alt="search" />
      </div>
    }
    placeholder="Search"
    containerSTyle={tw`bg-white my-[10px] px-[16px] h-[35px]`}
  />
);

export default SearchInput;
