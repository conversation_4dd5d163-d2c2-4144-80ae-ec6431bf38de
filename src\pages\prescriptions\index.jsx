import CalendarComponent from '@/components/calendar/calendar';
import PrescriptionCard from '@/components/prescriptionCard';
import TiptapEditor from '@/components/shared/tiptapEditor';
import GrayIcon from '@/assets/svgs/patient/gray-arrow.svg';
import { useEffect, useRef, useState } from 'react';
import tw from 'twin.macro';

const Prescriptions = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedEvent, setSelectedEvent] = useState(null);
  // Sample events data - in a real app, this would be fetched from an API
  const [events] = useState([
    {
      id: 1,
      title: 'Morning Exercise',
      start: new Date(2025, 2, 15, 8, 0),
      end: new Date(2025, 2, 15, 9, 0),
      resource: 'Completed',
    },
    {
      id: 2,
      title: 'Physical Therapy',
      start: new Date(2023, 5, 17, 10, 0),
      end: new Date(2023, 5, 17, 11, 30),
      resource: 'Missed',
    },
    {
      id: 3,
      title: 'Yoga Session',
      start: new Date(2025, 3, 20, 14, 0),
      end: new Date(2025, 3, 20, 15, 0),
      resource: 'Completed',
    },
  ]);

  // Sample daily status data (this would come from your backend)
  const [allDailyStatus] = useState([
    {
      date: '2025-03-15',
      status: 'Completed',
      icon: '',
      hasIcon: true,
      color: tw`bg-[#51C392]`,
    },
    {
      date: '2025-03-17',
      status: 'Missed',
      icon: '',
      hasIcon: true,
      color: tw`bg-[#FFCED3]`,
    },
    {
      date: '2025-04-05',
      status: 'Missed',
      icon: '',
      hasIcon: true,
      color: tw`bg-[#FFCED3]`,
    },
    {
      date: '2025-04-07',
      status: 'Pain',
      icon: '',
      hasIcon: false,
      color: tw`bg-[#D32F2F]`,
    },
  ]);

  // Filtered daily status for the current month/year
  const [dailyStatus, setDailyStatus] = useState([]);

  // Update filtered data when the current date changes
  useEffect(() => {
    // Filter daily status for the current month and year
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1; // JavaScript months are 0-indexed

    const filtered = allDailyStatus.filter(item => {
      const itemDate = new Date(item.date);
      return itemDate.getFullYear() === year && itemDate.getMonth() + 1 === month;
    });

    setDailyStatus(filtered);
  }, [currentDate, allDailyStatus]);

  const handleDateChange = date => {
    setCurrentDate(date);
  };

  const handleEventSelect = event => {
    setSelectedEvent(event);
  };

  const handleDateSelect = date => {
    // setSelectedDate(date);

    // Check if there's an event on the selected date
    const eventOnDate = events.find(event => {
      const eventDate = new Date(event.start);
      return (
        eventDate.getFullYear() === date.getFullYear() &&
        eventDate.getMonth() === date.getMonth() &&
        eventDate.getDate() === date.getDate()
      );
    });

    if (eventOnDate) {
      setSelectedEvent(eventOnDate);
      console.log('Found event on selected date:', eventOnDate.title);
    } else {
      console.log('No events on selected date:', date.toLocaleDateString());
    }
  };

  const editorRef = useRef();
  const handleContentChange = e => {
    console.log('er', e);
  };
  return (
    <>
      {/* <PrescriptionCard exercisesList={exercises} insightsList={insights} /> */}
      <TiptapEditor
        ref={editorRef}
        onContentChange={handleContentChange}
        placeholder="Start typing here..."
      />

      <CalendarComponent
        events={events}
        dailyStatus={dailyStatus}
        grayIcon={GrayIcon}
        onSelectEvent={handleEventSelect}
        onDateChange={handleDateChange}
        currentDate={currentDate}
        selectedEventId={selectedEvent?.id}
        onSelectSlot={handleDateSelect}
        pickerWidth="400px"
      />
    </>
  );
};

export default Prescriptions;
