import { useNavigate } from 'react-router-dom';
import AuthLayout from '@/components/auth/authLayout';
import BackButton from '@/components/shared/backButton';
import { showError } from '@/libs/react.toastify';
import ClinicCard from '@/components/auth/clinicCard';
import 'twin.macro';

const ClinicInfo = () => {
  const navigate = useNavigate();

  const handleContinue = async data => {
    try {
      //   const { detail } = await AuthApis.signup(
      //     {
      //       email: data.email,
      //       firstName: data.firstName,
      //       lastName: data.lastName,
      //       phoneNumber: "+" + data.phone_ch + data.phoneNumber,
      //     },
      //     token
      //   );
      // showSuccess(detail);
      navigate('/', { replace: true });
    } catch (error) {
      if (error?.response?.data?.detail) {
        showError(error?.response?.data?.detail);
      }
    }
  };

  return (
    <AuthLayout
      aboveTitle={<BackButton handelClick={() => navigate('/login', { replace: true })} />}
      authSection={
        <div tw="flex flex-col gap-4 max-h-[50vh] overflow-y-auto " className="element">
          <ClinicCard name={String('Clinic1')} imageUrl="https://picsum.photos/id/237/200/300" />
          <ClinicCard name={String('SSSSS')} />
        </div>
      }
      footerLinkText={'Let us know'}
      footerNormalText={"Can't log in?"}
      hasAuthFooter
      isLogIn
      redirectLink={'/sign-up'}
      redirectLinkText={'Edit now'}
      hasAuthRedirectFooter
      redirectNormalText={'Incorrect email address?'}
      subtitle="To continue to Rehabitaire"
      title={'Choose a clinic'}
      handelSubmit={() => {}}
      hideBorder={true}
    />
  );
};

export default ClinicInfo;
