import ToolTipIcon from '@assets/svgs/tooltip.svg';
import userHandUp from '@/assets/svgs/patient/user-hand-up.svg';
import userSpeak from '@/assets/svgs/patient/user-speak.svg';
import 'twin.macro';
import { formatJoinDate } from '@/utils/helpers';

const AccountAactiveData = ({ lastActivity = '10min ago', joinedDate, rootElementClasses }) => {
  return (
    <div tw="flex flex-wrap gap-5 text-black" css={rootElementClasses}>
      <div tw="flex gap-2.5 items-center">
        <img src={userSpeak} alt=" userSpeak icon" />
        {/* realtime data */}
        <p tw="text-sm">Last Activity: {lastActivity}</p>
      </div>
      <div tw="flex gap-2.5 items-center">
        <img src={userHandUp} alt=" userHandUp icon" />
        <p tw="text-sm">{formatJoinDate(joinedDate)}</p>
      </div>
    </div>
  );
};

export default AccountAactiveData;
