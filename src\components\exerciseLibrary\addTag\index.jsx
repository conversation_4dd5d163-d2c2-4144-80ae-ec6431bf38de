import { useEffect, useRef, useState } from 'react';
import Add from '@assets/svgs/exercise-library/primary-add.svg';
import whiteAdd from '@assets/svgs/exercise-library/white-add.svg';
import tw from 'twin.macro';

const AddTag = ({ options, setSelectedTags, selectedTags, title }) => {
  const [operSelectTag, setOpenSelectTag] = useState(false);
  const [value, setValue] = useState('');
  const [mouseEnter, setMouseEnter] = useState(false);
  const inputRef = useRef(null);

  // Focus input when opened
  useEffect(() => {
    if (operSelectTag && inputRef.current) {
      setTimeout(() => inputRef.current.focus(), 0);
    }
  }, [operSelectTag]);

  const handleSelect = val => {
    const newTag = {
      id: new Date(Date.now()),
      value: val.trim(),
    };

    document.getElementById('scrollable-section').style.overflow = 'hidden';

    const isDuplicate = selectedTags.some(
      tag => tag.title === newTag.title && tag.value === newTag.value
    );

    if (!isDuplicate) {
      setSelectedTags(prev => [newTag, ...prev]);
    }

    setOpenSelectTag(false);
    setValue('');
    document.body.style.overflow = '';
  };

  const triggerRef = useRef(null);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0, width: 0 });

  const openMenu = () => {
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setMenuPosition({ top: rect.bottom + 4, left: rect.left - 16, width: rect.width });
    }
    setOpenSelectTag(true);
  };

  return (
    <div tw="relative">
      {/* Main selector */}
      <div
        onMouseEnter={() => setMouseEnter(true)}
        onMouseLeave={() => setMouseEnter(false)}
        tw="flex gap-1 px-4 py-1 border transition-all duration-300 cursor-pointer text-Primary_800 border-Primary_800"
        css={
          operSelectTag
            ? tw`rounded-t-[10px]`
            : tw`rounded-[10px] hover:(text-white bg-Primary_800)`
        }
      >
        <div ref={triggerRef} onClick={openMenu} tw="flex gap-[2px] items-center w-[65px]">
          {!operSelectTag && (
            <img tw="w-[13px] h-[20px]" src={mouseEnter ? whiteAdd : Add} alt="add tag" />
          )}
          <p tw="text-[0.8rem]">{operSelectTag ? title : 'Add tag'}</p>
        </div>
        {operSelectTag && (
          <div
            style={{
              position: 'fixed',
              top: `${menuPosition.top}px`,
              left: `${menuPosition.left}px`,
              zIndex: 1000,
              width: 'fit-content',
            }}
            tw="bg-white border text-text_secondary border-border_stroke rounded-b-[10px] shadow-md"
          >
            {options.map(item => (
              <div
                key={item.id}
                tw="p-2 text-[0.8rem] cursor-pointer hover:bg-neutral_100 transition-all duration-300"
                onClick={() => handleSelect(item.value)}
              >
                {item.value}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AddTag;
