import PaymentCard from '../paymentCard';
import TwoColumnFields from '@/components/twoColumnFields';
import AuthInput from '@/components/auth/authInput';
import GenericSelect from '@/components/shared/select';
import PaymentLabel from '@/components/paymentlabel';
import 'twin.macro';
import OptionalLabel from '@/components/settings/optionalLabel';
import tw from 'twin.macro';

const BlingAddress = ({ register, control, errors, customStyle, isBilling = false }) => {
  return (
    <PaymentCard
      customStyle={customStyle}
      title={<PaymentLabel label={'Billing address'} />}
      showTitle
      content={
        <div tw="grid gap-4 mt-4">
          <TwoColumnFields
            isStartAlignment
            firstItem={
              <AuthInput
                register={register}
                name="firstName"
                label="First Name"
                placeholder="First Name"
                errorMessage={errors.firstName?.message}
              />
            }
            secundItem={
              <AuthInput
                register={register}
                name="lastName"
                label="Last Name"
                placeholder="First Name"
                errorMessage={errors.lastName?.message}
              />
            }
          />
          <AuthInput
            register={register}
            name="projectName"
            label="Project Name"
            placeholder="Project Name"
            errorMessage={errors.projectName?.message}
          />
          <AuthInput
            register={register}
            name="houseName"
            label="House Name"
            placeholder="House Name"
            errorMessage={errors.houseName?.message}
          />
          <AuthInput
            register={register}
            name="streetName"
            label="Street Name"
            placeholder="Street Name"
            errorMessage={errors.streetName?.message}
          />
          <TwoColumnFields
            isStartAlignment
            firstItem={
              <GenericSelect
                control={control}
                label="Country"
                name={`country`}
                placeholder="Country"
                options={[]}
                errorMessage={errors.country?.message}
              />
            }
            secundItem={
              <GenericSelect
                control={control}
                label="city"
                name={`city`}
                placeholder="city"
                options={[]}
                errorMessage={errors.city?.message}
              />
            }
          />
          <TwoColumnFields
            isStartAlignment
            firstItem={
              <GenericSelect
                control={control}
                label={<OptionalLabel label={'State'} />}
                name={`state`}
                placeholder="State"
                options={[]}
              />
            }
            secundItem={
              <AuthInput
                register={register}
                name="PostalCode"
                label="Postal code"
                errorMessage={errors.city?.message}
                placeholder="Postal code"
                containerSTyle={tw`h-[90%]`}
              />
            }
          />

          {isBilling && (
            <AuthInput
              register={register}
              name="company_name"
              label={<OptionalLabel label={'Company name '} />}
              errorMessage={errors.company_name?.message}
              placeholder="Postal code"
            />
          )}
          {isBilling && (
            <AuthInput
              register={register}
              name="vat_number"
              label={<OptionalLabel label={'Vat number'} />}
              errorMessage={errors.vat_number?.message}
              placeholder="Postal code"
            />
          )}
        </div>
      }
    />
  );
};

export default BlingAddress;
