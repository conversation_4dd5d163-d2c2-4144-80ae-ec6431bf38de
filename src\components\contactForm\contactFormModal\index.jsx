import Modal from '@/components/shared/normalModal';
import { useForm } from 'react-hook-form';
// import { ContactApis } from "../../apis/contact-us/api";
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useRef, useState } from 'react';
import ThanksModal from '@/components/contactForm/thanksModal';
// import useRecaptchaDimensions from "@/hooks/use-recaptch-dimensions";
// import { schemaContactForm } from '@/schemas';
import HideOverflowScrollStyleContactFormModal from '@/components/contactForm/hideOverflowScrollStyleContactFormModal';
import { useWindowDimensions } from '@/hooks';
import ContactFormFields from '@/components/contactForm/contactFormFields';
import { useContactFormModal } from '@/zustand/contact-form-modla';
import { showError } from '@/libs/react.toastify';
import AuthRecaptchaButton from '@/components/auth/authRecaptchaButton';
import tw from 'twin.macro';
import { schemaContactForm } from './schema';

const ContactForm = ({ inLandingPage = false, isAuth = false, userEmail }) => {
  const [errorMessage, setErrorMessage] = useState('');
  const [recaptchError, setRecaptchaError] = useState(false);
  const [openThankstModal, setOpenThankstModal] = useState(false);

  const recaptchaRef = useRef(null);
  const inputRef = useRef(null);
  const leftSideRef = useRef();
  const { clientWindowWidth } = useWindowDimensions();

  // const { height, width } = useRecaptchaDimensions({
  //   clientWindowWidth,
  //   inputRef,
  //   insideContactForm: true,
  //   leftSideRef,
  // });
  const { openContactForm, changeOpenContactForm } = useContactFormModal();
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
    reset,
    trigger,
    control,
    setValue,
  } = useForm({
    defaultValues: {
      email: userEmail ? userEmail : '',
      description: '',
      recaptchaToken: '',
    },
    resolver: yupResolver(schemaContactForm),
    mode: 'onBlur',
  });

  const onSubmit = async data => {
    try {
      setErrorMessage('');
      // if (isAuth) {
      //   await ContactApis.ContactUsAuth({
      //     email: data.email,
      //     message: data.description,
      //   });
      // } else {
      //   const token = await recaptchaRef.current.executeAsync();
      //   await ContactApis.ContactUsAnon({
      //     email: data.email,
      //     message: data.description,
      //     recaptchaToken: token,
      //   });
      // }
      reset();
      recaptchaRef.current.reset();
      setOpenThankstModal(true);
    } catch (error) {
      if (!error.response.data.isToast) {
        setErrorMessage(error.response.data.message);
      } else if (error.response.data.errors.recaptchaToken) {
        setRecaptchaError(true);
      } else {
        showError(error.response.data.message);
      }
      recaptchaRef.current.reset();
    }
  };

  const handleCloseModal = () => {
    changeOpenContactForm(false);
  };

  const handleCloseModals = () => {
    changeOpenContactForm(false);
    setOpenThankstModal(false);
  };

  useEffect(() => {
    const email = localStorage.getItem('rehab-email');

    if (email) {
      setValue('email', JSON.parse(email));
    }
  }, []);

  return (
    <Modal
      open={openContactForm}
      containerModalStyle={tw`bg-[#ffffffd1] h-auto overflow-y-auto max-h-[90vh] rounded-[20px]!`}
      backgroundModal={'rgba(0, 0, 0,0.25)'}
      handleClose={handleCloseModal}
      hideIcon={clientWindowWidth < 767}
      content={
        <div tw="flex lg:flex-row flex-col lg:gap-8 gap-2 content-center md:w-[70vw] max-md:w-[90vw]! overflow-x-hidden lg:px-14 px-4 lg:py-14 py-4">
          <HideOverflowScrollStyleContactFormModal />
          <div ref={leftSideRef} tw="lg:w-[40%] w-full text-center md:text-start">
            <h1 tw="font-bold xl:text-[48px] lg:text-[22px] text-[36px] mb-4">Get in touch</h1>
            <p tw="text-text_primary">
              {inLandingPage
                ? "Have a question or an awesome idea to share? We'd love to hear from you! Fill out the form below and we'll get back to you soon."
                : 'Have problems accessing your account or signing up? Fill out the form to contact our support.'}
            </p>
            {!isAuth && (
              <div tw="hidden mt-10 lg:block">
                <AuthRecaptchaButton
                  control={control}
                  recaptchError={recaptchError}
                  recaptchaRef={recaptchaRef}
                />
              </div>
            )}
          </div>
          <ContactFormFields
            handleSubmit={handleSubmit(onSubmit)}
            control={control}
            errorMessage={errorMessage}
            errors={errors}
            // height={height}
            inputRef={inputRef}
            isAuth={isAuth}
            isSubmitting={isSubmitting}
            recaptchError={recaptchError}
            recaptchaRef={recaptchaRef}
            register={register}
            trigger={trigger}
            // width={width}
            emailReadOnly={userEmail ? true : false}
          />

          {openThankstModal && (
            <ThanksModal
              openModal={openThankstModal}
              handleCLoseModal={handleCloseModals}
              handleBackButton={handleCloseModals}
            />
          )}
        </div>
      }
    />
  );
};
export default ContactForm;
