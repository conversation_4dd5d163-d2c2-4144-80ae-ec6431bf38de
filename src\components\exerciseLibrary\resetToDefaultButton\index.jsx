import Refresh from '@assets/svgs/exercise-library/Refresh.svg';
import 'twin.macro';

const ResetToDefaultSection = ({ handelClick, text }) => {
  return (
    <div tw="flex gap-2 items-center mb-[1.19em]">
      <p tw="[line-height: 133%] text-[1em] font-[600]">Parameters</p>
      <p
        tw="text-[#285FF5] flex gap-1 items-center text-[0.8em] cursor-pointer"
        onClick={handelClick}
        data-no-toggle="true"
      >
        <img alt="refresh" src={Refresh} tw="w-[16px]" />
        <span>{text}</span>
      </p>
    </div>
  );
};

export default ResetToDefaultSection;
