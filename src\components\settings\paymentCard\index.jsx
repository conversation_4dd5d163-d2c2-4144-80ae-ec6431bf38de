import DeleteIcon from '@assets/svgs/delete-red.svg';
import tw from 'twin.macro';

const PaymentCard = ({
  title,
  imageUrl,
  date,
  isDefault,
  isExpire,
  clickOnDeleteIcon,
  id,
  handelSetAsDefault,
  isHasActionsButtons = true,
  rootElementStyle,
}) => {
  const normalStyle = tw`text-info `;
  const DefaultStyle = tw`text-success bg-success_50`;
  const ExpireStyle = tw`text-error bg-error_50`;
  const buttonStyle = tw`font-medium text-[.875rem] px-[16px] py-[10px] rounded-[6px]`;

  const handelButtonClick = id => {
    if (!isExpire && !isDefault) {
      handelSetAsDefault(id);
    }
  };
  return (
    <div
      tw="flex justify-between items-center p-6 rounded-xl border border-border_stroke"
      css={rootElementStyle}
    >
      <div tw="flex gap-4 items-center">
        <img src={imageUrl} alt="image url" tw="w-[67px] h-[48px] rounded-lg object-fill" />
        <div>
          <p tw="font-medium" css={date && tw`mb-1.5`}>
            {title}
          </p>
          {date && <p tw="text-[.875rem] opacity-50 font-medium">Expires {date}</p>}
        </div>
      </div>
      {isHasActionsButtons && (
        <div tw="flex gap-4 items-center">
          <button
            css={[isExpire ? ExpireStyle : isDefault ? DefaultStyle : normalStyle, buttonStyle]}
            onClick={() => handelButtonClick(id)}
          >
            {isExpire ? 'Expired' : isDefault ? 'Default' : 'set as default'}
          </button>
          <img
            src={DeleteIcon}
            alt="delete"
            tw="cursor-pointer"
            onClick={() => clickOnDeleteIcon(id)}
          />
        </div>
      )}
    </div>
  );
};

export default PaymentCard;
