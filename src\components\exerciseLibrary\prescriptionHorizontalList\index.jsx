import { useDrop } from 'react-dnd';
import tw from 'twin.macro';

const DroppableTarget = ({
  children,
  onDrop,
  disableDragAndDrop,
  showPlaceholderText,
  getCurrentTabIndex,
}) => {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: ['EXERCISE'],
    drop: item => {
      if (item.type === 'source') {
        onDrop(item);
      }
    },
    collect: monitor => ({
      isOver: !!monitor.isOver(),
    }),
  }));

  return (
    <div
      ref={!disableDragAndDrop ? drop : null}
      tw="flex overflow-x-scroll gap-4 pb-2 w-full h-[90%] items-center"
      className="custom-scroll"
      style={{
        backgroundColor: isOver ? 'rgba(141, 161, 43, 0.1)' : 'transparent',
        transition: 'all 0.3s ease',
        position: 'relative',
        maxWidth: '100%',
        maxHeight: '240px',
        overflowX: 'scroll',
        overflowY: 'hidden',
      }}
    >
      {children}
      {showPlaceholderText && (
        <div
          tw="h-[165px] px-[6px] bg-Primary_50 flex-shrink-0 w-[155px] rounded-[8px] border border-Primary_600 flex items-center text-center"
          css={[
            isOver ? tw`border-Primary_600 bg-Primary_100 scale-105` : tw``,
            tw`transition-all duration-200 ease-in-out`,
          ]}
        >
          <span tw="font-normal text-black text-[0.8rem] items-center gap-1">
            Click
            <span
              style={{
                width: '20px',
                height: '20px',
                backgroundSize: 'contain',
                backgroundRepeat: 'noRepeat',
                backgroundPosition: 'center',
                display: 'inlineBlock',
              }}
              className={getCurrentTabIndex() === 0 ? 'add-icon' : 'add-template-icon'}
            />
            {getCurrentTabIndex() === 0
              ? 'to add an exercise or drag and drop'
              : 'to add an template or drag and drop'}
          </span>
          {isOver && (
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                border: '2px dashed #8DA12B',
                borderRadius: '20px',
                pointerEvents: 'none',
                animation: 'pulse 1.5s infinite',
              }}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default DroppableTarget;
