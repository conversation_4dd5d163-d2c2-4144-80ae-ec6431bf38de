import SectionContainer from '@/components/patient/SectionContainer';
import LabelSections from '@/components/sectionLabels';
import ProfilePicture from '@/components/profilePicture';
import 'twin.macro';
import AccountAactiveData from '@/components/accountActiveData';
import tw from 'twin.macro';
import Input from '@/components/shared/input';
function StaffInformation({ formData, setValue, register, trigger, errors, fieldsThPermistions }) {
  const inputsData = [
    {
      id: 0,
      name: 'firstName',
      label: 'First Name',
      placeholder: 'First Name',
      defaultValue: formData.firstName,
      onBlur: () => trigger('firstName'),
      errorMessage: errors?.firstName?.message,
      disabled: !fieldsThPermistions.firstName,
    },
    {
      id: 1,
      name: 'lastName',
      label: 'Last Name',
      placeholder: 'Last Name',
      defaultValue: formData.lastName,
      onBlur: () => trigger('lastName'),
      errorMessage: errors?.lastName?.message,
      disabled: !fieldsThPermistions.lastName,
    },
  ];
  return (
    <SectionContainer customeClass={tw`py-5`}>
      <LabelSections text={'Personal Information'} customStyle={tw`mb-5 py-2.5`} />

      <div tw="flex gap-6 items-end">
        {/* Image uploader */}
        <ProfilePicture
          disabled={true}
          onFileUpload={e =>
            setValue('imageFile', e, {
              shouldDirty: true,
              shouldTouch: true,
              shouldValidate: true,
            })
          }
          handelRemoveImage={() =>
            setValue('imageFile', null, {
              shouldDirty: true,
              shouldTouch: true,
              shouldValidate: true,
            })
          }
          containerStyle={tw`w-[6.25rem] h-[6.25rem]`}
          iconStyle={tw`p-0`}
        />

        <div tw="flex gap-y-3 gap-x-8 w-full">
          {inputsData.map((item, idx) => (
            <div tw="basis-[50%]" key={idx}>
              <Input
                {...item}
                {...{
                  register,
                  disabledStyle: tw`bg-disable cursor-not-allowed hover:bg-disable`,
                  containerSTyle: tw`bg-white border-border_stroke`,
                  labelStyle: tw`text-base`,
                  inputStye: tw`text-[0.8rem] py-4`,
                  labelRootElementStyle: tw`mb-2`,
                }}
              />
            </div>
          ))}
        </div>
      </div>
    </SectionContainer>
  );
}

export default StaffInformation;
