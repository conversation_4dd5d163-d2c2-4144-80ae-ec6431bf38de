import LoadingElement from '../loadingElement';
import tw from 'twin.macro';

const BlackButton = ({ handleCLickButton, type = 'button', text, isSubmitting }) => {
  return (
    <button
      type={type}
      onClick={handleCLickButton}
      tw="px-4 py-2 text-sm text-white bg-black rounded-md transition-all duration-300 hover:bg-Primary_600 md:py-2"
      css={isSubmitting ? tw`bg-Primary_600` : tw``}
    >
      {isSubmitting ? (
        <div tw="flex items-center justify-center w-[40px]">
          <LoadingElement width={28} height={28} />
        </div>
      ) : (
        text
      )}
    </button>
  );
};
export default BlackButton;
