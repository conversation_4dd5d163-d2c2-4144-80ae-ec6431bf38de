import React from 'react';
import tw from 'twin.macro';
import ArrowIcon from '@assets/svgs/gray-arrow.svg';
import { ModalViews } from '../..';

const VoiceMessageTitle = ({ modalView, setModalView }) => {
  const getButtonStyles = theViewOfTheButton => [
    tw`px-[10px] py-[20px] border-l border-l-border_stroke border-r border-r-border_stroke border-b-4 border-b-transparent text-text_primary font-bold text-[18px]`,
    modalView === theViewOfTheButton
      ? tw`bg-neutral_300 border-b-text_primary`
      : tw`opacity-30 hover:opacity-100 transition-opacity duration-300 `,
    modalView !== theViewOfTheButton &&
      theViewOfTheButton === ModalViews.VOICE_RECORDING &&
      tw`border-r-transparent`,
    modalView !== theViewOfTheButton &&
      theViewOfTheButton === ModalViews.TEXT_TO_SPEECH &&
      tw`border-l-transparent`,
  ];
  return (
    <div css={tw`px-[14px] w-full flex items-center gap-[10px] !bg-neutral_50 rounded-t-[12px]`}>
      <button
        onClick={() => setModalView(ModalViews.PRE_SESSION_MESSAGES)}
        aria-label="return"
        css={tw` px-5 py-4 my-[10px]`}
      >
        <img src={ArrowIcon} alt="return icon" css={tw`w-[13px] h-[13px]`} />
      </button>
      <div>
        <button
          css={getButtonStyles(ModalViews.VOICE_RECORDING)}
          onClick={() => setModalView(ModalViews.VOICE_RECORDING)}
        >
          Record Message
        </button>
        <button
          css={getButtonStyles(ModalViews.TEXT_TO_SPEECH)}
          onClick={() => setModalView(ModalViews.TEXT_TO_SPEECH)}
        >
          Text to Speech
        </button>
      </div>
    </div>
  );
};

export default VoiceMessageTitle;
