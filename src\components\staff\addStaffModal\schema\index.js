import * as yup from 'yup';

export const schemaAddStaff = yup
  .object({
    firstName: yup
      .string()
      .required('Required field.')
      .test(
        'is-valid-first-name',
        'Invalid name.',
        value => !value || /^[\u0600-\u06FFa-zA-Z]+(?: [\u0600-\u06FFa-zA-Z]+){0,2}$/.test(value)
      ),
    lastName: yup
      .string()
      .required('Required field.')
      .test(
        'is-valid-last-name',
        'Invalid name.',
        value => !value || /^[\u0600-\u06FFa-zA-Z]+(?: [\u0600-\u06FFa-zA-Z]+){0,1}$/.test(value)
      ),
    countryCode: yup.string().required('Required field.'),
    localNumber: yup
      .string()
      .required('Required field.')
      .matches(/^\d+$/, 'Invalid phone number.')
      .min(4, 'Invalid phone number.')
      .max(17, 'Invalid phone number.'),
    email: yup.string().email('Invalid email.').required('Required field.'),
    role: yup
      .object()
      .shape({
        label: yup.string().required(),
        value: yup.string().required(),
      })
      .required('Required field.'),
    licenseNumber: yup
      .number()
      .typeError('Invalid License Number')
      .nullable()
      .min(2, 'Invalid License Number.')
      .transform((value, original) => (original === '' ? null : value))
      .notRequired(),
  })
  .required();
