import Icon1 from '@assets/svgs/settings/Calendar Icons.svg';
import PersonIcon from '@assets/svgs/settings/person.svg';
import Icon2 from '@assets/svgs/settings/solar_clipboard-add-outline.svg';
import Icon3 from '@assets/svgs/settings/solar_filter-outline.svg';
import Icon4 from '@assets/svgs/settings/solar_users-group-rounded-outline.svg';

const notifications = [
  // Example notifications
  {
    id: 1,
    type: 'invitation',
    PT: '<PERSON>',
    patientName: '<PERSON>',
    patientId: '1',
    timestamp: 'Last Wednesday at 9:42 AM',
    isRead: false,
  },
  {
    id: 2,
    type: 'painReport',
    patientName: '<PERSON>',
    patientId: '1',
    exerciseName: 'Lower Back Stretch',
    timestamp: 'Last Wednesday at 9:42 AM',
    isRead: false,
  },
  {
    id: 3,
    type: 'sesssionCompletion',
    patientName: '<PERSON> Smith',
    patientId: '1',
    exerciseName: 'Lower Back Stretch',
    rating: 'Fair',
    timestamp: 'Last Wednesday at 9:42 AM',
    isRead: true,
  },
  {
    id: 4,
    type: 'discharge',
    PT: '<PERSON>',
    patientName: '<PERSON>',
    patientId: '1',
    timestamp: 'Last Wednesday at 9:42 AM',
    isRead: true,
  },
  {
    id: 5,
    type: 'sesssionCompletion',
    patientName: 'Rico Smith',
    patientId: '1',
    exerciseName: 'Lower Back Stretch',
    rating: 'Fair',
    timestamp: 'Last Wednesday at 9:42 AM',
    isRead: true,
  },
  {
    id: 6,
    type: 'discharge',
    PT: 'John Doe',
    patientName: 'Sean King',
    patientId: '1',
    timestamp: 'Last Wednesday at 9:42 AM',
    isRead: true,
  },
  {
    id: 7,
    type: 'sesssionCompletion',
    patientName: 'Rico Smith',
    patientId: '1',
    exerciseName: 'Lower Back Stretch',
    rating: 'Fair',
    timestamp: 'Last Wednesday at 9:42 AM',
    isRead: true,
  },
  {
    id: 8,
    type: 'discharge',
    PT: 'John Doe',
    patientName: 'Sean King',
    patientId: '1',
    timestamp: 'Last Wednesday at 9:42 AM',
    isRead: true,
  },
];

//const latest = [];
//const older = [];
const latest = [
  {
    actor: 'Lim Tangko',
    action: 'discharged',
    target: 'Alice Johnson',
    details: 'from Revalidatie',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'has been flagged as high adherence',
    target: '',
    details: 'this month',
    icon: PersonIcon,
  },
  {
    actor: 'Alice Johnson',
    action: 'missed 1 out of 8 sessions',
    target: '',
    details: 'this week',
    icon: Icon2,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon3,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon4,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon4,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
];

// Update the `date` field for each notification
const currentTime = new Date(); // Get the current time
latest.forEach((notification, index) => {
  const updatedDate = new Date(currentTime.getTime() + index * 6 * 60 * 60 * 1000); // Increment by 6 hours
  notification.date = updatedDate.toISOString(); // Convert to ISO string
});

const older = [
  {
    actor: 'Lim Tangko',
    action: 'discharged',
    target: 'Alice Johnson',
    details: 'from Revalidatie',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'has been flagged as high adherence',
    target: '',
    details: 'this month',
    icon: Icon2,
  },
  {
    actor: 'Alice Johnson',
    action: 'missed 1 out of 8 sessions',
    target: '',
    details: 'this week',
    icon: Icon3,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon4,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon4,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
  {
    actor: 'Alice Johnson',
    action: 'logged out of',
    target: 'revalidatie-patients.rehabitair.com',
    details: '',
    icon: Icon1,
  },
];

// Update the `date` field for each notification
const oneWeekAgo = new Date(); // Get the current date
oneWeekAgo.setDate(oneWeekAgo.getDate() - 7); // Subtract 7 days to get one week ago

older.forEach((notification, index) => {
  const updatedDate = new Date(oneWeekAgo.getTime() - index * 24 * 60 * 60 * 1000); // Decrement by 1 day
  notification.date = updatedDate.toISOString(); // Convert to ISO string
});

export default notifications;
export { latest, older };
