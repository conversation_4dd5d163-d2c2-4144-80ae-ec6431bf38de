import { Link } from 'react-router-dom';
import BreadcrumbSeparator from '@assets/svgs/patient/breadcrumbs-separator.svg';
import { Fragment } from 'react';
import 'twin.macro';
/**
 * Breadcrumb Component
 * @param {Array} items - Array of breadcrumb items: [{ label: 'Home', to: '/' }, { label: 'User', to: '/user' }]
 */
const Breadcrumb = ({ items = [] }) => {
  return (
    <nav
      tw="text-[14px] font-medium text-text_secondary flex items-center space-x-2.5 mb-2.5 "
      aria-label="Breadcrumb"
    >
      {items.map((item, index) => {
        const isLast = index === items.length - 1;
        return (
          <Fragment key={item}>
            {!isLast ? (
              <div tw="flex items-center space-x-2.5">
                <Link to={item.to} tw="">
                  {item.label}
                </Link>
                <img src={BreadcrumbSeparator} alt="BreadcrumbSeparator Icon" />
              </div>
            ) : (
              <span key={item.label} tw="font-semibold text-text_primary">
                {item.label}
              </span>
            )}
          </Fragment>
        );
      })}
    </nav>
  );
};
export default Breadcrumb;
