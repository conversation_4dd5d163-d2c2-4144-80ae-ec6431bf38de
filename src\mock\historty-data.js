///?month=Jan&2025
const histroy_data = [
  // {
  //   patient_joind_date: 'June, 2025',
  //   history_date_year_based: ['Jul', 'Jun'],
  //   rolling_adherence: { rate: '66.6%', color: '#0B834F' },
  //   month: 'Jul, 2025',
  //   days: [],
  // },
  {
    patient_joind_date: 'June, 2025',
    rolling_adherence: { rate: '30.6%', color: '#D12E3C' },
    history_date_year_based: ['Jun'],
    month: 'Jun, 2025',
    days: [
      {
        date: '2025-06-04',
        performed: 0,
        total: 3,
        status: 'missed',
        haveFeedback: false,
        haveReportedPain: false,
        prescription_id: 10,
      },
      {
        date: '2025-06-12',
        performed: 1,
        total: 3,
        status: 'incomplate',
        haveFeedback: false,
        haveReportedPain: true,
        prescription_id: 9,
      },
      {
        date: '2025-06-13',
        performed: 2,
        total: 3,
        status: 'incomplate',
        haveFeedback: true,
        haveReportedPain: false,
        prescription_id: 8,
      },
      {
        date: '2025-06-22',
        performed: 0,
        total: 2,
        status: 'missed',
        haveFeedback: false,
        haveReportedPain: true,
        prescription_id: 7,
      },
      {
        date: '2025-06-23',
        performed: 1,
        total: 2,
        status: 'incomplate',
        haveFeedback: false,
        haveReportedPain: false,
        prescription_id: 6,
      },
      {
        date: '2025-06-24',
        performed: 3,
        total: 3,
        status: 'complated',
        haveFeedback: true,
        haveReportedPain: true,
        prescription_id: 5,
      },
    ],
  },
];

const history_prescriptions = [
  {
    id: 1,
    name: 'Posture bureautique - tensions cervicales',
    sessions: [
      {
        session_number: 1,
        session_time: 'Jun/12/2025 | 13:07',
        feedback:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.',

        completion_rate: '100%',
        pain_level: 7,
        calories: 573,
      },
      {
        session_number: 2,
        session_time: 'Jun/12/2025 | 18:30',
        completion_rate: '100%',
        pain_level: 6,
        calories: 573,
      },
    ],
    exercises: [
      {
        id: 1,
        title: 'Squats',
        set: 'Set 1/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 2,
        title: 'Squats',
        set: ' Set 2/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 3,
        title: 'Plank',
        inputs: [
          {
            label: 'Time',
            value: '30',
            unit: 'Sec',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 4,
        title: 'Shoulder external rotation - right',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 5,
        title: 'Shoulder external rotation - left',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
    ],
  },
  {
    id: 2,
    name: 'Posture bureautique - tensions cervicales',
    sessions: [
      {
        session_number: 1,
        session_time: 'Jun/12/2025 | 13:07',
        feedback:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.',

        completion_rate: '100%',
        pain_level: 7,
        calories: 573,
      },
      {
        session_number: 2,
        session_time: 'Jun/12/2025 | 18:30',
        completion_rate: '100%',
        pain_level: 6,
        calories: 573,
      },
    ],
    exercises: [
      {
        id: 1,
        title: 'Squats',
        set: 'Set 1/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 2,
        title: 'Squats',
        set: ' Set 2/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 3,
        title: 'Plank',
        inputs: [
          {
            label: 'Time',
            value: '30',
            unit: 'Sec',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 4,
        title: 'Shoulder external rotation - right',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 5,
        title: 'Shoulder external rotation - left',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
    ],
  },
  {
    id: 3,
    name: 'Posture bureautique - tensions cervicales',
    sessions: [
      {
        session_number: 1,
        session_time: 'Jun/12/2025 | 13:07',
        feedback:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.',

        completion_rate: '100%',
        pain_level: 7,
        calories: 573,
      },
      {
        session_number: 2,
        session_time: 'Jun/12/2025 | 18:30',
        completion_rate: '100%',
        pain_level: 6,
        calories: 573,
      },
    ],
    exercises: [
      {
        id: 1,
        title: 'Squats',
        set: 'Set 1/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 2,
        title: 'Squats',
        set: ' Set 2/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 3,
        title: 'Plank',
        inputs: [
          {
            label: 'Time',
            value: '30',
            unit: 'Sec',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 4,
        title: 'Shoulder external rotation - right',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 5,
        title: 'Shoulder external rotation - left',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
    ],
  },
  {
    id: 4,
    name: 'Posture bureautique - tensions cervicales',
    sessions: [
      {
        session_number: 1,
        session_time: 'Jun/12/2025 | 13:07',
        feedback:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.',

        completion_rate: '100%',
        pain_level: 7,
        calories: 573,
      },
      {
        session_number: 2,
        session_time: 'Jun/12/2025 | 18:30',
        completion_rate: '100%',
        pain_level: 6,
        calories: 573,
      },
    ],
    exercises: [
      {
        id: 1,
        title: 'Squats',
        set: 'Set 1/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 2,
        title: 'Squats',
        set: ' Set 2/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 3,
        title: 'Plank',
        inputs: [
          {
            label: 'Time',
            value: '30',
            unit: 'Sec',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 4,
        title: 'Shoulder external rotation - right',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 5,
        title: 'Shoulder external rotation - left',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
    ],
  },
  {
    id: 5,
    name: 'Posture bureautique - tensions cervicales',
    sessions: [
      {
        session_number: 1,
        session_time: 'Jun/12/2025 | 13:07',
        feedback:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.',

        completion_rate: '100%',
        pain_level: 7,
        calories: 573,
      },
      {
        session_number: 2,
        session_time: 'Jun/12/2025 | 18:30',
        completion_rate: '100%',
        pain_level: 6,
        calories: 573,
      },
    ],
    exercises: [
      {
        id: 1,
        title: 'Squats',
        set: 'Set 1/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 2,
        title: 'Squats',
        set: ' Set 2/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 3,
        title: 'Plank',
        inputs: [
          {
            label: 'Time',
            value: '30',
            unit: 'Sec',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 4,
        title: 'Shoulder external rotation - right',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 5,
        title: 'Shoulder external rotation - left',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
    ],
  },
  {
    id: 6,
    name: 'Posture bureautique - tensions cervicales',
    sessions: [
      {
        session_number: 1,
        session_time: 'Jun/12/2025 | 13:07',

        completion_rate: 'N/A',
        pain_level: 'N/A',
        calories: 'N/A',
      },
      {
        session_number: 2,
        session_time: 'Jun/12/2025 | 18:30',
        completion_rate: 'N/A',
        pain_level: 'N/A',
        calories: 'N/A',
      },
    ],
    exercises: [
      {
        id: 1,
        title: 'Squats',
        set: 'Set 1/2',
        inputs: [
          {
            label: 'Reps',
            value: 'N/A',
            // unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: 'N/A',
            // unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 2,
        title: 'Squats',
        set: ' Set 2/2',
        inputs: [
          {
            label: 'Reps',
            value: 'N/A',
            // unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: 'N/A',
            // unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 3,
        title: 'Plank',
        inputs: [
          {
            label: 'Time',
            value: 'N/A',
            // unit: 'Sec',
          },
          {
            label: 'Exercise Duration',
            value: 'N/A',
            // unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 4,
        title: 'Shoulder external rotation - right',
        inputs: [
          {
            label: 'Reps',
            value: 'N/A',
            // unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: 'N/A',
            // unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 5,
        title: 'Shoulder external rotation - left',
        inputs: [
          {
            label: 'Reps',
            value: 'N/A',
            // unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: 'N/A',
            // unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
    ],
  },
  {
    id: 7,
    name: 'Posture bureautique - tensions cervicales',
    sessions: [
      {
        session_number: 1,
        session_time: 'Jun/12/2025 | 13:07',
        feedback:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.',

        completion_rate: '100%',
        pain_level: 7,
        calories: 573,
      },
      {
        session_number: 2,
        session_time: 'Jun/12/2025 | 18:30',
        completion_rate: '100%',
        pain_level: 6,
        calories: 573,
      },
    ],
    exercises: [
      {
        id: 1,
        title: 'Squats',
        set: 'Set 1/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 2,
        title: 'Squats',
        set: ' Set 2/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 3,
        title: 'Plank',
        inputs: [
          {
            label: 'Time',
            value: '30',
            unit: 'Sec',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 4,
        title: 'Shoulder external rotation - right',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 5,
        title: 'Shoulder external rotation - left',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
    ],
  },
  {
    id: 8,
    name: 'Posture bureautique - tensions cervicales',
    sessions: [
      {
        session_number: 1,
        session_time: 'Jun/12/2025 | 13:07',
        feedback:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.',

        completion_rate: '100%',
        pain_level: 7,
        calories: 573,
      },
      {
        session_number: 2,
        session_time: 'Jun/12/2025 | 18:30',
        completion_rate: '100%',
        pain_level: 6,
        calories: 573,
      },
    ],
    exercises: [
      {
        id: 1,
        title: 'Squats',
        set: 'Set 1/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 2,
        title: 'Squats',
        set: ' Set 2/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 3,
        title: 'Plank',
        inputs: [
          {
            label: 'Time',
            value: '30',
            unit: 'Sec',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 4,
        title: 'Shoulder external rotation - right',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 5,
        title: 'Shoulder external rotation - left',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
    ],
  },
  {
    id: 9,
    name: 'Posture bureautique - tensions cervicales',
    sessions: [
      {
        session_number: 1,
        session_time: 'Jun/12/2025 | 13:07',
        feedback:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.',

        completion_rate: '100%',
        pain_level: 7,
        calories: 573,
      },
      {
        session_number: 2,
        session_time: 'Jun/12/2025 | 18:30',
        completion_rate: '100%',
        pain_level: 6,
        calories: 573,
      },
    ],
    exercises: [
      {
        id: 1,
        title: 'Squats',
        set: 'Set 1/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 2,
        title: 'Squats',
        set: ' Set 2/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 3,
        title: 'Plank',
        inputs: [
          {
            label: 'Time',
            value: '30',
            unit: 'Sec',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 4,
        title: 'Shoulder external rotation - right',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 5,
        title: 'Shoulder external rotation - left',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
    ],
  },
  {
    id: 10,
    name: 'Posture bureautique - tensions cervicales',
    sessions: [
      {
        session_number: 1,
        session_time: 'Jun/12/2025 | 13:07',
        feedback:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam lectus risus, finibus ornare vestibulum et, feugiat quis dui. Vivamus sit amet dolor et magna facilisis rhoncus. Curabitur maximus est sed porta scelerisque.',

        completion_rate: '100%',
        pain_level: 7,
        calories: 573,
      },
      {
        session_number: 2,
        session_time: 'Jun/12/2025 | 18:30',
        completion_rate: '100%',
        pain_level: 6,
        calories: 573,
      },
    ],
    exercises: [
      {
        id: 1,
        title: 'Squats',
        set: 'Set 1/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 2,
        title: 'Squats',
        set: ' Set 2/2',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 3,
        title: 'Plank',
        inputs: [
          {
            label: 'Time',
            value: '30',
            unit: 'Sec',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 4,
        title: 'Shoulder external rotation - right',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-1.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
      {
        id: 5,
        title: 'Shoulder external rotation - left',
        inputs: [
          {
            label: 'Reps',
            value: '5',
            unit: 'Reps',
          },
          {
            label: 'Exercise Duration',
            value: '45',
            unit: 'Sec',
          },
        ],
        thumb_url: '/images/exercise3-2.png',
        video_url:
          'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4',
      },
    ],
  },
];

export const getCalnderDataByMonthAndYear = date => {
  const month = date.toLocaleString('en-US', { month: 'short' });
  const year = date.getFullYear();

  const formatter_date = `${month}, ${year}`;

  console.log('formatter_date=> ', formatter_date);

  const monthdata = histroy_data.find(item => {
    return item.month === formatter_date;
  });
  return monthdata;
};

export const getHistoryPrescriptionById = id => {
  const prescription = history_prescriptions.find(item => item.id === id);
  return prescription;
};
