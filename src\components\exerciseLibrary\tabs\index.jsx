import { useEffect } from 'react';
import Tabs from '@/components/shared/tabs';
import SearchPage from '../searchPage';
import 'twin.macro';

const TabsPage = ({
  register,
  setSearchParams,
  searchParams,
  tabsData,
  getCurrentTabIndex,
  state,
}) => {
  // Set default tab if no tab parameter is present
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (!tabParam) {
      setSearchParams({ tab: 'exercises' });
    }
  }, [searchParams]);

  const handleTabChange = tabId => {
    if (!state.isEditFilter) {
      const tabName = tabId.id === 0 ? 'exercises' : 'templates';
      setSearchParams({ tab: tabName });
    }
  };

  return (
    <div
      tw="bg-neutral_100 px-[10px] h-fit py-[8px] flex gap-4 items-center rounded-[8px]"
      id="tabs"
    >
      <SearchPage register={register} />
      <Tabs
        tabsData={tabsData}
        selectedTab={getCurrentTabIndex()}
        handelSelected={handleTabChange}
      />
    </div>
  );
};

export default TabsPage;
