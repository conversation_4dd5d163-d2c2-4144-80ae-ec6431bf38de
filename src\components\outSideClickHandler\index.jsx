import React, { useEffect, useRef } from 'react';

/**
 * OutSideClickHandler Component
 * Wraps children and calls onClickOutside when a click occurs outside the wrapper or optional iconRef.
 *
 * @param {Object} props
 * @param {Function} props.onClickOutside - Function to call when click is outside
 * @param {React.RefObject} [props.iconRef] - Optional ref for an icon to exclude from outside detection
 * @param {React.ReactNode} props.children - Children to render inside the wrapper
 * @param {string} [props.className] - Optional className for styling
 * @param {Object} [props.style] - Optional style object
 */
const OutSideClickHandler = ({ onClickOutside, iconRef, children, className, style, ...rest }) => {
  const wrapperRef = useRef(null);

  useEffect(() => {
    const handleClick = event => {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target) &&
        (!iconRef || (iconRef.current && !iconRef.current.contains(event.target)))
      ) {
        onClickOutside && onClickOutside(event);
      }
    };
    document.addEventListener('mousedown', handleClick);
    return () => {
      document.removeEventListener('mousedown', handleClick);
    };
  }, [onClickOutside, iconRef]);

  return (
    <div ref={wrapperRef} className={className} style={style} {...rest}>
      {children}
    </div>
  );
};

export default OutSideClickHandler;
