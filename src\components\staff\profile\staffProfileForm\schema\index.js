import * as Yup from 'yup';

export const therapistValidationSchema = Yup.object().shape({
  image_file: Yup.mixed().notRequired().nullable(),

  firstName: Yup.string()
    .required('Required field')
    .min(2, 'Invalid Name')
    .max(100, 'Invalid Name')
    .test(
      'is-valid-first-name',
      'Enter a valid name (letters only).',
      value => !value || /^[\u0600-\u06FFa-zA-Z]+(?: [\u0600-\u06FFa-zA-Z]+){0,2}$/.test(value)
    ),
  lastName: Yup.string()
    .required('Required field')
    .min(2, 'Invalid Name')
    .max(100, 'Invalid Name')
    .test(
      'is-valid-last-name',
      'Enter a valid name (letters only).',
      value => !value || /^[\u0600-\u06FFa-zA-Z]+(?: [\u0600-\u06FFa-zA-Z]+){0,2}$/.test(value)
    ),

  email: Yup.string()
    .email('Invalid email.')
    .matches(/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i, 'Invalid email.')
    .max(255, 'Maximum 255 characters are allowed.')
    .nullable()
    .notRequired(),

  country_code: Yup.string()
    .matches(/^\+\d{1,4}$/, 'Invalid country code')
    .required('Required field.'),

  phoneNumber: Yup.string()
    .matches(/^\d{6,15}$/, 'Invalid phone number')
    .required('Required field.'),
  role: Yup.object()
    .shape({
      label: Yup.string().required(),
      value: Yup.string().required(),
    })
    .required('Required field.'),
  licenseNumber: Yup.number()
    .nullable()
    .transform((value, original) => (original === '' ? null : value))
    .typeError('License Number must be a valid number'),
});
