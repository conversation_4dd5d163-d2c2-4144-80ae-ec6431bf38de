import tw from 'twin.macro';
import CloseIcon from '@assets/svgs/close-icon.svg';
const ModalTitle = ({ title, customStyle, handleClose, imageCustomeStyle }) => {
  return (
    <div
      css={[
        customStyle,
        tw`px-8 py-6  text-[18px] text-text_primary font-bold w-full flex items-center justify-between rounded-t-[12px]`,
      ]}
    >
      <p>{title}</p>
      {handleClose && (
        <button onClick={handleClose} aria-label="Close">
          <img src={CloseIcon} alt="close icon" css={[imageCustomeStyle]} />
        </button>
      )}
    </div>
  );
};

export default ModalTitle;
