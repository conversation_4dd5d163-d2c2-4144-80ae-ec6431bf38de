import React from 'react';
import { Controller } from 'react-hook-form';
import DatePicker from '@/components/shared/datePicker/datePicker';

const ControlledCalendar = ({ name, control, datePickerProps, errors }) => {
  return (
    <>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <DatePicker
            {...field}
            {...datePickerProps}
            inputClassName="!border-border_stroke !px-[16px] !py-[12px] font-medium text-[14px] hover:bg-neutral_100 focus:bg-neutral_100 focus:outline-none focus:caret-transparent"
          />
        )}
      />
      {errors.name && <span css={tw`text-error text-sm`}>{errors.name.message}</span>}
    </>
  );
};

export default ControlledCalendar;
