import Sidebar from '@/components/sidebar';
import { Outlet } from 'react-router-dom';
import Navbar from '../navbar';
import 'twin.macro';

/**
 * Layout section component that includes sidebar and main content area
 * @param {Object} props
 * @param {string} props.CollapseIcon - URL/path to the collapse/expand icon for the sidebar
 * @param {string} props.Logo - URL/path to the main logo image displayed when sidebar is expanded
 * @param {string} props.SmallLogo - URL/path to the smaller logo variant displayed when sidebar is collapsed
 * @param {Array} props.sidebarList - List of sidebar menu items containing id, title, and icon properties
 * @param {boolean} props.isCollapsed - Whether the sidebar is currently in collapsed state
 * @param {Function} props.setIsCollapsed - Function to toggle the sidebar collapsed state
 * @param {ReactNode} props.footerSection - React node for the footer section
 * @param {string} props.currentRoute - Current active route for sidebar highlighting
 */
const LayoutSection = ({
  CollapseIcon,
  Logo,
  sidebarList,
  isCollapsed,
  setIsCollapsed,
  SmallLogo,
  footerSection,
  currentRoute = '/',
}) => {
  return (
    <div tw="flex overflow-y-auto w-full h-screen">
      <Sidebar
        CollapseIcon={CollapseIcon}
        Logo={isCollapsed ? SmallLogo : Logo}
        sidebarList={sidebarList}
        isCollapsed={isCollapsed}
        setIsCollapsed={setIsCollapsed}
        footerSection={footerSection}
        isSelectedRoute={currentRoute}
      />
      <div tw="flex overflow-x-hidden flex-col w-full" className="element">
        <Navbar
          CollapseIcon={CollapseIcon}
          isCollapsed={isCollapsed}
          setIsCollapsed={setIsCollapsed}
        />
        <div tw="w-full min-h-[93%]">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default LayoutSection;
