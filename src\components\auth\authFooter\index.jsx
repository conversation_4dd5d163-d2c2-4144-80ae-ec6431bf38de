import 'twin.macro';

const AuthFooter = ({ onClick, normalText, linkText }) => {
  return (
    // <div tw="sticky bottom-0  text-center left-0 bg-Primary_50 w-full py-[24px]">
    <div tw="text-gray-500 sticky bottom-0  text-center left-0 bg-Primary_50 w-full py-[24px]">
      <p tw="font-['Inter'] font-[1.125rem] font-[500] text-text_secondary">
        {normalText}
        <span
          onClick={onClick}
          tw="font-['Inter'] font-[1.125rem] ms-1 font-semibold text-Primary_600 cursor-pointer "
        >
          {linkText}
        </span>
      </p>
    </div>
  );
};
export default AuthFooter;
