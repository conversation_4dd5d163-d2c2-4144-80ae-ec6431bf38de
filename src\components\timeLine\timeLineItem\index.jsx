import { formatDistanceToNow, parseISO, isValid, format } from 'date-fns';
import 'twin.macro';

const TimelineItem = ({ item, isLast, isOlder }) => {
  let baseDate;

  // Try to parse ISO string first
  if (typeof item.date === 'string') {
    baseDate = parseISO(item.date);
    if (!isValid(baseDate)) {
      // Try to parse as Date constructor (handles relative like '37 minutes ago' poorly, but fallback)
      baseDate = new Date(item.date);
    }
  } else if (item.date instanceof Date) {
    baseDate = item.date;
  } else {
    baseDate = new Date(item.date);
  }

  // If still invalid, fallback to now
  if (!isValid(baseDate)) {
    baseDate = new Date();
  }

  // Format the date based on the `isOlder` prop
  const formattedDate = isOlder
    ? format(baseDate, 'MMM d, yyyy') // Format as "Jul 7, 2025"
    : formatDistanceToNow(baseDate, { addSuffix: true }); // Format as relative time

  return (
    <div tw="flex relative gap-3 items-start last:pb-0">
      <div tw="flex flex-col gap-[0.93rem] items-center">
        <img src={item.icon} alt="image" tw="w-[1.2rem] h-[1.2rem] object-fill" />
        {!isLast && (
          <div tw="flex-1 w-[0.133rem] relative -top-[0.066rem] rounded-[1.33rem] min-h-[1.33rem] bg-stroke" />
        )}
      </div>
      <div tw="flex-1">
        <div tw="flex items-center gap-1 text-[0.84rem]">
          <span tw="font-medium">{item.actor}</span>
          <span>{item.action}</span>
          {item.target && <span tw="font-medium">{item.target}</span>}
          {item.details && <span>{item.details}</span>}
        </div>
      </div>
      <div tw="text-[0.75rem] text-text_secondary">{formattedDate}</div>
    </div>
  );
};

export default TimelineItem;
