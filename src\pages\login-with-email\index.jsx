import { useLocation, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
// import { AuthApis } from "@/apis/auth/api";
import { useWindowDimensions } from '@/hooks';
import { screenSizes } from '@/constants/constants';
import { showError } from '@/libs/react.toastify';
import AuthLayout from '@/components/auth/authLayout';
import { useAuth } from '@/zustand/auth-store';
import LoadingElement from '@/components/shared/loadingElement';
import 'twin.macro';

const EmailLogin = () => {
  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const navigate = useNavigate();
  const searchQuery = query.get('refresh_token');
  const emailQuery = query.get('email');
  const { clientWindowWidth } = useWindowDimensions();
  const addToken = useAuth(state => state.addUserEmail);

  const handleLogin = async () => {
    try {
      if (clientWindowWidth > screenSizes.md_custom) {
        // const { data } = await AuthApis.EmailLogin({
        //   refresh: searchQuery,
        // });
        // addToken(data.access);
        localStorage.setItem('rehab-email', JSON.stringify(emailQuery));
        navigate('/motion-caption', { replace: true });
      }
    } catch (error) {
      console.log('error', error);
      showError(error.response.data.detail);
      // if (error.response.status === 401) {
      //   navigate("/link-expire", { replace: true });
      // }
    }
  };

  useEffect(() => {
    if (searchQuery) {
      handleLogin();
    }
  }, [searchQuery]);

  return (
    <AuthLayout
      authSection={
        <div tw="flex justify-around items-center">
          <LoadingElement width={80} height={80} />
        </div>
      }
      footerLink={'/sign-up'}
      footerLinkText={'Let us know'}
      footerNormalText={"Can't log in?"}
      hasAuthFooter
      isLogIn
      subtitle={<></>}
      title={''}
      handelSubmit={() => {}}
    />
  );
};
export default EmailLogin;
