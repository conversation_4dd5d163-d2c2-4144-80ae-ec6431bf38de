import React from 'react';
import tw from 'twin.macro';
import <PERSON><PERSON><PERSON>r from '@/components/patient/SectionContainer';
import Input from '@/components/shared/input';
import GenericSelect from '@/components/shared/select';
import PhoneNumber from '@/components/phoneNumber';
import 'twin.macro';

// Make sure to pass these props from the parent component
function StaffClinicInformation({
  control,
  register,
  errors,
  touchedFields,
  formData,
  fieldsThPermistions,
}) {
  return (
    <SectionContainer customeClass={tw`grid grid-cols-2 gap-x-8 items-start gap-y-5 !py-8`}>
      {/* Email */}
      <Input
        disabled={!fieldsThPermistions.email}
        disabledStyle={tw`bg-disable cursor-not-allowed hover:bg-disable`}
        type="text"
        name="email"
        defaultValue={formData.email}
        placeholder={'Email Address'}
        labelStyle={tw`text-base `}
        labelRootElementStyle={tw`!mb-2`}
        inputStye={tw`text-[0.8rem] py-4`}
        register={register}
        label={'Email'}
        errorMessage={errors?.email?.message}
        containerSTyle={tw`bg-white border-border_stroke disabled:bg-disable disabled:cursor-not-allowed`}
      />

      {/* PHONE NUMBER */}
      <PhoneNumber
        control={control}
        defaultCountry={'EG'}
        label={'Phone number'}
        name={'phoneNumber'}
        phoneName={'phoneCh'}
        placeholder={'Phone number'}
        register={register}
        lableClasses={tw`text-base`}
        wrapperElementClasses={tw`gap-2 h-auto`}
        inputCalsses={tw`py-4`}
        errors={errors?.phoneNumber?.message}
        disable={!fieldsThPermistions.phoneNumber}
      />

      {/* License number  */}
      <Input
        type="text"
        label={'License number'}
        labelStyle={tw`text-base`}
        name="licenseNumber"
        placeholder="00-**********"
        register={register}
        disabled={!fieldsThPermistions.licenseNumber}
        errorMessage={
          errors.licenseNumber && touchedFields.licenseNumber ? errors.licenseNumber.message : ''
        }
        labelRootElementStyle={tw`!mb-2`}
        inputStye={tw`rounded-md focus:outline-none focus:ring-2 focus:ring-Primary py-4`}
        containerSTyle={tw`bg-white border-border_stroke disabled:bg-disable disabled:cursor-not-allowed`}
        disabledStyle={tw`bg-disable cursor-not-allowed hover:bg-disable`}
      />
      {/* Role */}
      <GenericSelect
        control={control}
        label="Role"
        name="role"
        placeholder="Select Role"
        options={[
          { label: 'Therapist', value: 'Therapist' },
          { label: 'Admin', value: 'Admin' },
        ]}
        errorMessage={errors.role && touchedFields.role ? errors.role.message : ''}
        labelStyle={tw`font-medium text-base`}
        labelContainerStyle={tw`mb-0`}
        customStyles={{
          control: () => ({
            padding: '.5rem',
          }),
        }}
        inputLength={7}
        rootElementStyle={tw`gap-2`}
        disabledStyle={tw`bg-disable cursor-not-allowed hover:bg-disable`}
        disable={!fieldsThPermistions.role}
      />
    </SectionContainer>
  );
}

export default StaffClinicInformation;
