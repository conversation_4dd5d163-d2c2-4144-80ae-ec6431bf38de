import React from 'react';
import tw from 'twin.macro';

/**
 * Simple ToolTip component
 * @param {Object} props
 * @param {string} props.text - The tooltip text to display
 */
const ToolTip = ({ text }) => (
  <div
    css={tw`
        absolute
        left-full
        top-[30%]
        ml-[6px]
        w-max
        max-w-[250px]
        px-[10px] py-[6px]
        bg-[#2E2E2E]
        text-white
        text-[14px]
        rounded-[5px]
        opacity-0
        pointer-events-none
        transition-opacity
        duration-200
        z-30
        group-hover:opacity-100
    `}
  >
    {text}
  </div>
);

export default ToolTip;
