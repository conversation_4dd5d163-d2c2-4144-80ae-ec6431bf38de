import VISAIcon from '@assets/svgs/payment/Visa.svg';
import SEPAIcon from '@assets/svgs/payment/SEPA.svg';
import PaypalIcon from '@assets/svgs/payment/Paypal.svg';
import AMERICANIcon from '@assets/svgs/payment/Mastercard.svg';
import OTHERIcon from '@assets/svgs/payment/Amex.svg';
import PaymentCard from '../paymentCard';
import Radio from '@/components/shared/radio';
import { CardPaymentForm } from '../cardPaymentForm';
import { SepaPaymentForm } from '../sepaPaymentForm';
import { PayPalPaymentForm } from '../payPalPaymentForm';
import 'twin.macro';

// Payment method configuration
const PAYMENT_METHODS = [
  {
    id: 'card',
    label: 'Card',
    icons: [VISAIcon, AMERICANIcon, OTHERIcon],
  },
  {
    id: 'paypal',
    label: '',
    icons: [PaypalIcon],
  },
  {
    id: 'sepa',
    label: '',
    icons: [SEPAIcon],
  },
];

const PaymentMethods = ({
  watch,
  register,
  control,
  message,
  errors,
  showTitle = true,
  customStyle,
}) => {
  const selectedPaymentMethod = watch('paymentMethod');
  const renderPaymentForm = () => {
    switch (selectedPaymentMethod) {
      case 'card':
        return <CardPaymentForm errors={errors} register={register} watch={watch} />;
      case 'sepa':
        return (
          <SepaPaymentForm register={register} watch={watch} control={control} errors={errors} />
        );
      case 'paypal':
        return <PayPalPaymentForm errors={errors} />;
      default:
        return null;
    }
  };

  return (
    <PaymentCard
      showTitle={showTitle}
      title="Payment method"
      customStyle={customStyle}
      content={
        <div tw="space-y-6">
          <div tw="space-y-4">
            <div tw="flex flex-col gap-4">
              {PAYMENT_METHODS.map(method => (
                <Radio
                  key={method.id}
                  control={control}
                  name="paymentMethod"
                  value={method.id}
                  customable={
                    <div tw="flex gap-2 items-center text-[1rem]">
                      <label htmlFor={method.id}>{method.label}</label>
                      <div tw="flex gap-1 items-center">
                        {method.icons.map((icon, index) => (
                          <img key={index} src={icon} alt={`${method.label} icon`} />
                        ))}
                      </div>
                    </div>
                  }
                  rules={{ required: 'Please select a payment method' }}
                  sectionToRenderAfter={method.id === selectedPaymentMethod && renderPaymentForm()}
                />
              ))}
            </div>
          </div>
        </div>
      }
    />
  );
};

export default PaymentMethods;
