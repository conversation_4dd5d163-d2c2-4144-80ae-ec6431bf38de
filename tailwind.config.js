/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      screens: {
        xs: '480px',
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px',
        '3xl': '1706px',
        '4xl': '1920px',
        '6xl': '2400px',
      },
      colors: {
        stroke: '#D9DFE4',
        border_stroke: '#BDC4C9',
        text_primary: '#2E2E2E',
        text_secondary: '#656565',
        text_tertiary: '#5E738A',
        Primary_50: '#F7FAEB',
        Primary_100: '#EFF4D7',
        Primary_200: '#E0E9AF',
        Primary_300: '#D0DF86',
        Primary_400: '#C0D45E',
        Primary: '#C6D86E',
        Primary_600: '#8DA12B',
        Primary_700: '#6A7920',
        Primary_800: '#475016',
        neutral_50: '#F8F9FA',
        neutral_100: '#F5F5F5',
        neutral_200: '#EEF0F3',
        neutral_300: '#E4E7EB',
        info: '#285FF5',
        info_light: '#EEF3FF',
        warning: '#FC9D14',
        warning_light: '#FFF6E9',
        error: '#D12E3C',
        error_50: '#FFF1F2',
        error_100: '#FEE4E2',
        success: '#0B834F',
        success_50: '#EBFDF5',
        success_100: '#D1FADF',
        disable: '#E4E7EB',
      },
      borderWidth: {
        primary: '1px',
      },
      borderRadius: {
        primary: '1px',
        card: '12px',
      },
    },
  },
  plugins: [],
};
