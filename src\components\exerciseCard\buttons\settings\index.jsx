import React from 'react';
import SettingsIcon from '@assets/svgs/settings.svg';
import tw from 'twin.macro';

const Settings = ({ handleClick }) => (
  <button
    type="button"
    css={tw`relative w-[18px] h-[22px] p-0 border-none bg-none`}
    onClick={handleClick}
    onPointerDown={e => e.stopPropagation()}
  >
    <img
      src={SettingsIcon}
      alt="settings Icon"
      css={tw`absolute top-0 left-0 w-[18px] h-[22px]`}
      draggable={false}
    />
  </button>
);

export default Settings;
