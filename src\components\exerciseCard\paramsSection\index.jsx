import React from 'react';
import tw, { css } from 'twin.macro';

const ParamsSection = ({ register, errors, readOnly, handleSubmit, onSubmit }) => {
  // Helper to get outline style based on error and readonly
  const outlineStyle = hasError =>
    !readOnly
      ? hasError
        ? tw`focus:outline-none focus:ring focus:ring-error`
        : tw`focus:outline-none focus:ring focus:ring-Primary`
      : undefined;

  return (
    <div css={tw`flex justify-between items-center font-bold text-[12px]`}>
      <div css={tw`flex items-center gap-[6px]`}>
        <input
          type="number"
          {...register('setsCount')}
          onBlur={handleSubmit(onSubmit)}
          disabled={readOnly}
          onPointerDown={e => e.stopPropagation()}
          css={[
            tw`w-[22px] h-[22px] py-[4px] border border-stroke rounded-[4px] text-center truncate`,
            outlineStyle(errors.setsCount),
            css`
              /* Hide number input arrows */
              &::-webkit-outer-spin-button,
              &::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
              }
              &[type='number'] {
                -moz-appearance: textfield;
              }
            `,
          ]}
        />
        <p tw="text-[0.7rem]">Sets</p>
      </div>
      <p tw="text-[0.7rem]">X</p>
      <div css={tw`flex items-center gap-[6px]`}>
        <input
          type="number"
          {...register('repsCount')}
          onBlur={handleSubmit(onSubmit)}
          disabled={readOnly}
          onPointerDown={e => e.stopPropagation()}
          onClick={e => e.stopPropagation()}
          css={[
            tw`w-[22px] h-[22px] py-[4px] border border-stroke rounded-[4px] text-center truncate`,
            outlineStyle(errors.repsCount),
            css`
              /* Hide number input arrows */
              &::-webkit-outer-spin-button,
              &::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
              }
              &[type='number'] {
                -moz-appearance: textfield;
              }
            `,
          ]}
        />
        <p tw="text-[0.7rem]">Reps</p>
      </div>
    </div>
  );
};

export default ParamsSection;
