import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from 'framer-motion';
import Breadcrumb from '../breadcrumb';
import tw from 'twin.macro';
/**
 * A tab navigation component that displays a list of tabs and corresponding content.
 * Syncs the selected tab with the URL query parameter `?tab=...`.
 *
 * @component
 *
 * @param {string[]} props.tabsKeys - Array of tab labels used for display and URL sync
 * @param {JSX.Element[]} props.tabsContent - Array of JSX elements, each representing the content of a tab
 *
 * The component will:
 * - Default to the first tab if the URL doesn't contain a valid `?tab=...` query
 * - Automatically update the URL query when a tab is clicked
 * - React to external changes in the `?tab=...` query (e.g. browser navigation)
 *
 * @returns {JSX.Element} - Rendered tab navigation and selected tab content
 */
export default function TabComponent({ tabsKeys = [], tabsContent = [], breadcrumbItems }) {
  const [searchParams, setSearchParams] = useSearchParams();
  const currentTabParam = searchParams.get('tab');
  const initialTabIndex =
    tabsKeys.indexOf(currentTabParam) !== -1 ? tabsKeys.indexOf(currentTabParam) : 0;

  const [selectedTabIndex, setSelectedTabIndex] = useState(initialTabIndex);
  const [direction, setDirection] = useState(0);

  // Update URL when tab changes
  const handleTabChange = idx => {
    const tabKey = tabsKeys[idx];
    setDirection(idx > selectedTabIndex ? 1 : -1);
    setSelectedTabIndex(idx);
    setSearchParams({ tab: tabKey });
  };

  // Update tab index if URL param changes externally
  useEffect(() => {
    const tab = searchParams.get('tab');
    const idx = tabsKeys.indexOf(tab);
    if (idx !== -1 && idx !== selectedTabIndex) {
      setDirection(idx > selectedTabIndex ? 1 : -1);
      setSelectedTabIndex(idx);
    }
  }, [searchParams, tabsKeys]);

  // Animation variants for tab content
  const tabVariants = {
    enter: direction => ({
      x: direction > 0 ? 100 : -100,
      opacity: 0,
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
    },
    exit: direction => ({
      zIndex: 0,
      x: direction < 0 ? 100 : -100,
      opacity: 0,
    }),
  };

  // Animation variants for tab indicator
  const indicatorVariants = {
    initial: { scaleX: 0 },
    animate: {
      scaleX: 1,
      transition: { duration: 0.15, ease: 'easeOut' },
    },
    exit: { scaleX: 0 },
  };

  return (
    <div tw="flex flex-col h-full">
      <div tw=" bg-[#f8f9fa]  ">
        {breadcrumbItems?.length > 0 && (
          <div tw="px-6">
            <Breadcrumb items={breadcrumbItems} />
          </div>
        )}
        <nav className="px-6 border-b-2 border-[#D9D9D9] flex space-x-6 text-text_secondary  ">
          {tabsKeys.map((tab, idx) => (
            <div
              key={tab}
              onClick={() => handleTabChange(idx)}
              css={[
                tw`p-2.5 text-[15px] font-semibold transition-colors duration-200 relative cursor-pointer`,
                selectedTabIndex === idx ? tw`text-Primary_600` : tw`hover:text-Primary_600`,
              ]}
            >
              {tab}
              {selectedTabIndex === idx && (
                <motion.div
                  tw="absolute h-[2px] -left-[1px] -bottom-[2px] bg-Primary_600 origin-left"
                  style={{ width: '100%' }}
                  variants={indicatorVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                />
              )}
            </div>
          ))}
        </nav>
      </div>
      <div tw="h-[90%] w-full overflow-x-hidden overflow-y-auto relative">
        <AnimatePresence initial={false} custom={direction} mode="wait">
          <motion.div
            key={selectedTabIndex}
            custom={direction}
            variants={tabVariants}
            style={{ paddingInline: 24, paddingBlock: 24 }}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
              x: { type: 'spring', stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 },
            }}
            tw="absolute inset-0"
          >
            {tabsContent[selectedTabIndex]}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}
