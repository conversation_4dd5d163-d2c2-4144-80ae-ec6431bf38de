import React from 'react';
import { useForm, Controller, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import PhoneInput from '@components/shared/phoneInput';
import tw from 'twin.macro';
import GenericSelect from '@components/shared/select';
import { schemaAddPatient } from './schema';
import { InitialAddPatient } from './module';
import PrimaryButton from '@/components/shared/primaryButton';
import Input from '@components/shared/input';
import { useEffect } from 'react';
import SecondaryButton from '../shared/secondaryButton';
import UnitCompound from './unitCompound';
import ControlledCalendar from '../controlledCalendar';
import { gender_opetions, height_options, weight_options } from '@/constants/constants';

import PhoneNumber from '../phoneNumber';

const AddPatient = ({ customOnSubmit, customOnCancel }) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid, touchedFields },
    control,
    trigger,
  } = useForm({
    defaultValues: InitialAddPatient,
    resolver: yupResolver(schemaAddPatient),
    mode: 'onChange',
  });

  const weightUnit = useWatch({ control, name: 'weightUnit' });
  const heightUnit = useWatch({ control, name: 'heightUnit' });

  useEffect(() => {
    // Whenever weightUnit changes, re-validate the weight field
    trigger('weight');
  }, [weightUnit, trigger]);

  useEffect(() => {
    trigger('height');
  }, [heightUnit, trigger]);

  const onSubmit = data => {
    console.log({
      data,
    });
    customOnSubmit && customOnSubmit(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} css={tw` flex flex-col`}>
      <div css={tw`p-8 pt-4 flex flex-col gap-6`}>
        {/*firstName & lastName*/}
        <div css={tw`flex gap-5`}>
          <div css={tw`flex-1`}>
            <Input
              type="text"
              label="First name"
              name="firstName"
              placeholder="First Name"
              register={register}
              errorMessage={
                errors.firstName && touchedFields.firstName ? errors.firstName.message : ''
              }
              inputStye={tw`rounded-[6px] focus:outline-none focus:ring-2 focus:ring-Primary`}
            />
          </div>
          <div css={tw`flex-1`}>
            <Input
              type="text"
              label="Last name"
              name="lastName"
              placeholder="Last Name"
              register={register}
              errorMessage={
                errors.lastName && touchedFields.lastName ? errors.lastName.message : ''
              }
              inputStye={tw`rounded-[6px] focus:outline-none focus:ring-2 focus:ring-Primary`}
            />
          </div>
        </div>
        {/*phoneNumber & emailAddress*/}
        <div css={tw`w-full flex gap-5 items-start`}>
          <div css={tw`flex-1 flex flex-col gap-1`}>
            <PhoneNumber
              control={control}
              register={register}
              name="localNumber"
              placeholder="000-000-000"
              phoneName="countryCode"
              defaultCountry="EG"
              label="Phone number"
              errors={
                errors.localNumber && touchedFields.localNumber ? errors.localNumber.message : ''
              }
              inputType="text"
              hideErrorMessage
              inputCustomStyle={tw`h-[2.98rem]`}
            />
          </div>
          {/*emailAddress*/}
          <div css={tw`flex-1`}>
            <Input
              type="text"
              label={
                <>
                  Email address
                  <span css={tw`text-text_tertiary font-light text-[13px]`}>(optional)</span>
                </>
              }
              name="email"
              placeholder="<EMAIL>"
              register={register}
              errorMessage={errors.email?.message}
              inputStye={tw`h-[2.933rem] rounded-[6px] focus:outline-none focus:ring-2 focus:ring-Primary`}
            />
          </div>
        </div>
        {/*gender & weight & height */}
        <div css={tw`w-full flex gap-5`}>
          {/* gender */}
          <div css={tw`flex-1`}>
            <GenericSelect
              control={control}
              label="Gender"
              name="gender"
              placeholder="Select gender"
              options={gender_opetions}
              errorMessage={errors.gender && touchedFields.gender ? errors.gender.message : ''}
              labelStyle={tw`font-medium text-[0.95rem]`}
              labelContainerStyle={tw`mb-0`}
              customStyles={{
                control: () => ({
                  padding: '8px',
                }),
              }}
              inputLength={7}
            />
          </div>
          {/* height */}
          <UnitCompound
            labelText="Height"
            labelTooltipText="height toolTip"
            control={control}
            selectorName="heightUnit"
            selectorPlaceHolder="cm"
            selectorOptions={height_options}
            inputName="height"
            inputPlaceholder="0 cm"
            register={register}
            errors={errors}
            touchedFields={touchedFields}
          />
          {/* weight */}
          <UnitCompound
            labelText="Weight"
            labelTooltipText="weight toolTip"
            control={control}
            selectorName="weightUnit"
            selectorPlaceHolder="kg"
            selectorOptions={weight_options}
            inputName="weight"
            inputPlaceholder="0 kg"
            register={register}
            errors={errors}
            touchedFields={touchedFields}
          />
        </div>
        {/* dateOfBirth & condition*/}
        <div css={tw`flex gap-5`}>
          {/* dateOfBirth */}
          <div css={tw`flex-[1_1_0%]`}>
            <ControlledCalendar
              name="dateOfBirth"
              control={control}
              datePickerProps={{
                placeholder: 'mm/dd/yyyy',
                label: 'Date of Birth',
                labelClassName: '!font-medium !text-[0.95rem] !mb-1',
                dateFormat: {
                  day: 'numeric',
                  month: 'numeric',
                  year: 'numeric',
                },
                calendarClassName: 'bottom-[0%] left-[calc(100%+8px)] !w-[160%]',
                maxDate: new Date(),
              }}
              errors={errors}
            />
          </div>
          {/* condition */}
          <div css={tw`flex-[2_1_0%]`}>
            <Input
              type="text"
              label={
                <>
                  Condition
                  <span css={tw`text-text_tertiary font-light text-[13px]`}>(optional)</span>
                </>
              }
              name="condition"
              placeholder="A short description of the condition"
              register={register}
              errorMessage={errors.condition?.message}
              inputStye={tw`h-[3rem] rounded-[6px] focus:outline-none focus:ring-2 focus:ring-Primary`}
            />
          </div>
        </div>
      </div>
      {/*action buttons*/}
      <div className="px-8 py-6 flex gap-3 bg-neutral_50 border-t border-stroke rounded-b-[12px]">
        <PrimaryButton
          text="Save Patient"
          type="submit"
          disable={!isValid}
          customStyle={tw`flex-1 !py-[10px] !rounded-[6px] !font-medium`}
        />
        <SecondaryButton
          text="Cancel"
          type="button"
          handelClick={customOnCancel}
          otherStyle={tw`flex-1 !py-[10px] !rounded-[6px] !font-medium bg-white !border-border_stroke text-text_primary text-sm`}
        />
      </div>
    </form>
  );
};

export default AddPatient;
