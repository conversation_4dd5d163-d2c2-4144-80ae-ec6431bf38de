import right from '@/assets/svgs/payment/right.svg';
import 'twin.macro';

const RulesSection = ({ listOfRules }) => {
  return (
    <div tw="py-[20px] border-b border-[#CFCFCF]">
      <p tw="font-medium text-[1.125rem] mb-2">Subscribe now for a 14 day trial!</p>
      <div tw="flex flex-col gap-1">
        {listOfRules?.map(item => (
          <div tw="flex gap-3 items-center" key={item.id}>
            <img alt={item.title} src={right} />
            <p key={item.id} tw="text-[.875rem] font-normal text-text_primary">
              {item.title}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RulesSection;
