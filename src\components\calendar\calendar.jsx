import React, { useState } from 'react';
import { Calendar as <PERSON><PERSON><PERSON><PERSON><PERSON>, momentLocalizer, Views } from 'react-big-calendar';
import moment from 'moment';
import YearMonthPicker from '../shared/datePicker/yearMonthPicker';
import tw from 'twin.macro';
import OutSideClickHandler from '../outSideClickHandler';
import CustomDay from './customDay';
import CustomDayName from './customDayName';
import CustomToolbar from './customToolBar';

/**
 * Calendar Component
 *
 * A customizable calendar component that displays events and daily status indicators
 *
 * @param {Object} props
 * @param {Array} props.events - Array of event objects to display on the calendar
 * @param {string} props.events[].id - Unique identifier for the event
 * @param {string} props.events[].title - Title of the event
 * @param {Date} props.events[].start - Start date and time of the event
 * @param {Date} props.events[].end - End date and time of the event
 * @param {string} props.events[].resource - Status of the event (e.g., "Completed", "Missed")
 * @param {Array} props.dailyStatus - Array of daily status objects
 * @param {string} props.dailyStatus[].date - Date string in format "YYYY-MM-DD"
 * @param {string} props.dailyStatus[].status - Status label (e.g., "Completed", "Missed")
 * @param {string} props.dailyStatus[].icon - Icon URL for the status
 * @param {boolean} props.dailyStatus[].hasIcon - Whether to show the icon
 * @param {Object} props.dailyStatus[].color - Tailwind color for the cell background
 * @param {React.ReactNode} [props.dailyStatus[].customComponent] - Optional custom component to render
 * @param {string} props.grayIcon - URL of the gray arrow icon used in the toolbar
 * @param {Function} props.onSelectEvent - Callback when an event is selected, receives the event object
 * @param {Date} [props.currentDate] - Current date to display in the calendar
 * @param {Function} [props.onDateChange] - Callback when date is changed, receives the new date
 * @param {number} [props.selectedEventId] - ID of the currently selected event
 * @param {Function} [props.onSelectSlot] - Callback when a date slot is selected
 * @param {string} [props.pickerWidth] - Width of the year-month picker
 */
const CalendarComponent = ({
  events = [],
  dailyStatus = [],
  grayIcon,
  onSelectEvent = () => {},
  currentDate: externalCurrentDate,
  onDateChange: externalOnDateChange,
  selectedEventId,
  onSelectSlot,
  pickerWidth,
  joinedDate,
  selectedDayDate,
  rollingAdherence,
  monthes,
}) => {
  const localizer = momentLocalizer(moment);
  const [internalCurrentDate, setInternalCurrentDate] = useState(new Date());
  const [isYearMonthPickerOpen, setIsYearMonthPickerOpen] = useState(false);

  // Determine whether to use internal or external state
  const currentDate = externalCurrentDate || internalCurrentDate;
  const isSameMonthAfterToday =
    moment(currentDate).isSame(moment(), 'month') && moment(currentDate).isAfter(moment(), 'day');

  const setCurrentDate = date => {
    if (externalOnDateChange) {
      externalOnDateChange(date);
    } else {
      setInternalCurrentDate(date);
    }
  };

  // Function to get status for a specific date
  const getStatusForDate = date => {
    const dateStr = moment(date).format('YYYY-MM-DD');
    const statusData = dailyStatus.find(item => item.date === dateStr);
    return statusData || null;
  };

  // Define available views
  const calendarViews = {
    month: true,
    week: false,
    day: false,
    agenda: false,
  };

  // Custom event styling based on status
  const eventStyleGetter = event => {
    let style = {
      borderRadius: '0px',
      height: '100%',
      color: 'white',
      border: '0px',
    };

    if (event.resource === 'Completed') {
      style.backgroundColor = '#8DA12B'; // Green for completed
    } else if (event.resource === 'Missed') {
      style.backgroundColor = '#D32F2F'; // Red for missed
    } else {
      style.backgroundColor = '#1976D2'; // Blue for default
    }

    // Add border for selected event
    if (selectedEventId && event.id === selectedEventId) {
      style.border = '3px solid #007bff';
      style.boxShadow = '0 0 8px rgba(0, 123, 255, 0.5)';
    }

    return {
      style: style,
    };
  };

  // Add dayPropGetter function
  const dayPropGetter = date => {
    const isCurrentMonth = moment(date).isSame(moment(currentDate), 'month');
    const isToday = moment(date).isSame(moment(), 'day');
    const isPast = moment(date).isBefore(moment(), 'day');
    const isFuture = moment(date).isBefore(moment(), 'day');

    return {
      style: {
        color: isCurrentMonth
          ? isToday
            ? '#8DA12B'
            : isPast
              ? '#A0A0A0'
              : isFuture
                ? '#2E2E2E30'
                : 'black'
          : '#A0A0A0',
        fontWeight: isToday ? 'bold' : 'normal',
      },
    };
  };

  // Handle year and month selection from the picker
  const handleYearMonthChange = date => {
    setCurrentDate(date);
    setIsYearMonthPickerOpen(false);
  };

  // Toggle the year-month picker
  const toggleYearMonthPicker = () => {
    setIsYearMonthPickerOpen(!isYearMonthPickerOpen);
  };

  // Handle event selection
  const handleEventSelect = event => {
    onSelectEvent(event);
  };

  return (
    <div tw="relative bg-white " css={tw`min-h-[40rem] h-[40rem] `}>
      {isYearMonthPickerOpen && (
        <OutSideClickHandler onClickOutside={() => setIsYearMonthPickerOpen(false)}>
          <div tw="absolute top-[3rem] left-1/2 z-50 transform -translate-x-1/2">
            <YearMonthPicker
              value={currentDate}
              onChange={handleYearMonthChange}
              isOpen={isYearMonthPickerOpen}
              onOpenChange={setIsYearMonthPickerOpen}
              closeOnSelect={true}
              pickerWidth={pickerWidth}
              standalone={true}
              joinedDate={joinedDate}
              monthes={monthes}
            />
          </div>
        </OutSideClickHandler>
      )}

      <BigCalendar
        localizer={localizer}
        events={events}
        startAccessor="start"
        endAccessor="end"
        date={currentDate}
        onNavigate={date => {
          setCurrentDate(date);
        }}
        views={calendarViews}
        defaultView={Views.MONTH}
        eventPropGetter={eventStyleGetter}
        dayPropGetter={dayPropGetter}
        onSelectEvent={handleEventSelect}
        onSelectSlot={vlaue => {
          onSelectSlot(vlaue);
        }}
        selectable={true}
        components={{
          toolbar: toolbarProps => (
            <CustomToolbar
              {...toolbarProps}
              grayIcon={grayIcon}
              onDateChange={setCurrentDate}
              onMonthYearClick={toggleYearMonthPicker}
              joinedDate={joinedDate}
              rollingAdherence={rollingAdherence}
              isYearMonthPickerOpen={isYearMonthPickerOpen}
            />
          ),
          header: CustomDayName,
          dateCellWrapper: dateProps => {
            const dateStatus = getStatusForDate(dateProps.value);
            return (
              <CustomDay
                color={dateStatus ? dateStatus?.color : null}
                hasIcon={dateStatus ? dateStatus?.hasIcon : false}
                icon={dateStatus ? dateStatus?.icon : null}
                customComponent={dateStatus?.customComponent}
                selectedDayDate={selectedDayDate}
                currentDate={currentDate}
                {...dateProps}
              />
            );
          },
        }}
        css={{
          '.rbc-month-view': {
            border: '1px solid #D9D9D9',
            borderRadius: '0.5rem',
            overflow: 'hidden',
          },
          '.rbc-month-row': {
            // flexBasis: 'var(--rbc-month-cell-height, 6rem)',
            borderBottom: '1px solid #2E2E2E',
            '&:last-child': {
              border: 'none',
            },
          },
          '.rbc-month-row + .rbc-month-row': { border: 'none', borderBottom: '1px solid #2E2E2E' },
          '.rbc-header': {
            padding: '0',
          },
          '.rbc-date-cell': {
            padding: '0.5rem',
            textAlign: 'center',
            color: isSameMonthAfterToday ? '#2E2E2E30' : '#2E2E2E',
            fontWeight: 500,
            fontSize: '.75rem',
          },
          '.rbc-date-cell.rbc-now': {
            color: '#6A7920',
          },
          '.rbc-off-range-bg': {
            backgroundColor: '#F8F9FA',
          },
          '.rbc-off-range': {
            color: '#2E2E2E30',
            fontWeight: 500,
            fontSize: '.75rem',
          },
          '.rbc-today': {
            backgroundColor: 'transparent',
          },
          '.rbc-event.rbc-selected': {
            backgroundColor: 'inherit',
          },
        }}
      />
    </div>
  );
};

export default CalendarComponent;
