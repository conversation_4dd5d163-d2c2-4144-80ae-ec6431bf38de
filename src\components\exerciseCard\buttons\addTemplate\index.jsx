import React from 'react';
import AddTemplateIcon from '@assets/svgs/add-template.svg';
import AddTemplateIconHover from '@assets/svgs/add-template-hover.svg';
import tw from 'twin.macro';

const AddTemplate = ({ handleClick }) => (
  <button
    type="button"
    css={tw`relative w-[18px] h-[22px] p-0 border-none bg-none`}
    className="group"
    onClick={e => {
      e.stopPropagation();
      handleClick();
    }}
  >
    <img
      src={AddTemplateIcon}
      alt="duplicate Icon"
      css={tw`absolute top-0 left-0 w-[18px] h-[22px] transition-opacity duration-300 opacity-100 group-hover:opacity-0`}
      draggable={false}
    />
    <img
      src={AddTemplateIconHover}
      alt="duplicate Icon Hover"
      css={tw`absolute top-0 left-0 w-[18px] h-[22px] transition-opacity duration-300 opacity-0 group-hover:opacity-100`}
      draggable={false}
    />
  </button>
);

export default AddTemplate;
