import Visa from '@/assets/svgs/settings/visa icon.svg';
import Master from '@/assets/svgs/settings/master.svg';
import Paypal from '@/assets/svgs/settings/paypal.svg';

export const MOADL_VIEW = {
  ADD_STAFF: 'ADD STAFF',
  PAYMENT_VIEW: 'PAYMENT_VIEW',
  SUCCESS_MODAL: 'SUCCESS_MODAL',
};
export const fieldsThPermistions = {
  firstName: true,
  lastName: true,
  email: false,
  phoneNumber: true,
  licenseNumber: false,
  role: false,
};
export const paymentMethodsCards = [
  {
    id: '1',
    title: 'Visa ending in 3233',
    expire: 'Expires 07/27',
    isDefault: true,
    imageUrl: Visa,
    isExpires: false,
  },
  {
    id: '2',
    title: 'Mastercard ending in 1234',
    expire: 'Expires 01/26',
    isDefault: false,
    imageUrl: Master,
    isExpires: false,
  },
  {
    id: '3',
    title: 'Account Name',
    // expire: 'Expires 07/27',
    isDefault: false,
    imageUrl: Paypal,
    isExpires: false,
  },
  {
    id: '4',
    title: 'Account Name',
    // expire: 'Expires 07/27',
    isDefault: false,
    imageUrl: Paypal,
    isExpires: false,
  },
  {
    id: '5',
    title: 'Account Name',
    // expire: 'Expires 07/27',
    isDefault: false,
    imageUrl: Paypal,
    isExpires: false,
  },
];

export const insightsData = [
  {
    title: 'Total Patients',
    color: '#F5F5F5',
    insight: '20',
    unit: 'Patients',
  },
  {
    title: 'Active Patients',
    color: '#EBFDF5',
    insight: '17',
    unit: 'Patients',
    tooltipText: 'This is Active Patients insght',
  },
  {
    title: 'Inactive Patients',
    color: '#F5F5F5',
    insight: '3',
    unit: 'Patients',
    tooltipText: 'This is Inactive Patients insght',
  },
  {
    title: 'High-risk',
    color: '#FFF1F2',
    insight: '7',
    unit: 'Patients',
    tooltipText: 'This is High-risk insght',
  },
  {
    title: 'High adherence',
    insight: '15',
    unit: 'Patients',
    color: '#F5F5F5',
  },
  {
    title: 'Low adherence',
    color: '#F5F5F5',
    insight: '2',
    unit: 'Patients',
  },
  {
    title: 'Total Prescriptions',
    color: '#F5F5F5',
    insight: '27',
    unit: 'Prescriptions',
  },
  {
    title: 'Active Prescriptions',
    color: '#F5F5F5',
    insight: '19',
    unit: 'Prescriptions',
  },
];
