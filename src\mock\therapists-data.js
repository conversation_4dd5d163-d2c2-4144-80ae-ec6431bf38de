const staffData = [
  {
    clinic_id: 1,
    therapists: [
      {
        id: 1,
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        Name: '<PERSON>',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2023-05-10',
        'Last activity': '2024-04-05',
        phone_number: '0101234567891',
        'License Number': '00-**********',
        Role: 'Clinic Owner',
        'No of patients': '10',
      },
      {
        id: 2,
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        Name: '<PERSON>',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2022-11-15',
        'Last activity': '2024-05-01',
        phone_number: '0102345678912',
        'License Number': '00-**********',
        Role: 'Therapist',
        'No of patients': '10',
      },
      {
        id: 3,
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        Name: '<PERSON>',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2021-08-20',
        'Last activity': '2024-06-12',
        phone_number: '0103456789123',
        'License Number': '00-**********',
        Role: 'Admin',
        'No of patients': '10',
      },
      {
        id: 4,
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        Name: '<PERSON> <PERSON>',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2020-02-25',
        'Last activity': '2024-06-10',
        phone_number: '0104567891234',
        'License Number': '00-**********',
        Role: 'Therapist',
        'No of patients': '10',
      },
      {
        id: 5,
        first_name: 'Emily',
        last_name: 'Clark',
        Name: 'Emily Clark',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2022-06-14',
        'Last activity': '2024-04-28',
        phone_number: '0105678912345',
        'License Number': '00-**********',
        Role: 'Admin',
        'No of patients': '10',
      },
      {
        id: 6,
        first_name: 'David',
        last_name: 'Nguyen',
        Name: 'David Nguyen',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2023-01-18',
        'Last activity': '2024-06-20',
        phone_number: '0106789123456',
        'License Number': '00-**********',
        Role: 'Therapist',
        'No of patients': '10',
      },
      {
        id: 7,
        first_name: 'Rachel',
        last_name: 'Kim',
        Name: 'Rachel Kim',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2021-10-10',
        'Last activity': '2024-06-15',
        phone_number: '0107891234567',
        'License Number': '00-**********',
        Role: 'Admin',
        'No of patients': '10',
      },
      {
        id: 8,
        first_name: 'Tom',
        last_name: 'Brown',
        Name: 'Tom Brown',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2019-12-05',
        'Last activity': '2024-06-18',
        phone_number: '0108912345678',
        'License Number': '00-**********',
        Role: 'Therapist',
        'No of patients': '10',
      },
      {
        id: 9,
        first_name: 'Jessica',
        last_name: 'White',
        Name: 'Jessica White',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2023-03-30',
        'Last activity': '2024-06-16',
        phone_number: '0109123456789',
        'License Number': '00-**********',
        Role: 'Admin',
        'No of patients': '10',
      },
      {
        id: 10,
        first_name: 'Luke',
        last_name: 'Harris',
        Name: 'Luke Harris',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2022-07-21',
        'Last activity': '2024-06-19',
        phone_number: '0100234567891',
        'License Number': '00-**********',
        Role: 'Therapist',
        'No of patients': '10',
      },
    ],
  },
];
const filterOptions = [
  { label: 'All', value: 'All therapists in the platform' },
  {
    label: 'Therapist',
    value: 'Therapists',
  },
  {
    label: 'Admin',
    value: 'Admins',
  },
];
// const actionOptions = [{ id: 1, label: 'Rermove Staff', value: 'Rermove Staff' }];
const getTherapistsByClinicId = id => {
  return staffData.find(clinic => clinic.clinic_id === id).therapists;
};
export const getTherapistById = (clinic_id, id) => {
  const therapists = staffData.find(clinic => clinic.clinic_id === clinic_id).therapists;
  if (therapists) {
    console.log(therapists);

    return therapists?.find(therapist => therapist.id === Number(id));
  }
  return null;
};

export default staffData;
export { getTherapistsByClinicId, filterOptions };
// This file contains mock data for patients, which can be used in testing or development.
