import ExerciseCard from '@/components/exerciseCard';
import AddTemplate from '@/components/exerciseCard/buttons/addTemplate';
import Delete from '@/components/exerciseCard/buttons/delete';
import Duplicate from '@/components/exerciseCard/buttons/duplicate';
import Favourite from '@/components/exerciseCard/buttons/favourite';
import { useEffect, useMemo } from 'react';
import { useDrag } from 'react-dnd';
import tw from 'twin.macro';

const SourceItemTemplate = ({
  item,
  handleDuplicate,
  handleDelete,
  handleFavouriteClick,
  PathIds,
  handelTemplateCardClick,
  handleAddTemplate,
  selectedCard,
  removeId,
  setSelectedCard,
  setValue,
  templates,
}) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'EXERCISE',
    item: { id: item.id, type: 'source', templateId: item.id },
    collect: monitor => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  const handleDeleteTemplate = () => {
    handleDelete(item);
  };

  const handleAddTemplateClick = () => {
    handleAddTemplate(item.exercises, item);
    if (selectedCard.length === 0) {
      setValue('frequency_day', item.frequency.day);
      setValue('frequency_week', item.frequency.week);
      setValue('duration', item.duration);
    }
  };

  const isSelectedItem = useMemo(() => {
    if (!selectedCard?.length || !item?.id) return false;

    const template = templates.find(_item => _item.id === item.id);
    if (!template?.exercises?.length) return false;

    const selectedFromSameTemplate = selectedCard.filter(
      sel => sel.templateId === item.id && !sel.isDuplicate
    );

    const allExercisesExist = template.exercises.every(templateEx => {
      return selectedFromSameTemplate.some(sel => {
        const match = sel.id.match(/^target-id-(\d+)-/);
        const actualId = match ? Number(match[1]) : Number(sel.id);

        return (
          actualId === Number(templateEx.id) &&
          sel.title === templateEx.title &&
          sel.reps === templateEx.reps &&
          sel.sets === templateEx.sets
        );
      });
    });

    return allExercisesExist;
  }, [selectedCard, item, templates]);

  let actionsList;
  if (isSelectedItem) {
    actionsList = [
      <Duplicate key="duplicate" handleClick={() => handleDuplicate(item)} />,
      <Delete key="delete" handleClick={handleDeleteTemplate} />,
    ];
  } else {
    actionsList = [<AddTemplate key={0} handleClick={handleAddTemplateClick} />];
  }

  useEffect(() => {
    if (removeId > 0) {
      const updated = selectedCard.map(_item => ({ ..._item, templateId: null }));
      setSelectedCard(updated);
    }
  }, [removeId]);

  return (
    <div
      ref={drag}
      style={{
        opacity: isDragging ? 0.5 : 1,
        transform: isDragging ? 'scale(1.05)' : 'scale(1)',
        transition: 'all 0.2s ease',
        cursor: 'grab',
      }}
    >
      <ExerciseCard
        hasShadowOnHover
        intensities={[]}
        tagText="Template"
        handelCardClick={handelTemplateCardClick}
        containerStyle={isSelectedItem ? tw`border-2 border-Primary_600` : tw``}
        title={item.title}
        subTitle={`${item?.exercises?.length} exercises`}
        PathIds={PathIds}
        isDefault={item.isDefault}
        actionsList={actionsList}
        favourite={<Favourite checked={item.isFavorite} onClick={handleFavouriteClick} />}
      />
    </div>
  );
};

export default SourceItemTemplate;
