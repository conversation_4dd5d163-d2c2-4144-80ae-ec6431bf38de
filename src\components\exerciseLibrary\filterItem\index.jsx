import ParentCheckbox from '../parentCheckbox';
import Child<PERSON><PERSON><PERSON><PERSON> from '../childCheckbox';
import AllCheckbox from '../allCheckbox';
import { exerciseLibraryActions } from '@/reducers/exercise-library';
import 'twin.macro';

// Filter Item Component
const FilterItem = ({
  item,
  control,
  watch,
  setValue,
  filterItems,
  register,
  state,
  dispatch,
  onDelete,
}) => {
  if (item.id === 'all') {
    return <AllCheckbox control={control} setValue={setValue} filterItems={filterItems} />;
  }

  const isCustomFilterItem = filterItems.some(
    group => group.title === 'custom' && group.items && group.items.some(i => i.id === item.id)
  );

  const handleCustomFilterClick = () => {
    if (isCustomFilterItem) {
      // Find the actual custom filter item from state.customFilters
      const clickedFilter = state.customFilters.find(filter => filter.id === item.id);
      if (clickedFilter) {
        dispatch({ type: exerciseLibraryActions.setEditFilterId, payload: clickedFilter.id });
        dispatch({ type: exerciseLibraryActions.isEditFilter, payload: true });
        // Clear current selected items and set them to the ones in the custom filter
        dispatch({ type: exerciseLibraryActions.toggleFilterItem, payload: null }); // Clear all
        clickedFilter.items.forEach(exercise => {
          dispatch({ type: exerciseLibraryActions.toggleFilterItem, payload: exercise.id });
        });
      }
    }
  };

  // Only render ParentCheckbox for custom if it has children
  const isCustom = item.title === 'custom';
  const hasChildren = item.items && item.items.length > 0;

  if (isCustom && !hasChildren) {
    return null;
  }

  return (
    <div key={item.id} onClick={isCustomFilterItem ? handleCustomFilterClick : undefined}>
      {isCustom && (
        <div
          style={{
            width: '100%',
            height: '1px',
            background: '#D9D9D9',
            margin: '4px 0',
          }}
        />
      )}
      <ParentCheckbox
        item={item}
        control={control}
        watch={watch}
        setValue={setValue}
        filterItems={filterItems}
      />

      {item.items?.length > 0 &&
        item.items.map(_val => (
          <ChildCheckbox
            key={_val.id}
            item={_val}
            control={control}
            register={register}
            watch={watch}
            setValue={setValue}
            filterItems={filterItems}
            dispatch={dispatch}
            state={state}
            onDelete={onDelete}
          />
        ))}
    </div>
  );
};
export default FilterItem;
