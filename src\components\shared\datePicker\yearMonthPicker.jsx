/* eslint-disable react-refresh/only-export-components */
import { useState, useRef, useEffect } from 'react';
import ActionButtons from './actionButton';
import { styles } from './datePicker';
import 'twin.macro';
import tw from 'twin.macro';

// Calendar Icon Component
const CalendarIcon = ({ iconColor }) => (
  <svg
    tw="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 4H4C3.45 4 3 4.45 3 5V13C3 13.55 3.45 14 4 14H12C12.55 14 13 13.55 13 13V5C13 4.45 12.55 4 12 4Z"
      stroke={iconColor || styles.primaryColor}
      strokeWidth="1.5"
    />
    <path
      d="M11 2V6"
      stroke={iconColor || styles.primaryColor}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M5 2V6"
      stroke={iconColor || styles.primaryColor}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path d="M3 8H13" stroke={iconColor || styles.primaryColor} strokeWidth="1.5" />
  </svg>
);

// Back Arrow Icon for returning to year selection
const BackArrowIcon = ({ onClick, color }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    tw="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
    onClick={onClick}
  >
    <path
      d="M15 10H5M5 10L10 15M5 10L10 5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Year Navigation Header Component
const YearNavigationHeader = ({
  currentYear,
  onPrevYear,
  onNextYear,
  onYearClick,
  primaryColor = styles.primaryColor,
  textDark = styles.textDark,
  isNextYearDisabled = false,
  // isPrevYearDisbled = false,
}) => {
  const prevYear = currentYear - 1;
  const nextYear = currentYear + 1;

  return (
    <div tw="flex justify-between items-center bg-white border-b border-border_stroke py-5">
      <div
        id="prev-year"
        tw=" text-center text-base rounded-lg px-3 font-[700] cursor-pointer flex-1"
        onClick={onPrevYear}
      >
        {prevYear}
      </div>
      <div
        id="current-year"
        tw=" text-center text-base text-white rounded-lg px-3 font-[700] py-1.5  cursor-pointer flex-1"
        css={[{ backgroundColor: primaryColor }]}
        onClick={onYearClick}
      >
        {currentYear}
      </div>
      <div
        id="next-year"
        tw=" text-center text-base rounded-lg px-3 font-[700] py-1.5 flex-1"
        css={[
          { color: textDark },
          isNextYearDisabled ? tw`opacity-40 cursor-not-allowed` : tw`opacity-100 cursor-pointer`,
        ]}
        onClick={() => !isNextYearDisabled && onNextYear()}
      >
        {nextYear}
      </div>
    </div>
  );
};

// Year Month Picker Component
const YearMonthPicker = ({
  // Base props
  onChange,
  value,
  label,

  // Customization props
  className,
  inputClassName,
  pickerClassName,
  labelClassName,
  primaryColor,
  secondaryColor,
  borderColor,
  borderRadius,
  iconColor,
  joinedDate,
  monthes,
  // Behavior props
  yearRange = { start: 1900, end: new Date().getFullYear() },

  // Format props
  locale = 'en-US',

  // Placeholder & Label Text
  placeholder = 'Select Year & Month',
  applyButtonText = 'Apply',
  cancelButtonText = 'Cancel',

  // Width options
  inputWidth = '100%',
  pickerWidth = '100%',

  // Calendar display settings
  closeOnSelect = false,

  // Custom handlers
  onCancel,

  // Parent controlled state
  isOpen: isOpenFromProps,
  onOpenChange,

  // Mode - with or without input
  standalone = false,
}) => {
  // Compute custom colors for the component
  const customColors = {
    primaryColor: primaryColor || styles.primaryColor,
    secondaryColor: secondaryColor || styles.secondaryColor,
    borderColor: borderColor || styles.borderColor,
    borderRadius: borderRadius || styles.borderRadius,
    selectedDayColor: primaryColor || styles.primaryColor,
  };

  // State Management
  const [selectedDate, setSelectedDate] = useState(value || new Date());
  const [isPickerOpen, setIsPickerOpen] = useState(standalone ? true : isOpenFromProps || false);
  const [activeStartDate, setActiveStartDate] = useState(value || new Date());
  const [showYearSelection, setShowYearSelection] = useState(true);
  const [monthViewYear, setMonthViewYear] = useState((value || new Date()).getFullYear());
  const [visableMonths, setVisableMonths] = useState(null);
  useEffect(() => {
    setVisableMonths(monthes);
  }, []);
  // Update internal state when controlled from parent
  useEffect(() => {
    if (isOpenFromProps !== undefined) {
      setIsPickerOpen(isOpenFromProps);
    }
  }, [isOpenFromProps]);

  // Update selected date when value changes
  useEffect(() => {
    if (value) {
      setSelectedDate(value);
      setActiveStartDate(value);
      setMonthViewYear(value.getFullYear());
    }
  }, [value]);

  // Refs for click-outside detection
  const pickerRef = useRef(null);
  const inputRef = useRef(null);

  // Generate years based on range
  const years = Array.from(
    { length: yearRange.end - yearRange.start + 1 },
    (_, i) => yearRange.start + i
  ).reverse();

  // Toggle picker visibility
  const togglePicker = () => {
    if (standalone) return; // No toggling in standalone mode

    const newIsOpen = !isPickerOpen;
    setIsPickerOpen(newIsOpen);

    if (onOpenChange) {
      onOpenChange(newIsOpen);
    }

    // Reset to year view when opening
    if (newIsOpen) {
      setShowYearSelection(true);
    }
  };

  // Format date for input display - show year and month
  const formatDateForInput = date => {
    if (!date) return '';
    return date.toLocaleDateString(locale, { year: 'numeric', month: 'long' });
  };

  // Navigate to a specific year (from year selection)
  const navigateToYear = year => {
    // Update the date with the selected year
    const updatedDate = new Date(selectedDate);
    updatedDate.setFullYear(year);
    setSelectedDate(updatedDate);
    setActiveStartDate(updatedDate);
    setMonthViewYear(year);

    // Move to month selection
    setShowYearSelection(false);
  };

  // Go back to year selection from month selection
  const backToYearSelection = () => {
    setShowYearSelection(true);
  };

  // Handle month click directly
  const handleMonthClick = monthIndex => {
    const newDate = new Date(selectedDate);
    newDate.setFullYear(monthViewYear);
    newDate.setMonth(monthIndex);

    setSelectedDate(newDate);

    // Close if closeOnSelect is true
    if (closeOnSelect) {
      if (onChange) {
        onChange(newDate);
      }
      if (!standalone) {
        setIsPickerOpen(false);
        if (onOpenChange) {
          onOpenChange(false);
        }
      }
    }
  };

  // Handle navigation
  const handleMonthNavigation = direction => {
    // Calculate new year based on direction
    const newYear = monthViewYear + direction;

    // Update states
    setMonthViewYear(newYear);

    // Update activeStartDate to show the correct year
    const newActiveStartDate = new Date(activeStartDate);
    newActiveStartDate.setFullYear(newYear);
    setActiveStartDate(newActiveStartDate);
  };

  // Handle Apply button
  const handleApply = () => {
    // Apply the selected year/month
    if (onChange) {
      onChange(selectedDate);
    }
    if (!standalone) {
      setIsPickerOpen(false);
      if (onOpenChange) {
        onOpenChange(false);
      }
    }
  };

  // Handle Cancel button
  const handleCancel = () => {
    // If in month selection, go back to year selection
    if (!showYearSelection) {
      setShowYearSelection(true);
      return;
    }

    // Close the picker without updating the value
    setIsPickerOpen(false);
    if (onOpenChange) {
      onOpenChange(false);
    }

    // Call custom onCancel if provided
    if (onCancel) {
      onCancel();
    }
  };

  // Close picker when clicking outside
  useEffect(() => {
    if (standalone) return; // No outside click handling in standalone mode

    const handleClickOutside = event => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target) &&
        inputRef.current &&
        !inputRef.current.contains(event.target)
      ) {
        setIsPickerOpen(false);
        if (onOpenChange) {
          onOpenChange(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onOpenChange, standalone]);

  // const actionButtonsComponent = (
  //   <ActionButtons
  //     onApply={handleApply}
  //     onCancel={handleCancel}
  //     applyButtonText={applyButtonText}
  //     cancelButtonText={showYearSelection ? cancelButtonText : 'Back'}
  //     primaryColor={customColors.primaryColor}
  //     secondaryColor={customColors.secondaryColor}
  //     borderColor={customColors.borderColor}
  //   />
  // );

  // Generate month names for custom month selection
  const getMonthNames = locale => {
    const months = [];
    for (let month = 0; month < 12; month++) {
      const date = new Date(2000, month, 1);
      const monthName = date.toLocaleString(locale, { month: 'short' });
      months.push(monthName);
    }
    return months;
  };
  const monthNames = getMonthNames(locale);
  const chunkArray = (arr, size) => {
    const result = [];
    for (let i = 0; i < arr.length; i += size) {
      result.push(arr.slice(i, i + size));
    }
    return result;
  };

  const monthChunks = chunkArray(monthNames, 3);

  // Render the picker content
  const renderPickerContent = () => (
    <div
      ref={pickerRef}
      tw="z-[100]"
      className={standalone ? className : pickerClassName}
      style={standalone ? { width: pickerWidth } : { width: pickerWidth }}
    >
      <div
        tw="w-[22rem] [box-shadow: 0 4px 6.8px 0 #0000000d] border rounded-xl overflow-hidden border-text_secondary bg-white relative"
        css={{ borderColor: customColors.borderColor }}
      >
        <div tw="relative">
          <YearNavigationHeader
            currentYear={monthViewYear}
            onPrevYear={() => handleMonthNavigation(-1)}
            onNextYear={() => handleMonthNavigation(1)}
            onYearClick={() => setShowYearSelection(false)}
            primaryColor={customColors.primaryColor}
            textDark={styles.textDark}
            isNextYearDisabled={monthViewYear >= new Date().getFullYear()}
            isPrevYearDisbled={monthViewYear <= new Date(joinedDate).getFullYear()}
          />

          {monthChunks.map((chunk, rowIdx) => (
            <div
              key={rowIdx}
              tw="grid grid-cols-3 "
              css={[
                rowIdx !== monthChunks.length - 1 && {
                  borderBottom: '1px solid #BDC4C9', // Tailwind's border-gray-200
                },
              ]}
            >
              {chunk.map((monthName, index) => {
                // Calculate the real index for selection
                const realIndex = rowIdx * 3 + index;
                return (
                  <div
                    key={monthName}
                    tw="cursor-pointer px-4 py-2.5"
                    onClick={() => handleMonthClick(realIndex)}
                  >
                    <div
                      tw="text-center rounded-md transition-colors py-1 border border-transparent"
                      css={[
                        realIndex === selectedDate.getMonth() &&
                        monthViewYear === selectedDate.getFullYear()
                          ? {
                              backgroundColor: customColors.primaryColor,
                              color: 'white',
                              fontWeight: '500',
                            }
                          : {
                              '&:hover': {
                                borderColor: customColors.primaryColor,
                              },
                            },
                      ]}
                    >
                      {monthName}
                    </div>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // For standalone mode, just render the picker without the input field
  if (standalone) {
    return renderPickerContent();
  }

  // Regular mode with input field
  return (
    <div tw="relative" className={className}>
      {label && (
        <label tw="block text-sm font-medium text-gray-700 mb-1" className={labelClassName}>
          {label}
        </label>
      )}

      <div tw="relative">
        <input
          ref={inputRef}
          type="text"
          tw="w-full py-[10px] px-[12px] border rounded-sm cursor-pointer"
          css={{
            borderColor: customColors.borderColor,
            borderRadius: customColors.borderRadius,
            width: inputWidth,
          }}
          className={inputClassName}
          placeholder={placeholder}
          value={formatDateForInput(selectedDate)}
          readOnly
          onClick={togglePicker}
        />
        <CalendarIcon iconColor={iconColor} />
      </div>

      {isPickerOpen && <div tw="absolute mt-1">{renderPickerContent()}</div>}
    </div>
  );
};

export default YearMonthPicker;
