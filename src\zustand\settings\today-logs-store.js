import { create } from 'zustand';
import { today } from '@/mock/logs-data';

const useTodayLogsStore = create(set => {
  // Define the store
  const store = {
    items: [], // Global today logs state
    head: 0, // Initialize head with 0
    loading: true, // Loading state
    step: 4, // Number of logs to fetch or show at a time
    hasMore: true, // Whether there are more logs to fetch

    // Fetch logs from the server
    fetchLogs: async () => {
      set(state => ({ loading: true })); // Set loading to true
      // Return a promise that resolves after the simulated delay and state update
      return new Promise(resolve => {
        setTimeout(() => {
          set(state => {
            const start = state.items.length;
            const newLogs = today.slice(start, start + state.step);

            const updated = {
              items: [...state.items, ...newLogs],
              head: state.items.length + newLogs.length, // Update head
              hasMore: start + state.step < today.length,
              loading: false, // Set loading to false after fetching
            };
            resolve(updated);
            return updated;
          });
        }, 1000); // Simulate 1 second delay
      });
    },

    // Handle "Show More" logic
    handleShowMore: () => {
      set(state => {
        if (state.hasMore) {
          store.fetchLogs();
          return {}; // No state update here; fetchLogs will handle it
        } else {
          return {
            head: Math.min(state.head + state.step, state.items.length),
          };
        }
      });
    },

    // Handle "Show Less" logic
    handleShowLess: () =>
      set(state => ({
        head: state.step, // Reset the head to the initial step
      })),
  };

  return store;
});

export default useTodayLogsStore;
