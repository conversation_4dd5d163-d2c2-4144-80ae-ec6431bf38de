import React from 'react';
import tw from 'twin.macro';
import Section<PERSON>ontainer from '@/components/patient/SectionContainer';
import TextArea from '@/components/shared/textarea';
import 'twin.macro';
import LabelSections from '@/components/sectionLabels';
function PatientFeedBack({ register, trigger, errors, fieldsThPermistions }) {
  const textAreasData = [
    {
      key: 'medical_conditions',
      disabled: !fieldsThPermistions.medical_condition,
      label: 'Medical Conditions',
      placeholder: 'Medical Conditions',
      name: 'medical_conditions',
      onBlur: () => trigger('medical_conditions'),
      errorMessage: errors?.medical_conditions?.message,
    },
    {
      key: 'pain_areas',
      disabled: !fieldsThPermistions.pain_area,
      label: 'Pain Areas',
      placeholder: 'Pain Areas',
      name: 'pain_areas',
      onBlur: () => trigger('pain_areas'),
      errorMessage: errors?.pain_areas?.message,
    },
  ];
  return (
    <SectionContainer tw="border-b-0">
      <LabelSections text={'Medical Information'} customStyle={tw`text-base mb-4`} />
      <div tw="flex gap-6">
        {/* Medical Condition */}
        {textAreasData.map((textArea, idx) => (
          <div tw="flex-1" key={idx}>
            <TextArea
              {...textArea}
              {...{
                register,
                rows: 5,
                containerSTyle: tw`px-0 py-0 border-0 `,
                labelStyle: tw`mb-2 text-sm font-medium`,
                textAreaStye: tw`border border-border_stroke rounded-md p-4 text-text_primary text-sm !bg-white resize-none disabled:!bg-disable disabled:cursor-not-allowed`,
              }}
            />
          </div>
        ))}
      </div>
    </SectionContainer>
  );
}

export default PatientFeedBack;
