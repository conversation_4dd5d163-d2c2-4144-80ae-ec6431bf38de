import React, { useState, useRef, useImperativeHandle, forwardRef } from 'react';
import { EditorProvider } from '@tiptap/react';
import MenuBar from './menuBar';
import { customStyles } from './styles';
import { extensions } from './editorConfig';

const TiptapEditor = forwardRef(
  ({ onContentChange, initialContent = '', placeholder = '' }, ref) => {
    const [linkText, setLinkText] = useState('');
    const [linkUrl, setLinkUrl] = useState('');
    const [editorContent, setEditorContent] = useState('');
    const editorRef = useRef(null);

    const content = () => {
      if (initialContent) {
        return initialContent;
      }
      return placeholder;
    };

    useImperativeHandle(ref, () => ({
      getContent: () => {
        return {
          html: editorContent,
          json: editorRef.current?.getJSON(),
        };
      },

      clearContent: () => {
        editorRef.current?.commands.clearContent();
      },

      setContent: newContent => {
        editorRef.current?.commands.setContent(newContent);
      },

      focus: () => {
        editorRef.current?.commands.focus();
      },

      getEditor: () => editorRef.current,
    }));

    return (
      <div tw="p-4 mx-auto max-w-2xl">
        <EditorProvider
          slotBefore={
            <MenuBar
              linkText={linkText}
              linkUrl={linkUrl}
              setLinkUrl={setLinkUrl}
              setLinkText={setLinkText}
            />
          }
          extensions={extensions}
          content={content}
          onUpdate={({ editor }) => {
            editorRef.current = editor;
            const html = editor.getHTML();
            setEditorContent(html);
            if (onContentChange) {
              onContentChange(html);
            }
          }}
          autofocus
          editorProps={{
            attributes: {
              class:
                'p-[16px] w-full border border-text_secondary rounded-[10px] outline-none text-[14px] min-h-[200px]',
            },
          }}
        />
        <style jsx global>
          {customStyles}
        </style>
      </div>
    );
  }
);

TiptapEditor.displayName = 'TiptapEditor';

export default TiptapEditor;
