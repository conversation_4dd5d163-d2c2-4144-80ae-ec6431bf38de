import React from 'react';
import DeleteIcon from '@assets/svgs/delete-red.svg';
import tw from 'twin.macro';

const Delete = ({ handleClick }) => (
  <button
    type="button"
    css={tw`relative  w-[18px] h-[18px] p-0 border-none bg-none`}
    onClick={e => {
      handleClick();
      e.stopPropagation();
    }}
    onPointerDown={e => e.stopPropagation()}
  >
    <img
      src={DeleteIcon}
      alt="delete Icon"
      css={tw`absolute top-0 left-0 w-[22px] h-[22px]`}
      draggable={false}
    />
  </button>
);

export default Delete;
