import ExerciseDetailsModelDescription from '../exerciseDetailsModelDescription';
import ExerciseDetailsModelTags from '../exerciseDetailsModelTags';
import ExerciseDetailsModeForm from '../excersiceDetailsModeForm';
import ExerciseDetailsModelListOfExercise from '../excersiceDetailsModelListOfExcersies';
import ExerciseDetailsModelHeader from '../exerciseDetailsModelHeader';
import GenericModal from '@/components/genericModal';
import tw from 'twin.macro';

const ExerciseDetailsModel = ({
  exercise,
  exerciseList,
  PrimaryButtonText,
  clickOnPrimaryButton,
  typeOfPrimaryButton = 'submit',
  secondaryButtonText,
  clickOnSecondaryButton,
  typeOfSecondaryButton = 'button',
  openModel,
  handelCloseMode,
  onSubmit,
  errors,
  control,
  register,
  setValue,
  onRelatedExerciseClick,
  onBackToPreviousExercise,
  exerciseStack,
  setSelectedCard,
  selectedCard,
  handleFavouriteClick,
  handleDelete,
  handleDuplicate,
}) => {
  if (!exercise) return null;
  const Tags = [
    { id: 0, title: 'Region: Chest' },
    { id: 1, title: 'Region: Back' },
    { id: 2, title: 'Region: Knees' },
    { id: 3, title: 'Joints: Hips' },
    { id: 4, title: 'Region: Chest' },
  ];

  const description = exercise.description || 'loredsdasdsd';
  const title = exercise.title || 'Arm Pendulum';
  const videoUrl = exercise.videoUrl || 'https://assets.mixkit.co/videos/40881/40881-720.mp4';
  const instructions =
    exercise.instructions ||
    'Lorem ipsum dolor sit amet consectetur, adipisicing elit. Placeat repellendus namsimilique, possimus in officia atque id incidunt facilis esse, expedita assumenda hicdoloremque dolor illo sed, eveniet sequi necessitatibus? Lorem ipsum dolor sit ametconsectetur, adipisicing elit. Placeat repellendus nam similique, possimus in officiaatque id incidunt facilis esse, expedita assumenda hic doloremque dolor illo sed,eveniet sequi necessitatibus? Lorem ipsum dolor sit amet consectetur, adipisicing elit.Placeat repellendus nam similique, possimus in officia atque id incidunt facilis esse, expedita assumenda hic doloremque dolor illo sed, eveniet sequi necessitatibus? Loremipsum dolor sit amet consectetur, adipisicing elit. Placeat repellendus nam similique, possimus in officia atque id incidunt facilis esse, expedita assumenda hic doloremque dolor illo sed, eveniet sequi necessitatibus';
  const movementName = exercise.movementName || 'shoulder';

  const exerciseConfig = [
    // {
    //   id: 1,
    //   name: 'mode',
    //   label: 'General Option',
    //   type: 'dropdown',
    //   options: ['Fixed Reps', 'Fixed Time', 'Fixed Reps and Fixed Time'],
    //   default: 'Fixed Time',
    //   validation: {
    //     required: true,
    //     type: 'string',
    //     allowed_values: ['Fixed Reps', 'Fixed Time', 'Fixed Reps and Fixed Time'],
    //     error_message: 'Please select a valid mode.',
    //   },
    // },
    {
      id: 2,
      name: 'reps_count',
      label: 'Reps Count',
      type: 'input',
      input_type: 'number',
      default: 5,
      display_depends_on: {
        field: 'mode',
        values: ['Fixed Reps', 'Fixed Reps and Fixed Time'],
      },
      validation: {
        required: true,
        type: 'number',
        min: 1,
        max: 20,
        error_message: 'Reps must be between 1 and 20.',
      },
    },
    // {
    //   id: 3,
    //   name: 'time',
    //   label: 'Time',
    //   type: 'dropdown',
    //   options: [10, 20, 30],
    //   default: 20,
    //   display_depends_on: {
    //     field: 'mode',
    //     values: ['Fixed Time'],
    //   },
    //   validation: {
    //     required: true,
    //     type: 'string',
    //     allowed_values: [10, 20, 30],
    //     error_message: 'Please select a valid time.',
    //   },
    // },
    {
      id: 4,
      name: 'sets_count',
      label: 'Number of Sets',
      type: 'input',
      input_type: 'number',
      placeholder: 'Enter number of sets',
      default: 1,
      validation: {
        required: true,
        type: 'number',
        min: 1,
        max: 3,
        error_message: 'Sets count must be between 1 and 3.',
      },
    },
    {
      id: 6,
      name: 'time',
      label: 'Time',
      type: 'input',
      input_type: 'number',
      default: 3,
      validation: {
        required: true,
        type: 'number',
        min: 3,
        max: 300,
        error_message: 'Time must be between 3 and 300 seconds.',
      },
    },
    {
      id: 7,
      name: 'holding-time',
      label: 'Holding time',
      type: 'input',
      input_type: 'number',
      default: 3,
      validation: {
        required: true,
        type: 'number',
        min: 3,
        max: 15,
        error_message: 'Holding time must be between 3 and 15 seconds.',
      },
    },
    // {
    //   id: 6,
    //   name: 'pacing',
    //   label: 'Pacing',
    //   type: 'dropdown',
    //   options: [5, 10, 15, 20, 25],
    //   default: 15,
    //   disabled_depends_on: {
    //     field: 'mode',
    //     values: ['Fixed Time', 'Fixed Reps'],
    //   },
    //   validation: {
    //     required: true,
    //     type: 'string',
    //     allowed_values: [5, 10, 15, 20, 25],
    //     error_message: 'Please select a valid pacing value.',
    //   },
    // },
    // {
    //   id: 7,
    //   name: 'accuracy',
    //   label: 'Accuracy',
    //   type: 'dropdown',
    //   options: [10, 20, 30, 40, 50, 60, 70, 80, 90],
    //   default: 40,
    //   validation: {
    //     required: true,
    //     type: 'string',
    //     allowed_values: [10, 20, 30, 40, 50, 60, 70, 80, 90],
    //     error_message: 'Please select a valid accuracy percentage.',
    //   },
    // },
  ];

  // Function to check if a field should be displayed based on its displayDependsOn property
  const shouldDisplayField = field => {
    // If no displayDependsOn property, always display the field
    if (!field.displayDependsOn && !field.disabledDependsOn) {
      return true;
    }

    // Handle displayDependsOn logic - only show when value matches
    if (field.displayDependsOn) {
      const { field: dependentFieldName, values: acceptableValues } = field.displayDependsOn;

      // Create the full field name with movement name prefix
      const fullFieldName = `${movementName}_${dependentFieldName}`;

      // Get the current value of the dependent field
      const currentValue = watch(fullFieldName);

      // For dropdown fields, the value might be an object with a 'value' property
      const actualValue =
        currentValue && typeof currentValue === 'object' ? currentValue.value : currentValue;

      // Check if the actual value matches any of the acceptable values
      return acceptableValues.includes(actualValue);
    }

    // Handle disabledDependsOn logic - hide when value matches
    if (field.disabledDependsOn) {
      const { field: dependentFieldName, values: disableValues } = field.disabledDependsOn;

      // Create the full field name with movement name prefix
      const fullFieldName = `${movementName}_${dependentFieldName}`;

      // Get the current value of the dependent field
      const currentValue = watch(fullFieldName);

      // For dropdown fields, the value might be an object with a 'value' property
      const actualValue =
        currentValue && typeof currentValue === 'object' ? currentValue.value : currentValue;

      // Reverse logic: hide when value is in the disableValues array
      return !disableValues.includes(actualValue);
    }

    // Default case (should not reach here)
    return true;
  };

  // Function to reset all form fields to their default values
  const handleResetToDefault = () => {
    if (exerciseConfig) {
      exerciseConfig.forEach(field => {
        // Create the field name using the movementName and field.name
        const fieldName = `${movementName}_${field.name}`;

        // Handle different field types differently
        if (field.type === 'dropdown') {
          // For other dropdown fields
          const defaultValueObject = field.default
            ? {
                label: field.default,
                value: field.default,
              }
            : null;

          setValue(fieldName, defaultValueObject, {
            shouldDirty: true,
            shouldTouch: true,
            shouldValidate: true,
          });
          // }
        } else if (field.type === 'input') {
          // For number inputs, convert to Number type
          const defaultValue =
            field.default !== undefined && field.default !== null ? Number(field.default) : '';

          // Use setValue with shouldDirty:true to trigger input component re-render
          setValue(fieldName, defaultValue, {
            shouldDirty: true,
            shouldTouch: true,
            shouldValidate: true,
          });

          // Force re-render by triggering a change event
          const event = new Event('input', { bubbles: true });
          const inputElement = document.querySelector(`input[name="${fieldName}"]`);
          if (inputElement) {
            inputElement.value = defaultValue;
            inputElement.dispatchEvent(event);
          }
        } else {
          // For other regular inputs, just set the default value
          setValue(fieldName, field.default || '', {
            shouldDirty: true,
            shouldTouch: true,
            shouldValidate: true,
          });
        }
      });
    }
  };

  // Helper function to create validation rules from backend validation
  const getFieldValidationRules = field => {
    const rules = {};

    if (field.validation) {
      // Required validation
      if (field.validation.required) {
        rules.required = field.validation.errorMessage || 'This field is required';
      }

      // Type validation
      if (field.validation.type) {
        rules.valueAsNumber = field.validation.type === 'number';
        // Add integer validation for number type
        if (field.validation.type === 'number') {
          rules.validate = value => {
            return Number.isInteger(Number(value)) || 'Please enter a integer number';
          };
        }
      }

      // Min/Max validation for numbers
      if (field.validation.min !== undefined) {
        rules.min = {
          value: field.validation.min,
          message: field.validation.errorMessage || `Minimum value is ${field.validation.min}`,
        };
      }

      if (field.validation.max !== undefined) {
        rules.max = {
          value: field.validation.max,
          message: field.validation.errorMessage || `Maximum value is ${field.validation.max}`,
        };
      }

      // Custom validation for allowedValues
      if (field.validation.allowedValues && field.validation.allowedValues.length > 0) {
        rules.validate = value => {
          // For dropdowns, we need to check the value property
          const actualValue = value && typeof value === 'object' ? value.value : value;
          return (
            field.validation.allowedValues.includes(actualValue) ||
            field.validation.errorMessage ||
            'Invalid selection'
          );
        };
      }
    }

    return rules;
  };

  return (
    <GenericModal
      PrimaryButtonStyle={tw`basis-[50%] rounded-[6px]`}
      PrimaryButtonText={PrimaryButtonText}
      SecondaryButtonStyle={tw`basis-[50%] rounded-[6px]`}
      backgroundModal={'rgba(0,0,0,0.4)'}
      clickOnPrimaryButton={() => clickOnPrimaryButton(exercise)}
      clickOnSecondaryButton={clickOnSecondaryButton}
      handelCloseMode={handelCloseMode}
      handelFormSubmit={() => onSubmit(exercise)}
      modalStyle={tw`backdrop-blur-[0px]`}
      containerModalStyle={tw`rounded-card! w-[50vw]`}
      secondaryButtonText={secondaryButtonText}
      typeOfPrimaryButton={'button'}
      openModel={openModel}
      element="div"
      typeOfSecondaryButton={typeOfSecondaryButton}
      title={
        <ExerciseDetailsModelHeader
          handelCLiconTitle={onBackToPreviousExercise}
          handelCloseClick={handelCloseMode}
          title={title}
          hideArrow={exerciseStack.length === 0}
        />
      }
      content={
        <div tw="py-[14px] px-[20px] relative max-h-[65vh] overflow-y-auto" className="element">
          <ExerciseDetailsModelDescription
            description={description}
            instructions={instructions}
            videoUrl={videoUrl}
          />
          <ExerciseDetailsModelTags tagsList={Tags} />
          <ExerciseDetailsModeForm
            handelResetDefault={handleResetToDefault}
            text={'Reset to default'}
            exerciseConfig={exerciseConfig}
            shouldDisplayField={shouldDisplayField}
            movementName={movementName}
            getFieldValidationRules={getFieldValidationRules}
            errors={errors}
            control={control}
            register={register}
          />
          <ExerciseDetailsModelListOfExercise
            exerciseList={exerciseList}
            onRelatedExerciseClick={onRelatedExerciseClick}
            handleDelete={handleDelete}
            handleFavouriteClick={handleFavouriteClick}
            selectedCard={selectedCard}
            handleDuplicate={handleDuplicate}
            setSelectedCard={setSelectedCard}
          />
        </div>
      }
    />
  );
};

export default ExerciseDetailsModel;
