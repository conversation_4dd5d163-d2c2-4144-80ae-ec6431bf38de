import 'twin.macro';
import { styles } from './datePicker';

// Year Item Component
const YearItem = ({
  year,
  isSelected,
  onClick,
  primaryColor = styles.primaryColor,
  borderColor = styles.borderColor,
}) => (
  <div tw="flex justify-center py-4 w-full border-b" css={{ borderColor }}>
    <div
      tw="py-1 w-fit px-3 text-center cursor-pointer rounded-[6px] text-[13px] transition-colors"
      css={[
        isSelected
          ? {
              backgroundColor: primaryColor,
              color: 'white',
              fontWeight: 'bold',
            }
          : {
              '&:hover': {
                backgroundColor: primaryColor,
                color: 'white',
              },
            },
      ]}
      onClick={onClick}
    >
      {year}
    </div>
  </div>
);

export default YearItem;
