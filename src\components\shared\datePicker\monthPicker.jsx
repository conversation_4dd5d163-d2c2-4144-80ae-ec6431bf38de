/* eslint-disable react-refresh/only-export-components */
import Calendar from "react-calendar";
import { useState, useRef, useEffect } from "react";
import ActionButtons from "./actionButton";
import NavigationLabel from "./navigationLabel";
import { styles } from "./datePicker";
import "twin.macro";

// Calendar Icon Component
const CalendarIcon = ({ iconColor }) => (
  <svg
    tw="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 4H4C3.45 4 3 4.45 3 5V13C3 13.55 3.45 14 4 14H12C12.55 14 13 13.55 13 13V5C13 4.45 12.55 4 12 4Z"
      stroke={iconColor || styles.primaryColor}
      strokeWidth="1.5"
    />
    <path
      d="M11 2V6"
      stroke={iconColor || styles.primaryColor}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M5 2V6"
      stroke={iconColor || styles.primaryColor}
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M3 8H13"
      stroke={iconColor || styles.primaryColor}
      strokeWidth="1.5"
    />
  </svg>
);

// Month Picker Component
const MonthPicker = ({
  // Base props
  onChange,
  value,
  label,

  // Customization props
  className,
  inputClassName,
  pickerClassName,
  labelClassName,
  primaryColor,
  secondaryColor,
  borderColor,
  borderRadius,
  iconColor,

  // Format props
  locale = "en-US",

  // Placeholder & Label Text
  placeholder = "Select Month",
  applyButtonText = "Apply",
  cancelButtonText = "Cancel",

  // Width options
  inputWidth = "100%",
  pickerWidth = "100%",

  // Calendar display settings
  closeOnSelect = false,

  // Custom handlers
  onCancel,

  // Parent controlled state
  isOpen: isOpenFromProps,
  onOpenChange,
}) => {
  // Compute custom colors for the component
  const customColors = {
    primaryColor: primaryColor || styles.primaryColor,
    secondaryColor: secondaryColor || styles.secondaryColor,
    borderColor: borderColor || styles.borderColor,
    borderRadius: borderRadius || styles.borderRadius,
    selectedDayColor: primaryColor || styles.primaryColor,
  };

  // State Management
  const [selectedDate, setSelectedDate] = useState(value || new Date());
  const [isPickerOpen, setIsPickerOpen] = useState(isOpenFromProps || false);
  const [activeStartDate, setActiveStartDate] = useState(value || new Date());
  const currentDateYear = new Date().getFullYear();

  // Update internal state when controlled from parent
  useEffect(() => {
    if (isOpenFromProps !== undefined) {
      setIsPickerOpen(isOpenFromProps);
    }
  }, [isOpenFromProps]);

  // Update selected date when value changes
  useEffect(() => {
    if (value) {
      setSelectedDate(value);
      setActiveStartDate(value);
    }
  }, [value]);

  // Refs for click-outside detection
  const pickerRef = useRef(null);
  const inputRef = useRef(null);

  // Format month names
  const formatMonth = (locale, date) => {
    const monthName = date.toLocaleString(locale, { month: "long" });
    return monthName.substring(0, 3);
  };

  // Check if year is in the future
  const isYearInFuture = (year) => {
    return year > currentDateYear;
  };

  // Toggle picker visibility
  const togglePicker = () => {
    const newIsOpen = !isPickerOpen;
    setIsPickerOpen(newIsOpen);

    if (onOpenChange) {
      onOpenChange(newIsOpen);
    }
  };

  // Format date for input display - show month and year
  const formatDateForInput = (date) => {
    if (!date) return "";
    return date.toLocaleDateString(locale, { year: "numeric", month: "long" });
  };

  // Handle date change from calendar
  const handleDateChange = (date) => {
    setSelectedDate(date);

    // When a month is selected
    if (closeOnSelect) {
      if (onChange) {
        onChange(date);
      }
      setIsPickerOpen(false);
      if (onOpenChange) {
        onOpenChange(false);
      }
    }
  };

  // Handle Apply button
  const handleApply = () => {
    // Apply the selected month and year
    const finalDate = selectedDate;
    if (onChange) {
      onChange(finalDate);
    }
    setIsPickerOpen(false);
    if (onOpenChange) {
      onOpenChange(false);
    }
  };

  // Handle Cancel button
  const handleCancel = () => {
    // Close the picker without updating the value
    setIsPickerOpen(false);
    if (onOpenChange) {
      onOpenChange(false);
    }

    // Call custom onCancel if provided
    if (onCancel) {
      onCancel();
    }
  };

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target) &&
        inputRef.current &&
        !inputRef.current.contains(event.target)
      ) {
        setIsPickerOpen(false);
        if (onOpenChange) {
          onOpenChange(false);
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onOpenChange]);

  const actionButtonsComponent = (
    <ActionButtons
      onApply={handleApply}
      onCancel={handleCancel}
      applyButtonText={applyButtonText}
      cancelButtonText={cancelButtonText}
      primaryColor={customColors.primaryColor}
      secondaryColor={customColors.secondaryColor}
      borderColor={customColors.borderColor}
    />
  );

  // Calendar styling with custom colors
  const calendarCssStyles = {
    height: "auto",
    backgroundColor: "#fff",
    width: "100%",
    borderRadius: customColors.borderRadius,
    borderColor: "transparent",
    padding: "0",
    margin: "0",
    overflow: "hidden",

    // Navigation area
    ".react-calendar__navigation": styles.calendarStyles.navigation,

    // Button styling
    ".react-calendar button": {
      margin: "0",
      border: "0",
      borderRadius: "0",
      overflow: "hidden",
      background: "none",
      color: "#000",
      cursor: "pointer",
      height: "auto",
      textAlign: "center",
      fontWeight: "normal",
      fontSize: "13px",
    },

    // Month tiles in year view
    ".react-calendar__year-view__months__month": {
      padding: "12px 0",
      width: "25%",
      margin: "4px",
      borderRadius: "8px",
      flexBasis: "30%",
      fontSize: "14px",
    },

    // Hover effect
    ".react-calendar__tile:enabled:hover": {
      backgroundColor: "#f8f8f8",
      borderRadius: "50%",
    },
    ".react-calendar__tile:enabled:focus": {
      backgroundColor: "#f0f0f0",
    },

    // Active (selected) tile
    ".react-calendar__tile--active": styles.calendarStyles.tile.active,
    ".react-calendar__tile--now": {
      borderRadius: "50%",
      position: "relative",
      backgroundColor: "#f5f5f5",
    },
  };

  return (
    <div tw="relative" className={className}>
      {label && (
        <label
          tw="block text-[14px] font-medium text-gray-700 mb-1"
          className={labelClassName}
        >
          {label}
        </label>
      )}

      <div tw="relative">
        <input
          ref={inputRef}
          type="text"
          tw="w-full py-[10px] px-[12px] border rounded-[6px] cursor-pointer"
          css={{
            borderColor: customColors.borderColor,
            borderRadius: customColors.borderRadius,
            width: inputWidth,
          }}
          className={inputClassName}
          placeholder={placeholder}
          value={formatDateForInput(selectedDate)}
          readOnly
          onClick={togglePicker}
        />
        <CalendarIcon iconColor={iconColor} />
      </div>

      {isPickerOpen && (
        <div
          ref={pickerRef}
          tw="absolute z-[100] mt-1"
          style={{ width: pickerWidth }}
          className={pickerClassName}
        >
          <div
            tw="w-full [box-shadow: 0 4px 6.8px 0 #0000000d] border rounded-[12px] overflow-hidden border-text_secondary bg-white relative"
            css={{ borderColor: customColors.borderColor }}
          >
            <Calendar
              onChange={handleDateChange}
              value={selectedDate}
              prevLabel={null}
              nextLabel={null}
              prev2Label={null}
              next2Label={null}
              showNeighboringMonth={false}
              view="year" // Always show the year view (months)
              activeStartDate={activeStartDate}
              onActiveStartDateChange={({ activeStartDate }) => {
                setActiveStartDate(activeStartDate);
              }}
              formatMonth={formatMonth}
              navigationLabel={({ date, locale }) => (
                <NavigationLabel
                  date={date}
                  locale={locale}
                  view="year"
                  setView={() => {}} // No-op since we only have year view
                  isYearInFuture={isYearInFuture}
                  primaryColor={customColors.primaryColor}
                  textDark={styles.textDark}
                />
              )}
              css={calendarCssStyles}
            />
            {actionButtonsComponent}
          </div>
        </div>
      )}
    </div>
  );
};

export default MonthPicker;
