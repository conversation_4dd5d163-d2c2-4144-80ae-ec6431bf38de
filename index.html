<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.syncfusion.com/ej2/29.1.33/ej2-react-richtexteditor/styles/material.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.syncfusion.com/ej2/29.1.33/ej2-base/styles/material.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.syncfusion.com/ej2/29.1.33/ej2-buttons/styles/material.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.syncfusion.com/ej2/29.1.33/ej2-lists/styles/material.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.syncfusion.com/ej2/29.1.33/ej2-react-buttons/styles/material.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.syncfusion.com/ej2/29.1.33/ej2-popups/styles/material.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.syncfusion.com/ej2/29.1.33/ej2-navigations/styles/material.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.syncfusion.com/ej2/29.1.33/ej2-splitbuttons/styles/material.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.syncfusion.com/ej2/29.1.33/ej2-inputs/styles/material.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.syncfusion.com/ej2/29.1.33/ej2-dropdowns/styles/material.css"
      rel="stylesheet"
    />
    <!-- <script
      src="https://cdn.jsdelivr.net/npm/react-render-tracker"
      data-config="inpage:true"
    ></script> -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/systemjs/0.19.38/system.js"></script>
    <title>Rehabitair</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
