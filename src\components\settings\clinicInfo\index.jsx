import AddPatient from '@/components/addPatientForm';
import LabelSections from '@/components/sectionLabels';
import SectionCards from '@/components/sectionsCard';
import CLinicForm from '../clinicForm';
import { useForm } from 'react-hook-form';
import { InitialState } from '../clinicForm/modules';
import 'twin.macro';
import { showError } from '@/libs/react.toastify';
import tw from 'twin.macro';
import SaveChangesButton from '../saveChangesButton';

const ClinicInfo = () => {
  const {
    register,
    control,
    formState: { isSubmitting, errors },
  } = useForm({ defaultValues: InitialState });

  const onSubmit = async data => {
    try {
      console.log('data', data);
    } catch (error) {
      showError(error.message);
    }
  };
  return (
    <SectionCards customStyle={tw`pt-[20px]`}>
      <div tw="mb-[24px]">
        <LabelSections text={'Clinic information'} customStyle={tw`mb-[24px] px-[24px]`} />
        <CLinicForm register={register} control={control} errors={errors} />
      </div>
      <SaveChangesButton />
    </SectionCards>
  );
};

export default ClinicInfo;
