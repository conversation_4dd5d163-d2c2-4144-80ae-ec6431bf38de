import { Link, useNavigate } from 'react-router-dom';
import 'twin.macro';
import NameLinkIcon from '@assets/svgs/Link.svg';
import tw from 'twin.macro';
import { useExerciseLibrary } from '@/zustand/exercise-library';
import ActionLabel from '@/components/actionLabel';
/**
 * A component that displays prescription title information with assignee and assigner details
 *
 * @param {Object} props
 * @param {string} props.fromPerson - The name of the person who assigned the prescription
 * @param {string} props.toPerson - The name of the person who received the prescription
 * @param {JSX.Element} [props.customHead] - Optional custom header component to override default rendering
 *
 * @example
 * <PrescriptionActiveTitle
 *   fromPerson="Dr. Smith"
 *   toPerson="<PERSON>"
 * />
 *
 * @returns {JSX.Element} A header showing prescription assignment information
 *
 * @styling
 * - Layout: Flex container with 10px gap between elements
 * - Primary text: 0.9em size, semi-bold (600)
 * - Uses PersonLink component for the assigner's name
 */

const PrescriptionActiveTitle = ({
  by<PERSON><PERSON>,
  prescriptionName,
  therapist_id,
  patient_id,
  prescription_id,
  customHead,
  notes,
  children,
  isActive,
  editPerscriptionBase,
  pov,
}) => {
  const { setOpenNotesView } = useExerciseLibrary();
  const navigate = useNavigate();
  const navigateTo = pov === 'patient' ? `/staff/${therapist_id}` : `/patients/${patient_id}`;
  return customHead ? (
    customHead
  ) : (
    <div tw="flex justify-between items-center mb-8 w-full">
      <div tw="space-y-2.5">
        <div tw="flex items-center gap-1 text-[.875rem]">
          <p tw="font-semibold text-Primary_600">{prescriptionName}</p>
          <p tw="text-black">assign {pov === 'patient' ? 'By' : 'To'}</p>
          <Link
            to={navigateTo}
            className="group"
            css={[
              tw`
            transition-colors duration-200
            text-text_tertiary
            hover:text-info
            hover:underline
            underline-offset-4
            inline-flex items-center
          `,
            ]}
          >
            {byPerson}
            <img
              src={NameLinkIcon}
              alt="link"
              css={[
                tw`
              w-4 h-4 ml-1 opacity-0 transition-opacity duration-200
              group-hover:opacity-100
              pointer-events-none
            `,
              ]}
            />
          </Link>
        </div>

        {notes && isActive && (
          <div tw="text-xs font-semibold cursor-pointer text-text_primary">
            Includes pre-session messages.{' '}
            <ActionLabel
              onClick={() => {
                editPerscriptionBase();
                setOpenNotesView(true);
                navigate(
                  '/exercise-library',

                  { state: prescription_id }
                );
              }}
            >
              Check in Exercise Library.
            </ActionLabel>
          </div>
        )}
      </div>

      <div tw="relative w-12 h-10">{children}</div>
    </div>
  );
};

export default PrescriptionActiveTitle;
