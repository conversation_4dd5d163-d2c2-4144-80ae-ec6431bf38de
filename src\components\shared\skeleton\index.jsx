import React from "react";
import tw from "twin.macro";

/**
 * Reusable Skeleton Loader Component
 * @param {Object} props
 * @param {Object} props.skeletonStyle - Style object for the skeleton (width, height, borderRadius, etc.)
 * @returns {JSX.Element}
 */
const Skeleton = ({ skeletonStyle }) => (
  <div css={[tw`animate-pulse bg-gray-200 w-full h-full`, skeletonStyle]} />
);

export default Skeleton;
