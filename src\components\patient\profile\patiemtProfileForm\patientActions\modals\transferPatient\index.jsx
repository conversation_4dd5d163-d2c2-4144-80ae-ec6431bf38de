import Input from '@/components/shared/input';
import NormalModal from '@/components/shared/normalModal';
import tw from 'twin.macro';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { getTherapistsByClinicId } from '@/mock/therapists-data';
import { useMemo } from 'react';
import { initialTransferPatient } from './module';
import { transferPatientSchema } from './schema';
import GenericSelect from '@/components/shared/select';
import checkCircle from '@/assets/svgs/patient/check-circle.svg';
import ModalTitle from '@/components/modalTitle';
import { useState } from 'react';
import GenericModal from '@/components/genericModal';
import ActionModal from '@/components/actionModal';
//patient prop
const clinic_id = 1;
const TransferPatient = ({ open, handleClose, patient }) => {
  const [isDoneModalOpen, setIsDoneModalOpen] = useState(false);
  const [selectedTherapist, setSelectedTherapist] = useState(null); // {patient_name, therapist_name}

  const { patient_id, patient_name } = patient;

  const therapists = getTherapistsByClinicId(clinic_id);

  const therapistOptions = useMemo(
    () =>
      therapists.map(therapist => ({
        value: therapist.id,
        label: `${therapist.first_name} ${therapist.last_name}`,
      })),
    [therapists]
  );

  const {
    register,
    control,
    getValues,
    formState: { errors, isValid },
    handleSubmit,
  } = useForm({
    defaultValues: initialTransferPatient(patient_name),
    resolver: yupResolver(transferPatientSchema),
    mode: 'onChange',
  });

  const formData = getValues();

  const onSubmit = data => {
    //open the success data after recive response
    setIsDoneModalOpen(true);
    setSelectedTherapist({ patient_name, therapist_name: data.therapist.label });
  };

  return (
    <>
      {!isDoneModalOpen && (
        <GenericModal
          openModel={open}
          handelCloseMode={handleClose}
          title={<ModalTitle title={'Transfer Patient'} handleClose={handleClose} />}
          titleStyle={tw`rounded-t-xl overflow-hidden`}
          containerModalStyle={tw`max-w-fit h-auto !rounded-xl overflow-visible`}
          element="div"
          contetnContainerStyle={tw`min-h-full`}
          content={
            <>
              <div tw="px-8 pb-8 space-y-5">
                <Input
                  name="patient_name"
                  label="Patient"
                  register={register}
                  placeholder="Patient Name"
                  defaultValue={formData.patient_name}
                  readOnly
                />

                <GenericSelect
                  control={control}
                  label="Transfer to"
                  name="therapist"
                  placeholder="Select a therapist"
                  options={therapistOptions}
                  errorMessage={errors?.therapist?.message || ''}
                  labelStyle={tw`font-medium text-[0.95rem]`}
                  customStyles={{
                    control: () => ({
                      padding: '8px',
                    }),
                    menuList: () => ({
                      maxHeight: '250px',
                    }),
                  }}
                  isFixed
                  inputLength={100}
                />
              </div>
            </>
          }
          typeOfPrimaryButton={'button'}
          clickOnPrimaryButton={handleSubmit(onSubmit)}
          PrimaryButtonText={'Transfer'}
          PrimaryButtonStyle={tw`flex-1 !py-2.5 !rounded-md !font-medium !bg-Primary text-base`}
          PrimaryButtonDisable={!isValid}
          secondaryButtonText="Cancel"
          typeOfSecondaryButton="button"
          clickOnSecondaryButton={() => handleClose()}
          SecondaryButtonStyle={tw`flex-1 !py-2.5 !rounded-md !font-medium !bg-white !border-border_stroke text-text_primary !text-base`}
        />
      )}
      {isDoneModalOpen && (
        <>
          <ActionModal
            open={isDoneModalOpen}
            handleClose={() => setIsDoneModalOpen(false)}
            customIcon={checkCircle}
            customHeaderStyle={tw`p-3 border-8 mb-2 border-success_50 bg-success_100 rounded-full`}
            description={
              <div tw="max-w-[22rem]">
                <span tw="font-medium text-text_primary">{selectedTherapist.patient_name}</span> has
                been transferred to{' '}
                <span tw="font-medium text-text_primary">{selectedTherapist.therapist_name}.</span>
              </div>
            }
            PrimaryButtonStyle={tw`flex-1 !py-2.5 !rounded-md !font-medium !bg-white hover:!bg-white border-border_stroke text-text_primary !text-base`}
            primaryActionHandler={handleClose}
            actionButtonText="Done"
            hasSecondaryButton={false}
          />
        </>
      )}
    </>
  );
};

export default TransferPatient;
