import 'twin.macro';
import NoDataAvailableIco from '@assets/svgs/settings/no log available.svg';

const NoLogs = ({ label, subtitle, icon }) => {
  return (
    <div tw="flex flex-col gap-2 justify-center items-center w-full h-full text-center">
      <img src={icon ? icon : NoDataAvailableIco} alt="No data" />
      <p tw="text-[1.25rem] font-semibold">{label}</p>
      <p tw="text-[.875rem] text-text_secondary w-[30%]">{subtitle}</p>
    </div>
  );
};

export default NoLogs;
