import { useEffect, useState } from 'react';
import BlingAddress from '../bilingAddress';
import BlingCycle from '../bilingCycle';
import InviteMember from '../inviteMember';
import PaymentMethods from '../paymentMethods';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import 'twin.macro';

// Added back necessary props for child components
const CheckoutSection = ({
  selectedPlan,
  append,
  fields,
  control,
  setSelectedPlan,
  getValues,
  register,
  setValue,
  remove,
  watch,
  message,
  errors,
  setError,
  clearErrors,
}) => {
  const [stripePromise, setStripePromise] = useState(null);
  const [clientSecret, setClientSecret] = useState('');

  useEffect(() => {
    fetch('http://localhost:5252/config').then(async r => {
      const { publishableKey } = await r.json();
      setStripePromise(loadStripe(publishableKey));
    });
  }, []);

  useEffect(() => {
    fetch('http://localhost:5252/create-payment-intent', {
      method: 'POST',
      body: JSON.stringify({}),
    }).then(async result => {
      var { clientSecret } = await result.json();
      setClientSecret(clientSecret);
    });
  }, []);
  return (
    <div className="element" tw="w-[50%] bg-white px-[7%] sticky top-0 h-screen overflow-y-auto">
      <h1 tw="text-[2rem] z-10 bg-white p-[2%] font-[700] text-Primary_600 mb-6 sticky top-0 left-0">
        Checkout
      </h1>
      <div tw="grid gap-8">
        <BlingCycle selectedPlan={selectedPlan} setSelectedPlan={setSelectedPlan} />
        <InviteMember
          append={append}
          control={control}
          fields={fields}
          getValues={getValues}
          register={register}
          setValue={setValue}
          remove={remove}
          errors={errors}
          setError={setError}
          clearErrors={clearErrors}
        />
        <BlingAddress control={control} register={register} errors={errors} />
        <Elements stripe={stripePromise} options={{ clientSecret }}>
          <PaymentMethods
            control={control}
            message={message}
            register={register}
            watch={watch}
            errors={errors}
          />
        </Elements>
      </div>
    </div>
  );
};

export default CheckoutSection;
