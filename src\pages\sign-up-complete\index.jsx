import AccountCreatedImage from '@/assets/svgs/auth/account-created.svg';
import PrimaryButton from '@/components/shared/primaryButton';
import { useNavigate } from 'react-router-dom';
import 'twin.macro';

const SignUpComplete = () => {
  const navigate = useNavigate();
  return (
    <div tw="flex flex-col gap-4 justify-center items-center w-screen h-screen text-center">
      <img src={AccountCreatedImage} alt="account created" />
      <div tw="px-[2%] w-[35%] grid gap-2">
        <h1 tw="font-['Inter'] font-[600] text-[2.1rem]">Account Created</h1>
        <div tw="mb-6 text-center">
          <span tw="font-['Inter'] font-[400] text-text_secondary [line-height: 130%] text-[1.1rem]">
            Welcome to Rehabitaire! Your account for Clinic Name has been created successfully.
          </span>
        </div>
        <PrimaryButton
          tw="w-full py-[12px] px-[16px] rounded-[6px] text-[1rem] font-[600] font-['Inter']"
          text={'Continue'}
          type="button"
          handleClick={() => navigate('/login', { replace: true })}
        />
      </div>
    </div>
  );
};

export default SignUpComplete;
