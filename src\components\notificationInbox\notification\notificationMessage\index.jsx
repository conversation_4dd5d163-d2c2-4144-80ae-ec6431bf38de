import React from 'react';
import tw from 'twin.macro';
import { Link } from 'react-router-dom';

const NotificationMessage = ({ notification, setOpen }) => {
  const handleClickLink = event => {
    event.stopPropagation(); // Prevent the event from propagating to the parent
    setOpen(false); // Close the notification inbox
  };
  const MediumText = ({ text }) => {
    return <span css={tw`text-text_primary font-medium`}>{text}</span>;
  };
  const LinkText = ({ text, to }) => {
    return (
      <Link
        to={to}
        css={tw`text-info font-medium hover:underline`}
        onClick={event => handleClickLink(event)}
      >
        {text}
      </Link>
    );
  };

  const InvitationMessage = () => {
    return (
      <>
        <MediumText text={notification.PT} /> invited{' '}
        <LinkText text={notification.patientName} to={`/patients/${notification.patientId}`} /> to
        join <MediumText text="Rehabitaire." />
      </>
    );
  };

  const DeleteMessage = () => {
    return (
      <>
        <LinkText text={notification.patientName} to={`/patients/${notification.patientId}`} /> was
        discharged by PT <MediumText text={notification.PT} />.
      </>
    );
  };

  const CompleteMessage = () => {
    return (
      <>
        <LinkText text={notification.patientName} to={`/patients/${notification.patientId}`} />{' '}
        completed exercise session for <MediumText text={notification.exerciseName} /> with a rating
        of <MediumText text={notification.rating} />.
      </>
    );
  };

  const ReportMessage = () => {
    return (
      <>
        <LinkText text={notification.patientName} to={`/patients/${notification.patientId}`} />{' '}
        reported pain while executing <MediumText text={notification.exerciseName} /> in their
        latest session.
      </>
    );
  };
  const messageMap = {
    invitation: <InvitationMessage />,
    discharge: <DeleteMessage />,
    sesssionCompletion: <CompleteMessage />,
    painReport: <ReportMessage />,
  };
  return (
    <div css={tw`text-[0.8rem] text-text_secondary font-normal`}>
      {messageMap[notification.type]}
    </div>
  );
};

export default NotificationMessage;
