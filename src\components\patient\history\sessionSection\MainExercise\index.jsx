import 'twin.macro';
import tw from 'twin.macro';
import VideoPlayer from '@/assets/svgs/patient/video-player.svg';
const MainExercise = ({
  title,
  set,
  inputs,
  video_url,
  thumb_url,
  openVideo,
  setOpenVideo,
  showControls,
  setShowControls,
  videoRef,
  handleOpenVideo,
}) => {
  return (
    <div tw="w-[80%] h-full p-5 rounded-md border-border_stroke border space-y-4 ">
      <div tw="h-[18%]">
        <div tw="text-lg font-semibold">
          {title} <span tw="text-Primary_600"> {set}</span>
        </div>
        <div tw="grid grid-cols-2 gap-6">
          {inputs.map((input, index) => (
            <div key={index} tw="">
              <p tw="mb-2 text-base font-medium text-text_secondary">{input.label}</p>
              <div tw="border rounded-md px-4 py-3 flex items-center gap-1.5">
                <p tw="text-sm font-medium text-black">{input.value}</p>
                {input.unit && <p tw="text-sm text-text_secondary">{input.unit}</p>}
              </div>
            </div>
          ))}
        </div>
      </div>
      <div
        tw="overflow-hidden relative h-[80%] rounded-md"
        onMouseEnter={() => {
          setShowControls(true);
        }}
        onMouseLeave={() => {
          setShowControls(false);
        }}
      >
        {!openVideo && (
          <div tw="absolute w-full h-full top-0 bottom-0 right-0 left-0 z-[1] flex items-center justify-center">
            <div
              onClick={handleOpenVideo}
              tw="w-[6rem] h-[6rem] rounded-full bg-[#3F3F3F66] flex items-center justify-center cursor-pointer"
              style={{
                boxShadow:
                  '0px 27.831px 33.397px -5.566px rgba(16, 24, 40, 0.10), 0px 11.132px 11.132px -5.566px rgba(16, 24, 40, 0.04)',
              }}
            >
              <img src={VideoPlayer} />
            </div>
          </div>
        )}
        {video_url && (
          <video
            ref={videoRef}
            src={video_url}
            poster={thumb_url}
            autoPlay={openVideo}
            controls={showControls && openVideo}
            muted
            loop
            preload="none"
            css={[tw`object-cover h-full w-full transition-opacity duration-1000`]}
          />
        )}
      </div>
    </div>
  );
};

export default MainExercise;
