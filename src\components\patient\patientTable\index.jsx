import React, { useReducer, useEffect, useRef, useCallback, useState } from 'react';
import Table from '@/components/table';
import TableHeader from '@/components/table/tableHeader';
import { useForm } from 'react-hook-form';
import { initialState, reducer, TableActionTypes } from '@reducers/table';
import { usePatientsColumns } from '@/hooks/patients/usePatientsColumns';
import { filterOptions } from '@/mock/patients-data';
import { patietReorderableColumns } from '@/constants/constants';
import patientsData from '@/mock/patients-data';
import { useFilteredTableData } from '@/hooks/tabel/useFilteredTableData';
import PatientModals from '../profile/patiemtProfileForm/patientActions/modals';
import GroupPatientAction from './groupPatientAction';
let data = patientsData;

const PatientTable = () => {
  //state
  const [state, dispatch] = useReducer(reducer, initialState(patietReorderableColumns));
  const [openGroupPatientsModal, setOpenGroupPatientsModal] = useState(null); //discharge | delete
  //patient actions moadl {type: transfer | dischare | delete, id}
  const [openModal, setOpenModal] = useState(null);
  const handleOpen = ({ type, id }) => setOpenModal({ type, id });
  const hoverEnabledRef = useRef(true);
  const columns = usePatientsColumns({ state, dispatch, hoverEnabledRef, handleOpen });

  const { register, watch, control } = useForm({
    defaultValues: { search: '', filter: { label: 'All', value: 'All patients in the platform' } },
  });

  const patient = openModal && openModal.id ? data.find(p => p.id === openModal.id) : null;
  const searchValue = watch('search');
  const filterValue = watch('filter');
  const filteredData = useFilteredTableData(filterValue.label, searchValue, data);

  // Simulate loading for 2 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      dispatch({ type: TableActionTypes.SET_LOADING, payload: false });
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  const { sorting, selectedRows, pageSize, pageIndex, reorderableColumns, dateFormat, loading } =
    state;

  const columnOrder = ['Name', ...reorderableColumns];

  const settings = { reorderableColumns, dateFormat };

  // page index correction
  useEffect(() => {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    if (pageIndex >= totalPages && totalPages > 0) {
      dispatch({
        type: TableActionTypes.SET_PAGE_INDEX,
        payload: Math.max(totalPages - 1, 0),
      });
    }
  }, [filteredData.length, pageSize, pageIndex]);

  //handelers

  const handleCloseModal = () => setOpenModal(null);
  const handleOpenGroupPateitnModal = type => setOpenGroupPatientsModal(type);
  const handleCloseGroupPatientsModal = () => setOpenGroupPatientsModal(null);

  const onSortingChange = val => {
    const next = val();
    if (next[0]?.desc === false && sorting.length === 0) {
      dispatch({ type: TableActionTypes.SET_SORTING, payload: next });
    } else if (next[0]?.desc === false) {
      dispatch({ type: TableActionTypes.SET_SORTING, payload: [] });
    } else {
      dispatch({ type: TableActionTypes.SET_SORTING, payload: next });
    }
  };

  const onChangeSettings = newSettings => {
    if (newSettings.reorderableColumns && newSettings.reorderableColumns !== reorderableColumns) {
      dispatch({
        type: TableActionTypes.SET_REORDERABLE_COLUMNS,
        payload: newSettings.reorderableColumns,
      });
    }
    if (newSettings.dateFormat && newSettings.dateFormat !== dateFormat) {
      dispatch({ type: TableActionTypes.SET_DATE_FORMAT, payload: newSettings.dateFormat });
    }
  };

  //vars
  const totalPages = Math.ceil(filteredData.length / pageSize);
  const label = selectedRows?.length > 1 ? 'Patients' : 'Patient';
  const actionOptions = [
    {
      id: 1,
      label: `Discharge ${label}`,
      value: 'Discharge Patients',
      customClickHandler: () => handleOpenGroupPateitnModal('discharge'),
    },
    {
      id: 2,
      label: `Remove ${label}`,
      value: 'Remove Patients',
      customClickHandler: () => handleOpenGroupPateitnModal('delete'),
    },
  ];

  return (
    <>
      {patient && (
        <PatientModals
          {...{
            openModal,
            handleCloseModal,
            patient: { patientName: patient.Name, patient_id: patient.id },
          }}
        />
      )}

      <GroupPatientAction
        {...{ openGroupPatientsModal, handleCloseGroupPatientsModal, data, selectedRows, label }}
      />

      <Table
        data={filteredData}
        columns={columns}
        columnOrder={columnOrder}
        sorting={sorting}
        onSortingChange={onSortingChange}
        pageSize={pageSize}
        pageIndex={pageIndex}
        onPageSizeChange={newSize =>
          dispatch({ type: TableActionTypes.SET_PAGE_SIZE, payload: newSize })
        }
        onPreviousPage={() =>
          dispatch({
            type: TableActionTypes.SET_PAGE_INDEX,
            payload: Math.max(pageIndex - 1, 0),
          })
        }
        onNextPage={() =>
          dispatch({
            type: TableActionTypes.SET_PAGE_INDEX,
            payload: Math.min(pageIndex + 1, totalPages - 1),
          })
        }
        totalPages={totalPages}
        selectedRows={selectedRows}
        hoverEnabledRef={hoverEnabledRef}
        onCurrentRowsChange={useCallback(rows => {
          dispatch({ type: TableActionTypes.SET_CURRENT_ROWS, payload: rows });
        }, [])}
        loading={loading}
        customHeader={
          <TableHeader
            control={control}
            register={register}
            filterValue={filterValue}
            selectedRows={selectedRows}
            settings={settings}
            onChangeSettings={onChangeSettings}
            filterOptions={filterOptions}
            actionOptions={actionOptions}
          />
        }
      />
    </>
  );
};

export default PatientTable;
