import React from 'react';
import tw from 'twin.macro';
import { ModalViews } from '../..';

const Actions = ({
  isPrimaryDisabled,
  handlClickPrimary,
  primaryText,
  PrimaryType = 'button',
  setModalView,
}) => {
  return (
    <div
      css={tw`px-8 py-6 flex justify-center items-center gap-3 border-t border-t-border_stroke bg-neutral_50  rounded-b-[12px]`}
    >
      <button
        css={[
          tw`px-4 py-[10px] border rounded-[6px] flex-1 text-[15px] font-medium`,
          isPrimaryDisabled
            ? tw`bg-neutral_300 border-border_stroke cursor-not-allowed`
            : tw`bg-Primary border-Primary_600`,
        ]}
        disabled={isPrimaryDisabled}
        onClick={handlClickPrimary}
        type={PrimaryType}
      >
        <span css={isPrimaryDisabled ? tw`opacity-50` : tw`opacity-100`}>{primaryText}</span>
      </button>
      <button
        css={tw`px-4 py-[10px] border border-border_stroke rounded-[6px] flex-1 text-[15px] font-medium bg-white`}
        onClick={() => setModalView(ModalViews.PRE_SESSION_MESSAGES)}
      >
        Cancel
      </button>
    </div>
  );
};

export default Actions;
