import Heart from '@assets/svgs/exercise-library/heart.svg';
import FullScreen from '@assets/svgs/exercise-library/full screen.svg';
import Play from '@assets/svgs/exercise-library/play.svg';
import React, { useRef, useEffect, useState } from 'react';

import 'twin.macro';

const ExerciseDetailsModelDescription = ({ videoUrl, description, instructions }) => {
  const [showVideoModal, setShowVideoModal] = useState(false);
  const videoRef = useRef(null);

  useEffect(() => {
    if (videoRef.current) {
      if (showVideoModal) {
        videoRef.current.play();
        setShowVideoModal(true);
      } else {
        videoRef.current.pause();
        setShowVideoModal(false);
      }
    }
  }, [showVideoModal]);

  const onPlayVideo = () => {
    setShowVideoModal(prev => !prev);
  };

  const handleFullScreen = () => {
    if (videoRef.current) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen();
      } else if (videoRef.current.webkitRequestFullscreen) {
        videoRef.current.webkitRequestFullscreen();
      } else if (videoRef.current.mozRequestFullScreen) {
        videoRef.current.mozRequestFullScreen();
      } else if (videoRef.current.msRequestFullscreen) {
        videoRef.current.msRequestFullscreen();
      }
    }
  };

  return (
    <div tw="grid grid-cols-2 gap-4">
      <div tw="relative h-full">
        {!showVideoModal && (
          <img
            src={Play}
            alt="play"
            tw="absolute top-[50%] left-[50%] cursor-pointer -translate-x-[50%] -translate-y-[50%] z-[10]"
            onClick={onPlayVideo}
          />
        )}
        <div tw="flex absolute right-2 cursor-pointer bottom-2 gap-4 items-center z-[10]">
          <img src={Heart} alt="Heart" tw="" />
          <img src={FullScreen} alt="FullScreen" tw="" onClick={handleFullScreen} />
        </div>
        <video
          autoPlay={showVideoModal}
          loop
          ref={videoRef}
          onClick={onPlayVideo}
          src={videoUrl}
          alt="video"
          tw="object-fill rounded-[6px] h-full"
        />
      </div>
      <div tw="overflow-y-auto space-y-2 max-h-[250px]" id="prescription-items">
        <div>
          <label tw="font-semibold text-[1rem]">Description:</label>
          <p tw="text-text_secondary text-[0.8rem]">{description}</p>
        </div>
        <div>
          <label tw="font-semibold text-[1rem]">Instructions:</label>
          <p tw="text-text_secondary text-[0.8rem]">{instructions}</p>
        </div>
      </div>
    </div>
  );
};

export default ExerciseDetailsModelDescription;
