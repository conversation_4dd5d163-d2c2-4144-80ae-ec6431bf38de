import { useState } from 'react';
import NormalTooltipIcon from '@assets/svgs/tooltip.svg';
import DisableTooltipIcon from '@assets/svgs/disable-tooltip-icon.svg';
import tw from 'twin.macro';
import ToolTip from '../toolTip';

const TooltipIcon = ({
  src = NormalTooltipIcon,
  text,
  isActiveOnHover,
  srcDiabled = DisableTooltipIcon,
}) => {
  const [hovered, setHovered] = useState(false);

  return (
    <div
      className="group"
      css={tw`relative flex items-center justify-center w-[16px] h-[16px]`}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      {isActiveOnHover ? (
        hovered ? (
          <>
            <img src={src} alt="tooltip-icon" width={13} />
            <ToolTip text={text} />
          </>
        ) : (
          <img src={srcDiabled} alt="tooltip-icon-disabled" width={13} />
        )
      ) : (
        <>
          <img src={src} alt="tooltip-icon" width={13} />
          <ToolTip text={text} />
        </>
      )}
    </div>
  );
};

export default TooltipIcon;
