import 'twin.macro';

const MemberSubscription = ({ IncreaseValue, title, hasSubtitle, containerStyle }) => {
  return (
    <div tw="flex justify-between items-start font-medium text-[0.8rem]" css={containerStyle}>
      <div>
        <p tw="text-[.875rem] [font: 500]">{title}</p>
        {hasSubtitle && (
          <p tw="text-[.8125rem] font-normal text-[#5E738A]">${IncreaseValue} per member</p>
        )}
      </div>
      <p tw="text-[0.875rem]">${IncreaseValue}</p>
    </div>
  );
};

export default MemberSubscription;
