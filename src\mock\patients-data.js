const patientsData = [
  {
    id: 1,
    Name: '<PERSON>',
    Status: 'High adherence',
    Prescription: 'Lower Back Pain',
    'Overall adherence': '74.3',
    Adherence: '76.3',
    'Pain level': '4 of 10',
    'Assigned PT': '<PERSON><PERSON>',
    'Email address': '<EMAIL>',
    'Date joined': '2023-05-10',
    'Last visit': '2024-04-05',
    'Overall period': '8 weeks',
    'Time left': '3 weeks left',
    'Expiration date': '2024-04-29',
  },
  {
    id: 2,
    Name: '<PERSON>',
    Status: 'High risk',
    Prescription: 'Knee injury rehab',
    'Overall adherence': '44.2',
    Adherence: '42.2',
    'Pain level': '8 of 10',
    'Assigned PT': '<PERSON><PERSON>',
    'Email address': '<EMAIL>',
    'Date joined': '2023-09-15',
    'Last visit': '2024-02-12',
    'Overall period': '6 weeks',
    'Time left': '2 weeks left',
    'Expiration date': '2024-02-27',
  },
  {
    id: 3,
    Name: '<PERSON>',
    Status: 'High adherence',
    Prescription: 'Shoulder Strengthening',
    'Overall adherence': '77.8',
    Adherence: '78.9',
    'Pain level': '4 of 10',
    'Assigned PT': '<PERSON>. <PERSON>',
    'Email address': '<EMAIL>',
    'Date joined': '2023-11-02',
    'Last visit': '2024-01-18',
    'Overall period': '10 weeks',
    'Time left': '6 weeks left',
    'Expiration date': '2024-03-01',
  },
  {
    id: 4,
    Name: '<PERSON> Carter',
    Status: 'High adherence',
    Prescription: 'Post-Surgery Core Exercise',
    'Overall adherence': '73.8',
    Adherence: '72.5',
    'Pain level': '2 of 10',
    'Assigned PT': 'B. Turner',
    'Email address': '<EMAIL>',
    'Date joined': '2023-01-20',
    'Last visit': '2024-03-28',
    'Overall period': '5 weeks',
    'Time left': '1 weeks left',
    'Expiration date': '2024-04-07',
  },
  {
    id: 5,
    Name: 'Emily Martinez',
    Status: 'No prescriptions',
    'Email address': '<EMAIL>',
    'Date joined': '2023-04-07',
    'Last visit': '2024-02-15',
  },
  {
    id: 6,
    Name: 'Jason Anderson',
    Status: 'Low adherence',
    Prescription: 'Posture correction and mobility ex...',
    'Overall adherence': '46.7',
    Adherence: '45.2',
    'Pain level': '3 of 10',
    'Assigned PT': 'S. Roaberts',
    'Email address': '<EMAIL>',
    'Date joined': '2023-06-14',
    'Last visit': '2024-11-08',
    'Overall period': '7 weeks',
    'Time left': '4 weeks left',
    'Expiration date': '2024-12-08',
  },
  {
    id: 7,
    Name: 'Olivia Taylor',
    Status: 'High adherence',
    Prescription: 'Hip and lower body strengthening',
    'Overall adherence': '87.3',
    Adherence: '86.4',
    'Pain level': '4 of 10',
    'Assigned PT': 'K. Davis',
    'Email address': '<EMAIL>',
    'Date joined': '2023-03-05',
    'Last visit': '2024-09-19',
    'Overall period': '4 weeks',
    'Time left': '5 weeks left',
    'Expiration date': '2024-10-28',
  },
  {
    id: 8,
    Name: 'Ethan Miller',
    Status: 'Inactive',
    Prescription: 'Back mobility exercise',
    'Overall adherence': '56.7',
    Adherence: '52.7',
    'Pain level': '5 of 10',
    'Assigned PT': 'R. Parker',
    'Email address': '<EMAIL>',
    'Date joined': '2023-08-22',
    'Last visit': '2024-02-03',
    'Overall period': '6 weeks',
    'Time left': '4 weeks left',
    'Expiration date': '2024-03-03',
  },
  {
    id: 9,
    Name: 'Ava Wilson',
    Status: 'High adherence',
    Prescription: 'Lower backpain exercise',
    'Overall adherence': '88.3',
    Adherence: '87.3',
    'Pain level': '3 of 10',
    'Assigned PT': 'R. Mitchell',
    'Email address': '<EMAIL>',
    'Date joined': '2023-11-10',
    'Last visit': '2024-05-27',
    'Overall period': '8 weeks',
    'Time left': '3 weeks left',
    'Expiration date': '2024-06-12',
  },
  {
    id: 10,
    Name: 'Daniel Harris',
    Status: 'Low adherence',
    Prescription: 'TKR rehabilitation',
    'Overall adherence': '36.5',
    Adherence: '35.2',
    'Pain level': '3 of 10',
    'Assigned PT': 'T. Swift',
    'Email address': '<EMAIL>',
    'Date joined': '2023-02-18',
    'Last visit': '2024-08-14',
    'Overall period': '10 weeks',
    'Time left': '6 weeks left',
    'Expiration date': '2024-10-03',
  },
  {
    id: 11,
    Name: 'Lori Powell',
    Status: 'Discharged',
    Prescription: 'Neck pain relief',
    'Overall adherence': '82.5',
    'Assigned PT': 'T. Swift',
    'Email address': '<EMAIL>',
    'Date joined': '2023-04-23',
  },
  {
    id: 12,
    Name: 'Christopher Turner',
    Status: 'Low adherence',
    Prescription: 'Carpal tunnel exercise',
    'Overall adherence': '36.5',
    Adherence: '38.5',
    'Pain level': '3 of 10',
    'Assigned PT': 'T. Swift',
    'Email address': '<EMAIL>',
    'Date joined': '2023-01-16',
    'Last visit': '2023-07-02',
    'Overall period': '8 weeks',
    'Time left': '3 weeks left',
    'Expiration date': '2023-07-29',
  },
  {
    id: 13,
    Name: 'Sophia Miller',
    Status: 'High risk',
    Prescription: 'Jaw exercise',
    'Overall adherence': '59.8',
    Adherence: '58.3',
    'Pain level': '9 of 10',
    'Assigned PT': 'S. Roberts',
    'Email address': '<EMAIL>',
    'Date joined': '2023-04-12',
    'Last visit': '2024-10-18',
    'Overall period': '7 weeks',
    'Time left': '4 weeks left',
    'Expiration date': '2024-11-18',
  },
  {
    id: 14,
    Name: 'Jackson Wilson',
    Status: 'Inactive',
    Prescription: 'Carpal tunnel exercise',
    'Overall adherence': '69.3',
    Adherence: '66.3',
    'Pain level': '2 of 10',
    'Assigned PT': 'T. Swift',
    'Email address': '<EMAIL>',
    'Date joined': '2023-09-25',
    'Last visit': '2024-04-02',
    'Overall period': '9 weeks',
    'Time left': '5 weeks left',
    'Expiration date': '2024-03-02',
  },
  {
    id: 15,
    Name: 'Henry Adams',
    Status: 'Expired prescription',
    Prescription: 'TKR rehabilitation',
    'Overall adherence': '62.9',
    'Pain level': '3 of 10',
    'Assigned PT': 'S. Roberts',
    'Email address': '<EMAIL>',
    'Date joined': '2023-05-02',
    'Last visit': '2024-10-17',
    'Expiration date': '2024-02-04',
  },
];

const nameCellOptions = [
  { id: 1, label: 'Transform Patient', value: 'Transform Patient' },
  { id: 2, label: 'Discharge Patient', value: 'Discharge Patient' },
  { id: 3, label: 'Delete Patient', value: 'Delete Patient' },
];

const filterOptions = [
  { label: 'All', value: 'All patients in the platform' },
  {
    label: 'High adherence',
    value: 'Patients who completed +70% of their prescribed sessions during the last 2 weeks',
  },
  {
    label: 'Low adherence',
    value:
      'Patients who completed less than 70% of their prescribed sessions during the last 2 weeks',
  },
  {
    label: 'Inactive',
    value: "Patients who haven't exercised at all for at least 2 weeks",
  },
  {
    label: 'Pending',
    value: "Patients who haven't finished the onboarding process",
  },
  {
    label: 'No prescriptions',
    value: 'Patients who have no prescription',
  },
  {
    label: 'High risk',
    value: 'Patients who have reported discomfort or pain',
  },
  {
    label: 'Expiring prescriptions',
    value: 'Patients with prescriptions ending soon',
  },
  {
    label: 'Expired prescriptions',
    value: 'Patients with expired prescriptions',
  },
  {
    label: 'Discharged',
    value:
      'Patients that have completed their prescription and is no longer actively receiving therapy',
  },
];
const actionOptions = [
  { id: 1, label: 'Assign prescription', value: 'Assign Prescription' },
  { id: 2, label: 'Delete data', value: 'Delete data' },
];

const getPatientById = (id) => {
  return patientsData.find((patient) => patient.id === id);
};

export default patientsData;
export { nameCellOptions, filterOptions, actionOptions, getPatientById };
// This file contains mock data for patients, which can be used in testing or development.
