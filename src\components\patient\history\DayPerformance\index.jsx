import React from 'react';
import tw from 'twin.macro';
import { dayPerformanceStatus } from '@/constants/constants';
const DayPerformance = ({ status, performed, total }) => {
  return (
    <div tw="flex justify-center">
      <div
        tw="relative w-7 h-7 rounded-full border text-xs font-medium mb-1"
        css={[
          status === dayPerformanceStatus.MISSED && tw`border-error text-error`,
          status === dayPerformanceStatus.INCOMPLETE && tw`border-warning text-warning`,
          status === dayPerformanceStatus.COMPLETE && tw`border-success text-success`,
        ]}
      >
        <div
          tw="absolute top-[50%] translate-y-[-50%] left-0 w-full h-[0.0625rem] -rotate-45"
          css={[
            status === dayPerformanceStatus.MISSED && tw`bg-error `,
            status === dayPerformanceStatus.INCOMPLETE && tw`bg-warning`,
            status === dayPerformanceStatus.COMPLETE && tw`bg-success `,
          ]}
        />
        <div tw="pl-1.5 -mb-[.3125rem] flex">{performed}</div>
        <div tw="pr-1.5  flex justify-end">{total}</div>
      </div>
    </div>
  );
};

export default DayPerformance;
