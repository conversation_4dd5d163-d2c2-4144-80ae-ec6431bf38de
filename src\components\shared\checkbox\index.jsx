import tw from 'twin.macro';

/**
 * Input component for form input
 * @param {string} props.checkboxCustomStyle - Additional CSS styles for the input
 * @param {string} props.labelStyle - Additional Label CSS styles
 * @param {string} props.containerStyle - Additional CSS styles for the container
 * @param {string} props.label - Label for input
 * @param {boolean} props.checked - Whether the checkbox is checked
 * @param {function} props.onChange - Function to call when checkbox changes
 * @param {Object} props.register - React Hook Form register function
 * @param {string} props.name - Name of the field in the form
 * @param {Object} props.rest - Additional props to pass to the input element
 * @param {otherSection} props.otherSection- Custom component side checkbox
 * @param {onMouseEnter} props.onMouseEnter- Function for detect if user enter on checkbox
 * @param {onMouseLeave} props.onMouseLeave- Function for detect if user leave on checkbox
 */

const Checkbox = ({
  containerStyle,
  checkboxCustomStyle,
  labelCustomStyle,
  label,
  hasHovering,
  checked,
  indeterminate,
  onChange,
  register,
  name,
  otherSection,
  onMouseEnter,
  onMouseLeave,
  ...rest
}) => {
  return (
    <div
      css={[containerStyle, hasHovering ? tw`hover:bg-Primary_100` : tw``]}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      tw="flex items-start duration-500 ease-in-out cursor-pointer gap-2 rounded-[4px] px-[10px] py-[6px]"
    >
      <input
        type="checkbox"
        title={label}
        checked={checked}
        onChange={onChange}
        {...(register && name ? register(name) : {})}
        css={[
          tw`appearance-none w-[16px] h-[16px] border-border_stroke border rounded-[2.675px] bg-white focus:outline-none transition duration-200 relative cursor-pointer top-[3px]`,
          {
            '&:checked': {
              backgroundImage:
                "url(\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\")",
              backgroundSize: '100% 100%',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            },
            '&:indeterminate': {
              backgroundImage:
                'url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMyIgdmlld0JveD0iMCAwIDEwIDMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHk9IjAuMTg3NSIgd2lkdGg9IjEwIiBoZWlnaHQ9IjIiIHJ4PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K")',
              backgroundSize: '60% 60%',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            },
            '&:hover:not(:checked):not(:indeterminate)': {
              backgroundImage:
                "url(\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='%23e0e0e0' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\")",
              backgroundSize: '120% 120%',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            },
          },
          checkboxCustomStyle,
          checked || indeterminate ? tw`bg-Primary_600 border-Primary_800` : tw``,
        ]}
        {...rest}
      />
      <p tw="text-[0.787em] [line-height: 130%] flex-1" css={labelCustomStyle}>
        {label}
      </p>
      {otherSection}
    </div>
  );
};

export default Checkbox;
