import React from 'react';
import tw from 'twin.macro';
import GenericModal from '@/components/genericModal';
import userRounded from '@/assets/svgs/patient/user-rounded-outline.svg';
import ExerciseDetailsModelHeader from '@/components/exerciseLibrary/exerciseDetailsModelHeader';
import ActionModalHeader from '@/components/actionModelHeader';
import PaymentMethods from './paymentMehtods';
const PaymentView = ({ open, handleClose, handelAction, control, paymentInfo }) => {
  return (
    <GenericModal
      openModel={open}
      handelCloseMode={handleClose}
      handelFormSubmit={handelAction}
      containerModalStyle={[
        tw`!w-[34rem] md:!min-w-0 !min-w-0 !rounded-xl h-fit shadow-[0rem_0.3125rem_0.9375rem_0rem_rgba(0,0,0,0.08),0rem_0.9375rem_2.1875rem_-0.3125rem_rgba(17,24,38,0.2),0rem_0rem_0rem_0.0625rem_rgba(152,161,178,0.1)] !overflow-hidden`,
      ]}
      title={
        <ExerciseDetailsModelHeader
          containerStyle={tw`border-none pb-0`}
          handelCloseClick={handleClose}
          customTitle={
            <ActionModalHeader
              customStyle={tw`!bg-Primary_100 !border-Primary_50`}
              icon={userRounded}
            />
          }
          hideArrow={true}
        />
      }
      titleStyle={tw`!p-0 `}
      content={
        <PaymentMethods
          {...{ control, name: paymentInfo.name, initialMethod: paymentInfo.method }}
        />
      }
      contetnContainerStyle={tw` min-h-[0px]`}
      PrimaryButtonStyle={tw`flex-1 !py-[10px] !rounded-[6px] !font-medium`}
      PrimaryButtonText={'Confirm Payment'}
      SecondaryButtonStyle={tw`flex-1 !py-[10px] !rounded-[6px] !font-medium bg-white !border-border_stroke text-text_primary text-sm`}
      secondaryButtonText="Cancel"
      clickOnSecondaryButton={handleClose}
    />
  );
};

export default PaymentView;
