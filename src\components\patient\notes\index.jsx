import PrimaryButton from '@/components/shared/primaryButton';
import 'twin.macro';
import tw from 'twin.macro';

import { useEffect, useState } from 'react';
import { getAllNotesByPatientId } from '@/mock/notes-data';
import NoteCard from './noteCard';
import SectionCards from '@/components/sectionsCard';
import AddNewNote from './addNewNote';

const Notes = ({ patient_id }) => {
  const [open, setOpen] = useState(false);

  const [notes, setNotes] = useState(null);
  const [noteToUpdate, setNoteToUpdate] = useState(null);
  //this pram will send from addNewNoteModal
  const handleAddNewNote = ({ images, documents, content }) => {
    const newNote = {
      id: 15,
      published_date: new Date().toDateString(),
      author: notes.therapist.name,
      images,
      documents,
      content,
    };

    setNotes({ ...notes, data: [newNote, ...notes.data] });
  };

  const handleUpdateNote = updatedNote => {
    const updateNotes = notes.data.map(note => {
      if (note.id === updatedNote.id) {
        return updatedNote;
      }
      return note;
    });

    setNotes({ ...notes, data: updateNotes });
    setNoteToUpdate(null);
  };
  const handleDeleteNote = id => {
    const updatedNotes = notes.data.filter(note => note.id !== id);

    setNotes({ ...notes, data: updatedNotes });
  };
  useEffect(() => {
    const notes = getAllNotesByPatientId();
    setNotes(notes);
  }, []);

  return (
    <>
      <SectionCards customStyle={tw` p-5`}>
        <div tw="flex justify-end mb-5">
          <PrimaryButton
            text={'Add new note'}
            customStyle={tw`min-w-[10.625rem]`}
            handleClick={() => setOpen(true)}
          />
        </div>
        {/* Notes list  */}
        <div tw="grid grid-cols-3 gap-6">
          {notes &&
            notes.data?.map(note => (
              <div
                key={note.id}
                tw="cursor-pointer"
                onClick={() => {
                  setNoteToUpdate(note);
                  setOpen(true);
                }}
              >
                <NoteCard {...{ ...note, handleDeleteNote }} />
              </div>
            ))}
        </div>
      </SectionCards>

      <AddNewNote
        {...{ open, setOpen, noteToUpdate, setNoteToUpdate, handleAddNewNote, handleUpdateNote }}
      />
    </>
  );
};
export default Notes;
