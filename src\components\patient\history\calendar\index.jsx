import CalendarComponent from '@/components/calendar/calendar';
import LeftArrow from '@assets/svgs/left-arrow.svg';
import 'twin.macro';
import { useEffect, useMemo, useState } from 'react';
import { getCalnderDataByMonthAndYear, getHistoryPrescriptionById } from '@/mock/historty-data';
import moment from 'moment';
import LegendData from './LegendData';
import DayStatus from './DayStatus';
import SectionCards from '@/components/sectionsCard';
import tw from 'twin.macro';

const Calendar = ({ setPrescription, ref }) => {
  const [month, setMonth] = useState(new Date());

  const [historyData, setHistoryData] = useState(null);
  const [selectedDay, setSelectedDay] = useState(null);

  useEffect(() => {
    const data = getCalnderDataByMonthAndYear(month);

    if (data) {
      const currentMonth = new Date(historyData?.month).getMonth();
      const previousMonth = new Date(month).getMonth();

      if (data.days.length > 0 && currentMonth !== previousMonth)
        setSelectedDay(data.days[data.days.length - 1]);
      else {
        const selectedDayFromData = data.days.find(
          item => item.date === moment(month).format('YYYY-MM-DD')
        );
        setSelectedDay(selectedDayFromData);
      }
      setHistoryData(data);
    } else {
      setHistoryData(null);
      setSelectedDay({ date: moment(month).format('YYYY-MM-DD'), prescription_id: null });
    }
  }, [month]);

  useEffect(() => {
    if (!selectedDay?.prescription_id) setPrescription(null);
    else {
      const prescription = getHistoryPrescriptionById(selectedDay.prescription_id);

      setPrescription(prescription);
    }
  }, [selectedDay]);

  const handleSelectDay = value => {
    const dayDate = typeof value === 'string' ? value : moment(value.start).format('YYYY-MM-DD');
    const selectedDay = historyData?.days?.find(item => item.date === dayDate);

    if (selectedDay) {
      setSelectedDay(selectedDay);
    } else {
      setSelectedDay({ date: dayDate, prescription_id: null });
    }
  };

  const mappedDilyStatus = useMemo(() => {
    if (!historyData) return null;
    return historyData.days.map((item, idx) => ({
      ...item,
      customComponent: <DayStatus key={idx} item={item} />,
    }));
  }, [historyData?.days]);

  return (
    <SectionCards customStyle={tw`w-1/3 p-4 space-y-4`} ref={ref}>
      {/* Calendar */}

      <CalendarComponent
        grayIcon={LeftArrow}
        currentDate={month}
        onDateChange={date => {
          setMonth(date);
        }}
        dailyStatus={mappedDilyStatus || undefined}
        onSelectSlot={handleSelectDay}
        selectedDayDate={selectedDay?.date}
        joinedDate={historyData?.patient_joind_date}
        rollingAdherence={historyData?.rolling_adherence}
        monthes={historyData?.history_date_year_based}
      />

      {/* Legend */}
      <LegendData />
    </SectionCards>
  );
};

export default Calendar;
