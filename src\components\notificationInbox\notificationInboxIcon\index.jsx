import React, { useEffect, useState } from 'react';
import useRemToPx from '@/hooks/useRemToPx';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '@tailwind';

const fullConfig = resolveConfig(tailwindConfig);
const { Primary_800 } = fullConfig.theme.colors;

const NotificationInboxIcon = ({ onClick, ref, open }) => {
  const [isFilled, setIsFilled] = useState(false);
  const handleMouseEnter = () => setIsFilled(true);
  const handleMouseLeave = () => {
    if (!open) {
      // Only reset if the icon is not open
      setIsFilled(false);
    }
  };

  useEffect(() => {
    setIsFilled(open);
  }, [open]);

  const widthInPixels = useRemToPx(1.667);

  return (
    <svg
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      ref={ref}
      width={widthInPixels}
      height={widthInPixels} // Set height to match width
      viewBox="0 0 33 33"
      fill={isFilled ? Primary_800 : 'none'}
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
      style={{ cursor: 'pointer' }}
    >
      <path
        d="M25.1251 13.4468V12.5068C25.1251 7.34814 21.0931 3.16681 16.1251 3.16681C11.1571 3.16681 7.12508 7.34814 7.12508 12.5068V13.4468C7.12669 14.5689 6.80648 15.6679 6.20242 16.6135L4.72508 18.9135C3.37708 21.0135 4.40642 23.8681 6.75175 24.5321C12.8799 26.2694 19.3703 26.2694 25.4984 24.5321C27.8438 23.8681 28.8731 21.0135 27.5251 18.9148L26.0478 16.6148C25.4432 15.6694 25.1225 14.5703 25.1238 13.4481L25.1251 13.4468Z"
        stroke={isFilled ? 'none' : Primary_800}
        strokeWidth="2.00537"
      />
      <path
        d="M10.125 25.8334C10.9983 28.164 13.3543 29.8334 16.125 29.8334C18.8957 29.8334 21.2517 28.164 22.125 25.8334"
        stroke={isFilled ? 'none' : Primary_800}
        strokeWidth="2.00537"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default NotificationInboxIcon;
