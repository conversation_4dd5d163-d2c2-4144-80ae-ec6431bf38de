import React from 'react';
import Tag from '@/components/exerciseLibrary/tag';
import tw from 'twin.macro';

const MessageTag = ({ isActive, type }) => {
  const customStyle = [
    tw`!px-[10px] !py-[1px] !font-normal !border`,
    isActive ? tw`bg-info_light text-info border-info` : tw`bg-[#BDBDBD] text-text_primary`,
  ];
  return <Tag text={type} otherStyle={customStyle} />;
};

export default MessageTag;
