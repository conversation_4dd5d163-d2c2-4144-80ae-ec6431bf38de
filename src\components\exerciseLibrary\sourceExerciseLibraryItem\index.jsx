import ExerciseCard from '@/components/exerciseCard';
import Add from '@/components/exerciseCard/buttons/add';
import Delete from '@/components/exerciseCard/buttons/delete';
import Duplicate from '@/components/exerciseCard/buttons/duplicate';
import Favourite from '@/components/exerciseCard/buttons/favourite';
import { useDrag } from 'react-dnd';
import tw from 'twin.macro';

const SourceItem = ({
  item,
  selectedCard,
  handleDuplicate,
  handleDelete,
  setSelectedCard,
  handleFavouriteClick,
  canDrag = true,
  handelCardClick,
  showActions,
  state,
  showDuplicate = true,
  showAdd = true,
  showDelete = true,
  params,
  showFavorite = true,
}) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'EXERCISE',
    item: { id: item.id, type: 'source' },
    collect: monitor => ({
      isDragging: !!monitor.isDragging(),
    }),
    canDrag: canDrag,
  }));

  const isSelected = selectedCard?.some(selectedItem => selectedItem.title === item.title);

  const handleAddClick = e => {
    const newItem = {
      ...item,
      id: `target-id-${item.id}`,
      isDuplicate: false,
    };

    // Check if we're in template tab (activePrescriptionTab === 1)
    // if (state?.activePrescriptionTab === 1) {
    //   addEditTemplateData([newItem]);
    // } else {
    // Default behavior for prescription tab (activePrescriptionTab === 0)
    setSelectedCard(prev => [...prev, newItem]);
    // }
    e.stopPropagation();
  };

  return (
    <div
      ref={drag}
      style={{
        opacity: isDragging ? 0.5 : 1,
        transform: isDragging ? 'scale(1.05)' : 'scale(1)',
        transition: 'all 0.2s ease',
        cursor: 'grab',
      }}
    >
      <ExerciseCard
        hasShadowOnHover
        imageUrl={item.imgUrl}
        videoUrl={item.videoUrl}
        tagText="Exercise"
        showActions={showActions}
        title={item.title}
        handelCardClick={handelCardClick}
        containerStyle={[
          selectedCard?.some(selectedItem => selectedItem.title === item.title)
            ? tw`border-2 border-Primary_600`
            : tw``,
          tw`transition-all duration-200 ease-in-out`,
          state?.selectedFilteredItem?.some(val => val === item.id)
            ? tw`border-2 border-Primary_600`
            : tw``,
        ]}
        actionsList={[
          isSelected && showDuplicate && (
            <Duplicate
              key={0}
              handleClick={() => {
                handleDuplicate(item);
              }}
            />
          ),
          isSelected && showDelete && (
            <Delete
              key={1}
              handleClick={() => {
                handleDelete(
                  selectedCard
                    ?.slice()
                    .reverse()
                    .find(selectedItem => selectedItem.title === item.title)?.id
                );
              }}
            />
          ),
          !isSelected && showAdd && <Add key={4} handleClick={handleAddClick} />,
        ].filter(Boolean)}
        favourite={
          showFavorite && (
            <Favourite
              checked={item.isFavorite}
              onClick={e => {
                handleFavouriteClick();
                e.stopPropagation();
              }}
            />
          )
        }
        params={params}
      />
    </div>
  );
};

export default SourceItem;
