import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useState } from 'react';
import TImer from '@assets/svgs/auth/otp-timer-icon.svg';
import { useAuth } from '@/zustand/auth-store';
import AuthLayout from '@/components/auth/authLayout';
import OtpInput from '@/components/auth/otpInputs';
import tw from 'twin.macro';
import * as yup from 'yup';
import BackButton from '@/components/shared/backButton';
// import { showSuccess } from "@/libs/react.toastify";

// otp-page validation Yup
const schemaOtpPage = yup
  .object({
    otp: yup.string(),
  })
  .required();

// inital state for OTP page
const initialStateOTP = {
  email: '',
  name: '',
  phoneNumber: '',
  otp: '',
};

const OtpPageLogin = () => {
  const [isSubmiting, setIsSubmiting] = useState(false);
  const [otpLastValue, setOtpLastValue] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [isFalseValue, setIsFalseValue] = useState(false);
  const [isTrueValue, setIsTrueValue] = useState(false);
  const [isReset, setIsRest] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60); // 60 seconds = 1 minute
  const email = localStorage.getItem('rehab-email');
  const from = localStorage.getItem('from');

  const { control } = useForm({
    defaultValues: initialStateOTP,
    resolver: yupResolver(schemaOtpPage),
  });

  useEffect(() => {
    if (!JSON.parse(email)) {
      navigate('/login', { replace: true });
    }
  }, []);

  const addToken = useAuth(state => state.addUserEmail);
  const navigate = useNavigate();

  // Timer effect
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft(prev => prev - 1);
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [timeLeft]);

  const handleSubmit = async () => {
    setIsFalseValue(false);
    try {
      setErrorMessage('');
      setIsFalseValue(false);
      setIsTrueValue(false);
      setIsSubmiting(true);
      // const { data } = await AuthApis.verifyOtp({
      //   email: JSON.parse(email),
      //   otp: otpLastValue,
      // });
      // if (data?.tokens) addToken(data.tokens.access);
      setIsSubmiting(false);
      if (JSON.parse(from) === 'signUp') {
        navigate('/account-created', { replace: true });
      } else {
        navigate('/', { replace: true });
      }
      setIsTrueValue(true);
    } catch (error) {
      if (error?.response?.status === 401) {
        navigate('/link-expire', { relative: true });
      }
      if (error?.response?.data?.error) {
        setErrorMessage(error?.response?.data?.error);
      }
      setIsSubmiting(false);
      setIsFalseValue(true);
    }
  };

  const resendOtp = async () => {
    try {
      if (!isReset) {
        setIsRest(true);
        setErrorMessage('');
        setIsFalseValue(false);
        // const { detail } = await AuthApis.resendOtp({
        //   email: JSON.parse(email),
        // });
        // showSuccess(detail);
        setIsRest(false);
        setTimeLeft(60); // Reset timer to 1 minute
      }
    } catch (error) {
      setIsFalseValue(true);
      setIsRest(false);
      setErrorMessage(error.response.data.message);
    }
  };

  useEffect(() => {
    if (otpLastValue.length === 6) {
      handleSubmit();
    }
  }, [otpLastValue.length, otpLastValue]);

  const formatTime = seconds => {
    if (seconds >= 60) {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins}m ${secs}s`;
    }
    return `${seconds}s`;
  };

  return (
    <AuthLayout
      customContainerStyle={tw`space-y-6`}
      authRedirectSection={tw`pt-[0.75rem]`}
      aboveTitle={
        <BackButton
          handelClick={() => {
            if (JSON.parse(from) === 'signUp') {
              navigate('/sign-up', { replace: true });
            } else {
              navigate('/login', { replace: true });
            }
          }}
        />
      }
      authSection={
        <>
          <OtpInput
            isFalseValue={isFalseValue}
            isTrueValue={isTrueValue}
            setOtpLastValue={setOtpLastValue}
            iseReset={isReset}
            errorMessage={errorMessage}
          />
          <div tw="flex justify-between items-center w-full">
            <span tw="font-[500] text-[.875rem] font-['Inter']">
              Didn't get the code?
              <span
                tw="ms-[4px] cursor-pointer font-['Inter'] font-[500] "
                css={timeLeft === 0 ? tw`text-info ` : tw`text-text_primary opacity-50`}
                onClick={timeLeft === 0 ? resendOtp : undefined}
              >
                Resend it
              </span>
            </span>
            <p tw="text-[.8125rem] font-['Inter'] flex items-center gap-1">
              <img tw="w-[15px] font-['Inter'] h-[15px]" src={TImer} alt="Timer" />
              {formatTime(timeLeft)}
            </p>
          </div>
        </>
      }
      footerLinkText={'Let us know'}
      footerNormalText={JSON.parse(from) === 'signUp' ? 'Facing problems?' : 'Facing problems?'}
      hasAuthFooter
      isLogIn
      hasAuthRedirectFooter
      redirectLink={JSON.parse(from) === 'signUp' ? '/sign-up' : '/login'}
      redirectLinkText={'Edit now'}
      redirectNormalText={'Incorrect email address?'}
      hasOpacity={false}
      subtitle={
        <div>
          <span tw="text-text_secondary font-normal opacity-50 font-['Inter'] text-[1.125rem] mt-2">
            Enter the OTP code sent to
          </span>
          <span tw="opacity-50 font-semibold font-['Inter'] ms-1 text-[1.125rem]">
            {String(JSON.parse(email)).toLocaleLowerCase()}
          </span>
        </div>
      }
      title={'Verify your email'}
    />
  );
};
export default OtpPageLogin;
