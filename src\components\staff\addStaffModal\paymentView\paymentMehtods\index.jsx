import 'twin.macro';

import PaymentMethodsList from './paymentMethodsList';
import { useState } from 'react';
import PaymentInfromation from './paymentInformation';

function PaymentMethods({ control, name, initialMethod }) {
  const [method, setMehtod] = useState(initialMethod);
  return (
    <div tw="pt-4 ">
      <PaymentInfromation {...{ name, method }} />
      <PaymentMethodsList {...{ control, setMehtod }} />
    </div>
  );
}

export default PaymentMethods;
