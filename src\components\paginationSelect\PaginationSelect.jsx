import { useState, useRef } from 'react';
import SelectArrowIcon from '@/assets/svgs/pagination-select-arrow-icon.svg';
import useClickOutside from '@/hooks/useClickOutside';
import tw from 'twin.macro';

const PaginationSelect = ({ pageSizes, onPageSizeChange, currentPageSize }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPageSize, setSelectedPageSize] = useState(currentPageSize);
  const buttonRef = useRef(null);
  useClickOutside([buttonRef], () => setIsOpen(false));

  const handlePageSizeChange = size => {
    setSelectedPageSize(size);
    setIsOpen(false);
    onPageSizeChange(size);
  };

  return (
    <div tw="relative gap-2 items-center">
      <div tw="flex gap-3 items-center px-2 py-[8px]">
        <div>
          <p tw="text-[0.91875em]">Rows per page</p>
        </div>
        <div
          tw="flex justify-center items-center h-[32px] px-[10px] py-[8px] rounded-md border border-stroke cursor-pointer relative"
          onClick={() => setIsOpen(!isOpen)}
          ref={buttonRef}
        >
          <div tw="flex  items-center gap-[6px]">
            <p tw="text-[0.91875em]">{selectedPageSize}</p>
            <img
              width={20}
              src={SelectArrowIcon}
              alt="select-arrow"
              tw="transition-transform duration-200"
              style={{
                transform: isOpen ? 'rotate(0deg)' : 'rotate(180deg)',
              }}
            />
          </div>
          {isOpen && (
            <ul tw="absolute bottom-[120%] left-0 w-full p-[4px] text-center bg-white rounded-md border border-gray-300 z-[100] flex flex-col gap-1">
              {pageSizes.map(item => (
                <div key={item}>
                  <li
                    tw="py-[2px] rounded-[6px] cursor-pointer hover:(bg-Primary_600 text-white)"
                    css={currentPageSize === item && tw`bg-Primary_600 text-white`}
                    onClick={() => handlePageSizeChange(item)}
                  >
                    {item}
                  </li>
                </div>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaginationSelect;
