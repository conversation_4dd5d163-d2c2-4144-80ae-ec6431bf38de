import React, { useReducer, useEffect, useRef, useCallback, useState } from 'react';
import Table from '@/components/table';
import TableHeader from '@/components/table/tableHeader';
import { useForm } from 'react-hook-form';
import { initialState, reducer, TableActionTypes } from '@reducers/table';

import tw from 'twin.macro';
import { staffReorderableColumns } from '@/constants/constants';
import { useStaffColumns } from '@/hooks/staff/useStaffColumns';
import staffData, { filterOptions } from '@/mock/therapists-data';
import { useFilteredTableData } from '@/hooks/tabel/useFilteredTableData';
import StaffHeader from '@/components/staff/staffHeader';
import ActionModal from '@/components/actionModal';

const Staff = () => {
  const [state, dispatch] = useReducer(reducer, initialState(staffReorderableColumns));
  const [openRemoveGroupModal, setOpenRemoveGroupModal] = useState(false);
  const [staffRemoveId, setStaffRemoveId] = useState(null);

  const actionOptions = [
    {
      id: 1,
      label: 'Rermove Staff',
      value: 'Rermove Staff',
      customClickHandler: () => setOpenRemoveGroupModal(true),
    },
  ];
  const hoverEnabledRef = useRef(true);
  const columns = useStaffColumns({ state, dispatch, hoverEnabledRef, setStaffRemoveId });

  const { register, watch, control } = useForm({
    defaultValues: { search: '', filter: { label: 'All', value: 'All patients in the platform' } },
  });

  const searchValue = watch('search');
  const filterValue = watch('filter');
  const filteredData = useFilteredTableData(
    filterValue.label,
    searchValue,
    staffData[0].therapists
  );

  // Simulate loading for 2 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      dispatch({ type: TableActionTypes.SET_LOADING, payload: false });
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  const { sorting, selectedRows, pageSize, pageIndex, reorderableColumns, dateFormat, loading } =
    state;

  const columnOrder = ['Name', ...reorderableColumns];

  const settings = { reorderableColumns, dateFormat };

  const onSortingChange = val => {
    const next = val();
    if (next[0]?.desc === false && sorting.length === 0) {
      dispatch({ type: TableActionTypes.SET_SORTING, payload: next });
    } else if (next[0]?.desc === false) {
      dispatch({ type: TableActionTypes.SET_SORTING, payload: [] });
    } else {
      dispatch({ type: TableActionTypes.SET_SORTING, payload: next });
    }
  };

  const onChangeSettings = newSettings => {
    if (newSettings.reorderableColumns && newSettings.reorderableColumns !== reorderableColumns) {
      dispatch({
        type: TableActionTypes.SET_REORDERABLE_COLUMNS,
        payload: newSettings.reorderableColumns,
      });
    }
    if (newSettings.dateFormat && newSettings.dateFormat !== dateFormat) {
      dispatch({ type: TableActionTypes.SET_DATE_FORMAT, payload: newSettings.dateFormat });
    }
  };

  // page index correction
  useEffect(() => {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    if (pageIndex >= totalPages && totalPages > 0) {
      dispatch({
        type: TableActionTypes.SET_PAGE_INDEX,
        payload: Math.max(totalPages - 1, 0),
      });
    }
  }, [filteredData.length, pageSize, pageIndex]);

  const totalPages = Math.ceil(filteredData.length / pageSize);

  return (
    <>
      {staffRemoveId && (
        <ActionModal
          open={staffRemoveId}
          title={`Remove Staff:  ${staffData[0].therapists.find(therapist => therapist.id === staffRemoveId).Name}`}
          handleClose={() => setStaffRemoveId(null)}
          primaryActionHandler={() => setStaffRemoveId(null)}
          description="Removing staff will block their access to the system."
          SecondaryButtonStyle={tw`bg-white !border-border_stroke text-text_primary  text-base`}
          PrimaryButtonStyle={tw`flex-1 !py-2.5 !rounded-md !font-medium !bg-error_50  hover:!bg-error_100 text-error text-base`}
          actionButtonText="Remove"
        />
      )}
      {openRemoveGroupModal && (
        <ActionModal
          open={openRemoveGroupModal}
          title={`Remove Staff: ${staffData[0].therapists
            .filter(item => selectedRows.includes(item.id))
            .map(item => item.Name)
            .join(', ')}`}
          handleClose={() => setOpenRemoveGroupModal(false)}
          primaryActionHandler={() => setOpenRemoveGroupModal(false)}
          description="Removing staff will block their access to the system."
          SecondaryButtonStyle={tw`bg-white !border-border_stroke text-text_primary  text-base`}
          PrimaryButtonStyle={tw`flex-1 !py-2.5 !rounded-md !font-medium !bg-error_50  hover:!bg-error_100 text-error text-base`}
        />
      )}
      <div css={tw`w-full h-full p-5`}>
        <StaffHeader />
        <Table
          data={filteredData}
          columns={columns}
          columnOrder={columnOrder}
          sorting={sorting}
          onSortingChange={onSortingChange}
          pageSize={pageSize}
          pageIndex={pageIndex}
          onPageSizeChange={newSize =>
            dispatch({ type: TableActionTypes.SET_PAGE_SIZE, payload: newSize })
          }
          onPreviousPage={() =>
            dispatch({
              type: TableActionTypes.SET_PAGE_INDEX,
              payload: Math.max(pageIndex - 1, 0),
            })
          }
          onNextPage={() =>
            dispatch({
              type: TableActionTypes.SET_PAGE_INDEX,
              payload: Math.min(pageIndex + 1, totalPages - 1),
            })
          }
          totalPages={totalPages}
          selectedRows={selectedRows}
          hoverEnabledRef={hoverEnabledRef}
          onCurrentRowsChange={useCallback(rows => {
            dispatch({ type: TableActionTypes.SET_CURRENT_ROWS, payload: rows });
          }, [])}
          loading={loading}
          customHeader={
            <TableHeader
              control={control}
              register={register}
              filterValue={filterValue}
              selectedRows={selectedRows}
              settings={settings}
              onChangeSettings={onChangeSettings}
              actionOptions={actionOptions}
              filterOptions={filterOptions}
            />
          }
        />
      </div>
    </>
  );
};

export default Staff;
