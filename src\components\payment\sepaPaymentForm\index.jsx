import Input from '@/components/shared/input';
import Radio from '@/components/shared/radio';
import { IbanElement } from '@stripe/react-stripe-js';
import { useState } from 'react';
import 'twin.macro';

// SEPA Payment Form Component
export const SepaPaymentForm = ({ register, watch, control, errors }) => {
  const [cardErrors, setCardErrors] = useState('');
  const handleCardElementChange = event => {
    if (event.error) setCardErrors(event.error.message);
  };
  return (
    <div>
      <div tw="flex gap-4 justify-around items-center my-2 w-full">
        <Radio control={control} name="PayAs" value="individual" label="Pay as an individual" />
        <Radio control={control} name="PayAs" value="business" label="Pay as a business" />
      </div>
      <div tw="space-y-4">
        {watch('PayAs') === 'business' ? (
          <Input
            register={register}
            label="Company Name"
            name="companyName"
            placeholder="Company Name"
            errorMessage={errors.companyName?.message}
          />
        ) : (
          <div tw="grid grid-cols-2 gap-4">
            <Input
              register={register}
              label="First Name"
              name="paymentFirstName"
              placeholder="First Name"
              errorMessage={errors.paymentFirstName?.message}
            />
            <Input
              register={register}
              label="Last Name"
              name="paymentLastName"
              errorMessage={errors.paymentLastName?.message}
              placeholder="Last Name"
            />
          </div>
        )}
        <Input
          register={register}
          label="Email Address"
          name="paymentEmail"
          errorMessage={errors.paymentEmail?.message}
          placeholder="Email Address"
        />
        <div>
          <p tw="font-[600] text-[0.95rem] font-['Inter'] mb-2">IBAN</p>
          <div tw="flex items-center hover:bg-neutral_50 transition-all duration-300 px-3 gap-3 border-[1px] rounded-[6px] border-stroke">
            <IbanElement
              options={{
                supportedCountries: ['SEPA'],
                placeholderCountry: 'DE',
                style: {
                  base: {
                    fontSize: '14px',
                    color: '#424770',
                    '::placeholder': {
                      color: '#aab7c4',
                    },
                  },
                  invalid: {
                    color: '#9e2146',
                  },
                },
                hideIcon: true,
              }}
              onChange={handleCardElementChange}
              tw="bg-none w-full focus:border-[none] outline-0 text-sm py-3"
            />
          </div>
          {cardErrors && <p tw="mt-1 text-sm text-red-500">{cardErrors}</p>}
        </div>
        <p tw="font-normal text-[0.8125rem] [line-height: 130%]">
          By providing your IBAN and confirming this payment, you are authorizing Stripe Payments
          Europe Ltd, the creditor and Stripe, our payment service provider, to send instructions to
          your bank to debit your account and your bank to debit your account in accordance with
          those instructions. You are entitled to a refund from your bank under the terms and
          conditions of your agreement with your bank. A refund must be claimed within 8 weeks
          starting from the date on which your account was debited.
        </p>
      </div>
    </div>
  );
};
