import ExerciseCard from '@/components/exerciseCard';
import 'twin.macro';
import tw from 'twin.macro';

/**
 * A reusable horizontal scrollable list component for displaying prescription cards
 *
 * @param {Object} props
 * @param {Array} props.prescriptionsList - Array of prescription items to display (required)
 * @param {Object} props.prescriptionsList[].id - Unique identifier for each prescription item
 * @param {string} props.prescriptionsList[].label - Label text for the prescription item
 * @param {ReactNode} props.prescriptionsList[].element - React element to render for each prescription
 * @param {ReactNode} [props.customList] - Optional custom list content to override default list rendering
 * @param {Object} [props.containerListStyle] - Custom styles for the outer container
 * @param {Object} [props.ulStyle] - Custom styles for the unordered list element
 * @param {Object} [props.liStyle] - Custom styles for each list item
 *
 * @example
 * <PrescriptionsCardsList
 *   prescriptionsList={[
 *     {
 *       id: 1,
 *       label: "Morning Medication",
 *       element: <PrescriptionCard {...cardProps} />
 *     }
 *   ]}
 *   containerListStyle={tw`custom-style`}
 *   ulStyle={tw`custom-ul-style`}
 *   liStyle={tw`custom-li-style`}
 * />
 *
 * @returns {JSX.Element} A horizontally scrollable list of prescription cards
 *
 * @styling
 * Container:
 * - Background color: #F8F9FA
 * - Border radius: 8px
 * - Padding: 24px
 * - Horizontal scroll enabled
 *
 * List:
 * - Gap between items: 24px
 * - Bottom padding: 40px
 * - Custom scrollbar:
 *   - Width: 4px
 *   - Height: 10px
 *   - Thumb color: #E4E7EB
 *   - Thumb hover color: #BDC4C9
 *   - Thumb border radius: 4px
 *
 * List Items:
 * - Width: 250px
 * - Label: 0.78em size, bold (700)
 * - Flex shrink disabled
 */

import { DndProvider, useDrag } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import useScrollSwipe from '@/hooks/exercises-library/useScrollBySwipe';

const PrescriptionsCardsList = ({ exercisesList, customList, containerListStyle }) => {
  const { scrollRef } = useScrollSwipe();
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'EXERCISE',
    item: { id: 1, type: 'source' },
    collect: monitor => ({
      isDragging: !!monitor.isDragging(),
    }),
    canDrag: true,
  }));

  return (
    <div
      tw="overflow-x-auto bg-neutral_50 border rounded-[.5rem] border-border_stroke px-6 py-6 select-none"
      css={containerListStyle}
    >
      <ul
        ref={scrollRef}
        tw="flex gap-5 max-w-full overflow-x-auto overflow-y-auto [&::-webkit-scrollbar]:(w-[4px] h-[10px]) [&::-webkit-scrollbar-track]:bg-none [&::-webkit-scrollbar-thumb]:(bg-[#E4E7EB] cursor-pointer) [&::-webkit-scrollbar-thumb]:rounded-[4px] [&::-webkit-scrollbar-thumb]:hover:bg-[#BDC4C9]"
        css={[
          exercisesList.length >= 2 && tw`pb-[40px] md:pb-0`,
          exercisesList.length >= 3 && tw`md:pb-[40px] lg:pb-0`,
          exercisesList.length >= 5 && tw`lg:pb-[40px] xl:pb-0`,
          exercisesList.length >= 6 && tw`xl:pb-[40px] 4xl:pb-0`,
          exercisesList.length >= 6 && tw`xl:pb-[40px] 4xl:pb-0`,
          exercisesList.length >= 12 && tw`4xl:pb-[40px]`,
        ]}
      >
        {customList
          ? customList
          : exercisesList.length > 0 &&
            exercisesList.map((item, idx) => (
              <li
                ref={drag}
                key={item.id}
                tw="max-h-full shrink-0 md:w-1/3 w-1/2 lg:w-1/6 xl:w-[14%]  4xl:w-1/12"
                css={[isDragging && tw`opacity-50 cursor-grab`]}
                onDragStart={e => {
                  e.preventDefault();
                }}
                onDrag={e => {
                  e.preventDefault();
                }}
              >
                <ExerciseCard
                  hasShadowOnHover
                  imageUrl={item.image}
                  tagText={`Exercise ${idx + 1}`}
                  title={item.title}
                  params={{
                    sets: item.parameters.sets.number,
                    reps: item.parameters.reps.number,
                    readOnly: true,
                  }}
                />
              </li>
            ))}
      </ul>
    </div>
  );
};

export default PrescriptionsCardsList;
