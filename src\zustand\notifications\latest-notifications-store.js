import { create } from 'zustand';
import { latest } from '@/mock/notifications-data'; // Mock server data

const useLatestNotificationsStore = create(set => {
  // Define the store
  const store = {
    items: [], // Global latest notifications state
    head: 0, // Initialize head with 0
    loading: false, // Loading state
    step: 4, // Number of notifications to fetch or show at a time
    hasMore: true, // Whether there are more notifications to fetch

    fetchNotifications: async () => {
      set(state => ({ loading: true })); // Set loading to true
      // Return a promise that resolves after the simulated delay and state update
      return new Promise(resolve => {
        setTimeout(() => {
          set(state => {
            const start = state.items.length;
            const newLogs = latest.slice(start, start + state.step);

            const updated = {
              items: [...state.items, ...newLogs],
              head: state.items.length + newLogs.length, // Update head
              hasMore: start + state.step < latest.length,
              loading: false, // Set loading to false after fetching
            };
            resolve(updated);
            return updated;
          });
        }, 1000); // Simulate 1 second delay
      });
    },

    // Handle "Show More" logic
    handleShowMore: () => {
      set(state => {
        if (state.hasMore) {
          store.fetchNotifications();
          return {}; // No state update here; fetchNotifications will handle it
        } else {
          return {
            head: Math.min(state.head + state.step, state.items.length),
          };
        }
      });
    },

    // Handle "Show Less" logic
    handleShowLess: () =>
      set(state => ({
        head: state.step, // Reset the head to the initial step
      })),
  };

  return store;
});

export default useLatestNotificationsStore;
