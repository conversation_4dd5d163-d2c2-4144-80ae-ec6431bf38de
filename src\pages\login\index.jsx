import { useEffect, useRef, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { createFormError<PERSON>andler } from '@/utils/form-error-handler';
import AuthLayout from '@/components/auth/authLayout';
import AuthInput from '@/components/auth/authInput';
import PrimaryButton from '@/components/shared/primaryButton';
import AuthRecaptchaButton from '@/components/auth/authRecaptchaButton';
import * as yup from 'yup';
import 'twin.macro';
import tw from 'twin.macro';

//initial state for login page
const initialStateLogin = {
  email: '',
  recaptchaToken: '',
};

const schemaLogin = yup
  .object({
    email: yup
      .string()
      .email('Please enter a valid email address')
      .required('Required field.')
      .matches(/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i, 'Please enter a valid email address')
      .max(255, 'Email must not exceed 255 characters'),
  })
  .required();

const Login = () => {
  const navigate = useNavigate();
  const recaptchaRef = useRef(null);
  const [recaptchError, setRecaptchaError] = useState(false);
  const [errorMessage, setErrorMessage] = useState(false);

  useEffect(() => {
    localStorage.removeItem('from');
    localStorage.removeItem('rehab-email');
  }, []);

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { isSubmitting, errors },
    setValue,
    setError,
  } = useForm({
    defaultValues: initialStateLogin,
    resolver: yupResolver(schemaLogin),
    mode: 'onBlur',
  });

  const handleCheckIn = async data => {
    try {
      setErrorMessage('');
      setRecaptchaError(false);
      const token = await recaptchaRef.current.executeAsync();
      setValue('recaptchaToken', token);
      // const { detail } = await AuthApis.logIn(data);
      // showSuccess(detail);
      localStorage.setItem('rehab-email', JSON.stringify(data.email));
      localStorage.setItem('from', JSON.stringify('login'));
      recaptchaRef.current.reset();
      reset();
      navigate('/otp-page');
    } catch (error) {
      recaptchaRef.current.reset();
      if (error?.response?.data?.detail) {
        setErrorMessage(error.response.data.detail);
      }
      if (error.response.data.recaptchaToken) {
        setRecaptchaError(true);
      } else {
        // Use the error handler
        const handleError = createFormErrorHandler(setError);
        handleError(error);
      }
    }
  };

  return (
    <div>
      <AuthLayout
        customContainerStyle={tw`space-y-8 w-[75%]`}
        authSection={
          <>
            <AuthInput
              label={'Email address'}
              name={'email'}
              placeholder={'Email address'}
              register={register}
              type={'email'}
              errorMessage={errors.email?.message}
            />
            <AuthRecaptchaButton
              control={control}
              recaptchError={recaptchError}
              recaptchaRef={recaptchaRef}
            />
            <PrimaryButton
              disable={isSubmitting}
              text={'continue'}
              tw="w-full py-[12px] px-[16px] rounded-[6px] text-[.9375rem] font-[500] font-['Inter']"
            />
            {errorMessage && (
              <span tw="pt-2 text-sm font-['Inter']" style={{ color: '#D12E3C' }}>
                {errorMessage}
              </span>
            )}
            <div>
              <span tw="font-[500] text-[.8rem] font-['Inter']">
                By logging in, you agree to Rehabitaire&apos;s
                <Link to="/terms" target="_blank" tw="mx-[4px] font-['Inter'] underline text-info">
                  Rehabitaire's Terms
                </Link>
                and
                <Link to="/terms" target="_blank" tw="ms-[4px] font-['Inter'] underline text-info">
                  Privacy Policy
                </Link>
                .
              </span>
            </div>
          </>
        }
        footerLink={'/sign-up'}
        footerLinkText={'Let us know'}
        footerNormalText={'Facing problems?'}
        hasAuthFooter
        hasAuthRedirectFooter
        redirectLink={'/sign-up'}
        redirectLinkText={'Sign up'}
        redirectNormalText={'Don’t have an account?'}
        isLogIn
        subtitle={
          <>
            After clicking the
            <span tw="text-text_primary text-[1.125rem] font-[700] capitalize"> continue</span>{' '}
            button, we&apos;ll send a verification code to your email.
          </>
        }
        title={'Log in'}
        handelSubmit={handleSubmit(handleCheckIn)}
      />
    </div>
  );
};
export default Login;
