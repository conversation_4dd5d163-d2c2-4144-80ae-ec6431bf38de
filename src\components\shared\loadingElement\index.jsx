const LoadingElement = ({ width, height, stroke = '#475016' }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 142 142"
      className="spinner"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="70.5813"
        cy="70.5822"
        r="44.5156"
        transform="rotate(150 70.5813 70.5822)"
        stroke={stroke}
        strokeWidth="1.58984"
      />
      <path
        d="M32.0136 92.8494C28.6009 94.8198 27.3953 99.2219 29.8167 102.331C33.9752 107.67 39.1573 112.16 45.0839 115.522C53.0871 120.063 62.1578 122.384 71.3585 122.246C80.5591 122.108 89.5559 119.515 97.4191 114.736C103.242 111.196 108.287 106.553 112.283 101.091C114.61 97.9107 113.272 93.5468 109.802 91.6799V91.6799C106.331 89.8131 102.052 91.1706 99.5605 94.224C96.8829 97.5061 93.656 100.323 90.0069 102.541C84.3155 106 77.8035 107.877 71.1439 107.977C64.4844 108.077 57.9189 106.397 52.1261 103.111C48.4119 101.003 45.1018 98.2846 42.3267 95.0845C39.745 92.1073 35.4264 90.8791 32.0136 92.8494V92.8494Z"
        fill={stroke}
      />
    </svg>
  );
};
export default LoadingElement;
