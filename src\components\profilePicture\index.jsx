import { useState, useEffect, useRef } from 'react';
import Profile from '@assets/svgs/patient/prfile.svg';
import { showError } from '@/libs/react.toastify';
import GenericModal from '../genericModal';
import FileUploaderIcon from '@assets/svgs/settings/file-uploader-icon.svg';
import UploadIcon from '@assets/svgs/settings/upload-camera.svg';
import ExerciseDetailsModelHeader from '../exerciseLibrary/exerciseDetailsModelHeader';
import tw from 'twin.macro';
import { AnimatePresence, motion } from 'framer-motion';

const MAX_FILE_SIZE_MB = 10;
const ACCEPTED_TYPES = ['image/jpeg', 'image/png', 'image/jpg'];

const ProfilePicture = ({
  onFileUpload,
  handelRemoveImage,
  text,
  imageStyle,
  containerStyle,
  cancelIconStyle,
  iconStyle,
  disabled = false,
}) => {
  const [image, setImage] = useState(null);
  const [showHovered, setShowHovered] = useState(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [error, setError] = useState('');
  const [open, setOpen] = useState(false);
  const [modalImage, setModalImage] = useState(null);
  const inputRef = useRef(null);

  useEffect(() => {
    if (image) {
      setIsAnimating(true);
      const timeout = setTimeout(() => setIsAnimating(false), 500);
      return () => clearTimeout(timeout);
    }
  }, [image]);

  const handleFile = file => {
    setError('');
    if (!file) return;
    const fileSizeMB = file.size / (1024 * 1024);
    const fileType = file.type;
    if (!ACCEPTED_TYPES.includes(fileType)) {
      showError('Only JPG, JPEG, and PNG files are allowed.');
      return;
    }
    if (fileSizeMB > MAX_FILE_SIZE_MB) {
      showError('Image size must be 10MB or less.');
      return;
    }
    setModalImage(file);
  };

  // handle input change
  const handleInputChange = e => {
    const file = e.target.files?.[0];
    handleFile(file);
    e.target.value = '';
  };

  // handle drop
  const handleDrop = e => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  // handle drag over
  const handleDragOver = e => {
    e.preventDefault();
  };

  // handle click
  const handleClick = () => {
    if (inputRef.current) inputRef.current.click();
  };

  const handleUpload = () => {
    if (modalImage) {
      setImage(modalImage);
      onFileUpload && onFileUpload(modalImage);
      setOpen(false);
      setModalImage(null);
    }
  };

  const removeImage = e => {
    e.stopPropagation();
    setImage(null);
    handelRemoveImage && handelRemoveImage();
    setError('');
  };

  const handelCloseModal = () => {
    setOpen(false);
    setModalImage(null);
  };

  const handleOpenModal = () => {
    if (disabled) return;
    setOpen(true);
    if (image && !modalImage) {
      setModalImage(image);
    }
  };

  const handleDelete = () => {
    setImage(null);
    setModalImage(null);
    handelRemoveImage && handelRemoveImage();
    // setOpen(false);
  };

  return (
    <div tw="flex flex-col gap-2 h-full">
      <div tw="flex flex-col gap-3 items-center">
        <div tw="w-[120px] h-[120px]" css={containerStyle}>
          <div
            tw="flex relative justify-center items-center w-full h-full rounded-full bg-border_stroke"
            css={[
              // !image ? tw`p-8` : tw``,
              iconStyle,
              disabled ? tw`cursor-not-allowed` : tw`cursor-pointer`,
            ]}
            onClick={handleOpenModal}
          >
            {image ? (
              <>
                <img
                  tw="relative z-0 h-full w-full rounded-full object-cover border border-[#BDC4C9] transition-all duration-500"
                  css={[imageStyle]}
                  src={URL.createObjectURL(image)}
                  alt="profile"
                  onMouseEnter={() => {
                    setShowHovered(true);
                  }}
                  onAnimationEnd={() => setIsAnimating(false)}
                />
                <AnimatePresence>
                  {showHovered && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      onMouseEnter={() => setShowHovered(true)}
                      onMouseLeave={() => setShowHovered(false)}
                      tw="bg-[rgba(0,0,0,0.6)]  w-full h-full absolute top-0 left-0 z-10 rounded-full flex items-center justify-center"
                    >
                      <img src={UploadIcon} alt="change image" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </>
            ) : (
              <img
                tw="relative z-0 h-[65%] w-[65%] rounded-full border border-[#BDC4C9] object-fill"
                css={imageStyle}
                src={Profile}
                alt="profile"
              />
            )}
            {image && (
              <div
                onClick={removeImage}
                tw="opacity-0 hover:opacity-100 transition-opacity absolute z-10 -top-1 -right-1 w-5 h-5 text-[0.875rem] rounded-full cursor-pointer bg-error text-white flex justify-center items-center"
                css={cancelIconStyle}
              >
                <span>x</span>
              </div>
            )}
          </div>
        </div>
        {text && (
          <p
            tw="text-[#285FF5] text-[0.8125rem] font-semibold cursor-pointer mt-1"
            onClick={handleOpenModal}
          >
            {text}
          </p>
        )}
      </div>
      {open && (
        <GenericModal
          openModel={open}
          handelCloseMode={handelCloseModal}
          title={
            <ExerciseDetailsModelHeader
              handelCloseClick={handelCloseModal}
              title={image ? 'Edit profile picture' : 'Upload a profile picture'}
              hideArrow={true}
            />
          }
          content={
            <div tw="flex flex-col justify-center items-center py-6">
              <div
                tw="flex flex-col items-center justify-center w-[180px] h-[180px] rounded-full mb-6 relative cursor-pointer bg-white"
                onClick={handleClick}
                css={modalImage ? tw`border-4 border-Primary` : tw`border border-stroke`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                {modalImage ? (
                  <img
                    tw="object-cover w-full h-full rounded-full"
                    src={URL.createObjectURL(modalImage)}
                    alt="preview"
                  />
                ) : (
                  <img src={FileUploaderIcon} alt="upload" tw="mb-2 w-16 h-16" />
                )}
                <input
                  type="file"
                  accept="image/jpeg,image/png,image/jpg"
                  ref={inputRef}
                  style={{ display: 'none' }}
                  onChange={handleInputChange}
                />
                {!modalImage && (
                  <div tw="text-center">
                    <div tw="font-medium text-base text-[#212B36]">Click upload</div>
                    {/* <div tw="text-xs text-[#637381] my-1">OR</div> */}
                    <div tw="flex items-center w-full">
                      <div tw="flex-1 border-t border-text_primary" />
                      <span tw="mx-2 font-bold">OR</span>
                      <div tw="flex-1 border-t border-text_primary" />
                    </div>
                    <div tw="font-normal text-sm text-[#637381]">Drag and drop</div>
                  </div>
                )}
              </div>
            </div>
          }
          containerModalStyle={tw`rounded-card!`}
          PrimaryButtonText={image ? 'Confirm' : 'Upload'}
          clickOnPrimaryButton={handleUpload}
          secondaryButtonText={image ? 'Delete' : 'Cancel'}
          clickOnSecondaryButton={image ? handleDelete : handelCloseModal}
          PrimaryButtonDisable={!modalImage}
          typeOfSecondaryButton={'button'}
          containerButtonsStyle={tw`p-6!`}
          PrimaryButtonStyle={tw`basis-[50%] rounded-[6px] bg-[#D3E57A] border border-Primary_800! text-[#212B36] hover:bg-[#c3d96a]`}
          SecondaryButtonStyle={
            image
              ? tw`basis-[50%] rounded-[6px] bg-error_50 text-error border-error!`
              : tw`basis-[50%] rounded-[6px] bg-white text-[#212B36] border border-[#E5E7EB]`
          }
        />
      )}
      {error && <span tw="text-sm text-red-500">{error}</span>}
    </div>
  );
};

export default ProfilePicture;
