import { css } from 'twin.macro';

export const customStyles = css`
  .ProseMirror ol {
    list-style-type: none;
    counter-reset: item;
    padding-left: 0;
  }

  .ProseMirror ol li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    position: relative;
  }

  .ProseMirror ol li::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #000;
    border-radius: 50%;
    margin-right: 10px;
    margin-top: 8px;
    flex-shrink: 0;
  }
`;
