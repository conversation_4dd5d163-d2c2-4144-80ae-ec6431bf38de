import Checkbox from '@/components/shared/checkbox';
import Input from '@/components/shared/input';
import { CardCvcElement, CardExpiryElement, CardNumberElement } from '@stripe/react-stripe-js';
import CardNumberIcon from '@assets/svgs/payment/card-number.svg';
import tw from 'twin.macro';
import { useState } from 'react';

// Card Payment Form Component
export const CardPaymentForm = ({ register, watch, errors }) => {
  const [cardErrors, setCardErrors] = useState({
    cardNumber: '',
    cardExpiry: '',
    cardCvc: '',
  });

  const handleCardElementChange = elementType => event => {
    setCardErrors(prev => ({
      ...prev,
      [elementType]: event.error ? event.error.message : '',
    }));
  };

  return (
    <div tw="space-y-4">
      <div tw="grid grid-cols-2 gap-4">
        <Input
          register={register}
          label="First Name"
          name="paymentFirstName"
          placeholder="First Name"
          errorMessage={errors.paymentFirstName?.message}
        />
        <Input
          register={register}
          label="Last Name"
          name="paymentLastName"
          placeholder="Last Name"
          errorMessage={errors.paymentLastName?.message}
        />
      </div>

      <div tw="grid grid-cols-3 gap-4">
        <div>
          <p tw="font-[600] text-[0.95rem] font-['Inter'] mb-2">Card Number</p>
          <div tw="flex items-center hover:bg-neutral_50 h-[47px] transition-all duration-300 px-3 gap-3 border-[1px] rounded-[6px] border-stroke">
            <img src={CardNumberIcon} alt="card number" tw="opacity-50" />
            <CardNumberElement
              options={{
                style: {
                  base: { fontSize: '0.9rem' },
                },
              }}
              onChange={handleCardElementChange('cardNumber')}
              tw="bg-none text-[10px] placeholder:text-[10px] placeholder:text-red-400 w-full focus:border-[none] outline-0 py-4"
            />
          </div>
          {cardErrors.cardNumber && <p tw="mt-1 text-sm text-red-500">{cardErrors.cardNumber}</p>}
        </div>
        <div>
          <p tw="font-[600] text-[0.95rem] font-['Inter'] mb-2">Expiry</p>
          <CardExpiryElement
            options={{
              style: {
                base: { fontSize: '0.9rem' },
              },
            }}
            onChange={handleCardElementChange('cardExpiry')}
            tw="hover:bg-neutral_50 h-[46px] py-4 transition-all duration-300 px-3 gap-3 border-[1px] rounded-[6px] border-stroke"
          />
          {cardErrors.cardExpiry && <p tw="mt-1 text-sm text-red-500">{cardErrors.cardExpiry}</p>}
        </div>
        <div>
          <p tw="font-[600] text-[0.95rem] font-['Inter'] mb-2">CVC</p>
          <CardCvcElement
            options={{
              style: {
                base: { fontSize: '0.9rem' },
              },
            }}
            onChange={handleCardElementChange('cardCvc')}
            tw="hover:bg-neutral_50 h-[46px] py-4 transition-all duration-300 px-3 gap-3 border-[1px] rounded-[6px] border-stroke"
          />
          {cardErrors.cardCvc && <p tw="mt-1 text-sm text-red-500">{cardErrors.cardCvc}</p>}
        </div>
      </div>
      <div tw="grid gap-2">
        <Checkbox
          label={
            <span tw="text-Primary_600 text-[0.8125rem] font-medium">
              Use my billing address as card address
            </span>
          }
          register={register}
          name="useMyBling"
          checked={watch('useMyBling')}
          containerStyle={tw`px-0!`}
        />
        <p tw="[line-height: 130%] font-medium text-[0.8125rem]">
          I authorize Rehabitaire to save this payment and automatically charge this payment method
          whenever a subscription is associated with it.
        </p>
      </div>
    </div>
  );
};
