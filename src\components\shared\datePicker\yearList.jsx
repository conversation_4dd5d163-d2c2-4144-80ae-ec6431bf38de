import { styles } from './datePicker';
import YearItem from './yearItem';
import 'twin.macro';
import tw from 'twin.macro';

// Year List Component
const YearsList = ({
  years,
  activeStartDate,
  navigateToYear,
  setShowYearsList,
  selectedDate,
  actionButtons,
  primaryColor = styles.primaryColor,
  borderColor = styles.borderColor,
}) => (
  <div tw="w-full h-full bg-white z-10 overflow-hidden flex flex-col [&::-webkit-scrollbar]:(w-[6px] h-[10px] bg-[#E4E7EB]) [&::-webkit-scrollbar-thumb]:(bg-Primary border-none rounded-[6px])">
    <div tw="flex relative flex-col">
      <div tw="flex sticky top-0 left-0 justify-center items-center px-3 py-3 w-full bg-white  border-b border-b-[#D9DFE4]">
        <span
          tw="w-fit text-center text-[14px] text-white rounded-[6px] px-[12px] font-[700] py-[6px] cursor-pointer"
          css={{ backgroundColor: primaryColor }}
        >
          {selectedDate.getFullYear()}
        </span>
      </div>
      <div
        tw=""
        css={[
          tw`grid grid-cols-3 justify-items-center overflow-y-auto max-h-[220px] [&::-webkit-scrollbar]:(w-[6px] h-[10px] bg-[#E4E7EB]) [&::-webkit-scrollbar-thumb]:(bg-Primary border-none rounded-[6px])`,
          {
            // Remove border-bottom from the last row (last 3 items)
            '& > *:nth-last-child(-n+3)': {
              borderBottom: 'none !important',
            },
          },
        ]}
      >
        {years.map(year => (
          <YearItem
            key={year}
            year={year}
            isSelected={year === activeStartDate.getFullYear()}
            onClick={() => {
              navigateToYear(year);
              setShowYearsList(false);
            }}
            primaryColor={primaryColor}
            borderColor={borderColor}
          />
        ))}
      </div>
      {actionButtons}
    </div>
  </div>
);

export default YearsList;
