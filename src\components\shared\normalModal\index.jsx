import { AnimatePresence, motion } from 'framer-motion';
import { memo, useEffect } from 'react';
// import { IoIosClose } from "react-icons/io";
import tw from 'twin.macro';

const Modal = ({
  content,
  title,
  handleClose,
  open,
  containerModalStyle,
  titleStyle,
  hideIcon = false,
  backgroundModal,
  modalStyle,
  icon,
}) => {
  useEffect(() => {
    // Disable scroll on body when modal is open
    if (open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    // Clean up to enable scroll again when the component is unmounted or modal is closed
    return () => {
      document.body.style.overflow = '';
    };
  }, [open]);
  return (
    <AnimatePresence>
      <motion.div
        onClick={handleClose}
        style={{ background: backgroundModal, ...modalStyle }}
        tw="bg-[rgba(0,0,0,0.4)] w-screen min-h-screen  fixed top-0 items-center left-0 [z-index: 99] flex justify-center flex-col"
        initial={{ opacity: 0 }}
        animate={open ? { opacity: 1 } : { opacity: 0 }}
      >
        <motion.div
          initial={{ opacity: 0 }}
          animate={open ? { opacity: 1 } : { opacity: 0 }}
          onClick={e => {
            e.stopPropagation();
          }}
          tw="bg-white md:rounded-primary rounded-[20px] relative md:min-w-[500px] min-w-[300px] h-[80vh] overflow-y-auto"
          css={containerModalStyle}
          className="element"
        >
          {icon && icon}
          {title && (
            <div
              css={[titleStyle, tw`pb-2 text-lg font-bold sticky top-0 left-0 z-[10] bg-white`]}
              tw=""
            >
              {title}
            </div>
          )}
          {content}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
export default memo(Modal);
