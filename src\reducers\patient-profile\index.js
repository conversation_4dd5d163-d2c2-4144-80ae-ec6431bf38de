export const PatientProfileActionTypes = {
  UPLOAD_IMAGE: 'UPLOAD_IMAGE',
  REMOVE_IMAGE: 'REMOVE_IMAGE',
  TOGGLE_MENU_OPEN: 'TOGGLE_MENU_OPEN',
  TOGGLE_TRANSFER_PATIENT_MODAL: 'TOGGLE_TRANSFER_PATIENT_MODAL',
  TOGGLE_DISCHARGE_PATIENT_MODAL: 'TOGGLE_DISCHARGE_PATIENT_MODAL',
  TOGGLE_DELETE_PATIENT_MODAL: 'TOGGLE_DELETE_PATIENT_MODAL',
};

export const initialState = {
  imageFile: null,
  openMenu: false,
  transferPatientModalOpen: false,
  dischargePatientModalOpen: false,
  deletePatientModalOpen: false,
};

export function reducer(state, action) {
  switch (action.type) {
    case PatientProfileActionTypes.UPLOAD_IMAGE:
      return {
        ...state,
        imageFile: action.payload, // expect a File or data URL
      };

    case PatientProfileActionTypes.REMOVE_IMAGE:
      return {
        ...state,
        imageFile: null,
      };

    case PatientProfileActionTypes.TOGGLE_MENU_OPEN:
      return {
        ...state,
        openMenu: action.payload, //true || false
      };
    case PatientProfileActionTypes.TOGGLE_TRANSFER_PATIENT_MODAL:
      console.log('action:', action);

      return {
        ...state,
        transferPatientModalOpen: action.payload, //true || false,
      };

    case PatientProfileActionTypes.TOGGLE_DISCHARGE_PATIENT_MODAL:
      return {
        ...state,
        dischargePatientModalOpen: action.payload, //true || false,
      };

    case PatientProfileActionTypes.TOGGLE_DELETE_PATIENT_MODAL:
      return {
        ...state,
        deletePatientModalOpen: action.payload, //true || false,
      };

    default:
      return state;
  }
}
