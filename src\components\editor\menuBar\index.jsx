import React, { useRef } from 'react';
import tw from 'twin.macro';
import boldIcon from '@assets/svgs/tiptap-editor/bold-icon.svg';
import italicIcon from '@assets/svgs/tiptap-editor/Italic-icon.svg';
import underlineIcon from '@assets/svgs/tiptap-editor/under-line-icon.svg';
import orderedListIcon from '@assets/svgs/tiptap-editor/polit-points.svg';
import uploadIcon from '@assets/svgs/tiptap-editor/menu-bar-ink-outline.svg';
const MenuBar = ({ editor, handleFileUpload }) => {
  const fileInputRef = useRef(null);

  if (!editor) {
    return null;
  }

  const leftToolbarIconsList = [
    {
      id: 0,
      imageUrl: boldIcon,
      alt: 'bold',
      onClick: () => editor.chain().focus().toggleBold().run(),
      disabled: !editor.can().chain().focus().toggleBold().run(),
      style: tw`w-[10px]`,
    },
    {
      id: 1,
      imageUrl: italicIcon,
      alt: 'italic',
      onClick: () => editor.chain().focus().toggleItalic().run(),
      disabled: !editor.can().chain().focus().toggleItalic().run(),
      style: tw`w-[6px]`,
    },
    {
      id: 2,
      imageUrl: underlineIcon,
      alt: 'underline',
      onClick: () => editor.chain().focus().toggleUnderline().run(),
      disabled: !editor.can().chain().focus().toggleUnderline().run(),
      style: tw`w-[10px]`,
    },
    {
      id: 3,
      imageUrl: orderedListIcon,
      alt: 'orderedList',
      onClick: () => editor.chain().focus().toggleOrderedList().run(),
      disabled: !editor.can().chain().focus().toggleOrderedList().run(),
      style: tw`w-[14px]`,
    },
  ];

  const rightToolbarIconsList = [
    {
      id: 5,
      imageUrl: uploadIcon,
      alt: 'image',
      onClick: () => fileInputRef.current.click(),
      disabled: false,
    },
  ];

  return (
    <div tw="flex flex-wrap gap-2 justify-between mb-3 p-4 border border-border_stroke rounded-[.625rem]">
      <div tw="flex gap-6 items-center">
        {leftToolbarIconsList.map(icon => (
          <button key={icon.id} onClick={icon.onClick} disabled={icon.disabled}>
            <img src={icon.imageUrl} alt={icon.alt} css={icon.style} />
          </button>
        ))}
      </div>
      <div tw="flex gap-4">
        {rightToolbarIconsList.map(icon => (
          <button key={icon.id} onClick={icon.onClick} disabled={icon.disabled}>
            <img tw="w-[14px]" src={icon.imageUrl} alt={icon.alt} />
          </button>
        ))}
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileUpload}
          accept="
    image/*,
    application/pdf,
    .doc,.docx,
    application/msword,
    application/vnd.openxmlformats-officedocument.wordprocessingml.document,
    .xls,.xlsx,
    application/vnd.ms-excel,
    application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,
    .ppt,.pptx,
    application/vnd.ms-powerpoint,
    application/vnd.openxmlformats-officedocument.presentationml.presentation,
    text/plain
  "
          tw="hidden"
          multiple
        />
      </div>
    </div>
  );
};

export default MenuBar;
