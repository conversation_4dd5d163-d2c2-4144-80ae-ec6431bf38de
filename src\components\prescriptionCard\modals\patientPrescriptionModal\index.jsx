import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import { schemaPrescriptionForm } from '@/components/exerciseLibrary/prescriptionForm/schema';
import {
  mappedExerciseToPrescriptionView,
  mapPrescriptionToExerciseLibraryView,
} from '@/mock/prescriptions-data';
import PrescriptionModal from '@/components/exerciseLibrary/prescriptionModal';

// Example initial state for the prescription form

const PatientPrescriptionModal = ({
  openPrescriptionModal,
  setOpenPrescriptionModal,
  prescription,
}) => {
  const exercises = mappedExerciseToPrescriptionView(prescription);
  const {
    register,
    handleSubmit,
    setValue,
    getValues,
    trigger,
    watch,
    control,
    reset,
    formState: { errors, isValid },
  } = useForm({
    defaultValues: {},
    mode: 'onChange',
    resolver: yupResolver(schemaPrescriptionForm),
  });
  useEffect(() => {
    const mappedPerscsription = mapPrescriptionToExerciseLibraryView(prescription);
    reset({
      frequency_day: mappedPerscsription.frequency_day,
      frequency_week: mappedPerscsription.frequency_week,
      duration: Number(mappedPerscsription.duration),
      prescription_name: mappedPerscsription.prescription_name,
      patients: null,
      startDate: new Date(),
      generalMessage: null,
      exerciseMessages: {},
    });
  }, [prescription, reset]);

  return (
    <PrescriptionModal
      open={openPrescriptionModal}
      handleClose={() => setOpenPrescriptionModal(false)}
      exercises={exercises}
      register={register}
      handleSubmit={handleSubmit}
      control={control}
      watch={watch}
      setValue={setValue}
      getValues={getValues}
      trigger={trigger}
      errors={errors}
      isValid={isValid}
      reset={reset}
    />
  );
};

export default PatientPrescriptionModal;
