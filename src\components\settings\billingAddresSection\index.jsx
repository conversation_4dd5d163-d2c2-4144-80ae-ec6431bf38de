import BlingAddress from '@/components/payment/bilingAddress';
import SectionCards from '@/components/sectionsCard';
import SaveChangesButton from '../saveChangesButton';
import { useForm } from 'react-hook-form';
import tw from 'twin.macro';
import 'twin.macro';

const BillingAddresSection = () => {
  const {
    control,
    register,
    formState: { errors },
  } = useForm();

  return (
    <SectionCards>
      <BlingAddress
        customStyle={tw`border-0! rounded-[10px]`}
        control={control}
        register={register}
        errors={errors}
        isBilling
      />
      <SaveChangesButton />
    </SectionCards>
  );
};

export default BillingAddresSection;
