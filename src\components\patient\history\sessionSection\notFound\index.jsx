import SectionCards from '@/components/sectionsCard';
import 'twin.macro';
import tw, { css } from 'twin.macro';
import notFound from '@assets/svgs/not-found.svg';
const NotFound = ({ style, notFoundImage, headline, subTitle, customStyle }) => {
  return (
    <SectionCards
      style={style}
      customStyle={css([
        tw`w-2/3 flex justify-center items-center h-full overflow-hidden p-3 space-y-4 text-center`,
        customStyle,
      ])}
    >
      <div tw="text-center">
        <img src={notFoundImage || notFound} />
        <p tw=" text-4xl text-text_primary font-semibold mb-4">{headline}</p>
        <p tw=" text-lg   text-text_secondary max-w-[29.5rem] mx-auto">{subTitle}</p>
      </div>
    </SectionCards>
  );
};

export default NotFound;
