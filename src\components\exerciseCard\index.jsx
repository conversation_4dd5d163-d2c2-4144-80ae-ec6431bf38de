import React, { useState } from 'react';
import ParamsSection from '@/components/exerciseCard/paramsSection';
import ColoredSvgViewer from '@/components/exerciseCard/coloredSvgViewer';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { paramsSchema } from '@schemas';
import tw from 'twin.macro';
import isDefaultIcon from '@assets/svgs/exercise-library/template-icon-default.svg';

/**
 * A reusable card component for displaying exercise or template information with image, video, SVG thumbnail, and interactive elements.
 *
 * @param {Object} props
 * @param {string} props.imageUrl - URL of the exercise image to display (used if no videoUrl or intensities)
 * @param {string} [props.videoUrl] - URL of the exercise video to display (fades in on hover if provided)
 * @param {Array} [props.intensities] - If provided, renders a ColoredSvgViewer as the card's thumbnail (used for templates)
 * @param {string} props.tagText - Text to display as a tag on the image/SVG/video
 * @param {JSX.Element[]} [props.actionsList] - Optional list of action buttons to display in the card's top section
 * @param {string} props.title - Title of the exercise or template
 * @param {JSX.Element} [props.favourite] - Optional favourite button to display in the title section
 * @param {string} [props.subTitle] - Optional subtitle to display in the card's bottom section
 * @param {Object} [props.params] - Optional params object with sets and reps to display in the card's bottom section
 * @param {Object} [props.containerStyle] - Optional custom styles for the card container
 * @param {Function} [props.handelCardClick] - container card click
 *
 * @example
 * <ExerciseCard
 *   imageUrl="/exercises/yoga.jpg"
 *   tagText="Beginner"
 *   title="Morning Yoga"
 *   thumbnailIcon={favoriteIcon}
 *   titleIcon={heartIcon}
 *   containerStyle={tw`custom-style`}
 * />
 *
 * @returns {JSX.Element} A styled exercise card component
 *
 * @styling
 * - Container: White background, 350px height
 * - Border: 2px #D9DFE4, rounded corners
 * - Hover state: Secondary color border
 * - Image section: 65% of card height
 * - Title section: 35% of card height
 * - Padding: 12px horizontal, varying vertical
 * - Transitions: Smooth border color change
 * - Image: Full cover of its container
 * - Thumbnail overlay: Absolute positioned at bottom
 *
 * @features
 * - Hover effect with border color change
 * - Image with tag overlay
 * - Customizable thumbnail section
 * - Customizable title section
 * - Responsive image handling
 * - Custom container styling support
 */

const ExerciseCard = ({
  hasShadow,
  hasShadowOnHover,
  imageUrl,
  videoUrl,
  intensities,
  tagText,
  actionsList,
  title,
  favourite,
  subTitle,
  params,
  containerStyle,
  handelCardClick,
  PathIds,
  showActions = true,
  setRemoveId,
  id,
  isDefault,
}) => {
  const [hovered, setHovered] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(paramsSchema),
    defaultValues: {
      setsCount: params?.sets,
      repsCount: params?.reps,
    },
  });

  const onSubmit = data => {
    setRemoveId(id);
  };

  return (
    <div
      onClick={handelCardClick}
      css={[
        tw`
         h-[165px] w-full  bg-white cursor-pointer rounded-card overflow-hidden border-[1px] border-stroke transition-all duration-300 select-none
        `,
        hasShadow && {
          boxShadow:
            '0px 0px 0px 1px rgba(152, 161, 178, 0.10), 0px 15px 35px -5px rgba(17, 24, 38, 0.15), 0px 5px 15px 0px rgba(0, 0, 0, 0.08)',
        },
        hasShadowOnHover && {
          '&:hover': {
            boxShadow:
              '0px 0px 0px 1px rgba(152, 161, 178, 0.10), 0px 15px 35px -5px rgba(17, 24, 38, 0.15), 0px 5px 15px 0px rgba(0, 0, 0, 0.08)',
          },
        },
        containerStyle,
      ]}
    >
      {/* the top section */}
      <div
        css={tw`relative w-full h-[60%]`}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      >
        {intensities ? (
          <div css={tw`flex relative items-center justify-center w-full h-full bg-neutral-100`}>
            {isDefault && <img src={isDefaultIcon} alt="sad" tw="absolute top-2 left-2" />}
            <ColoredSvgViewer svgHeight={80} PATH_IDS={PathIds} />
          </div>
        ) : (
          <>
            {/* Image always rendered, fades out on hover if videoUrl */}
            <img
              src={imageUrl}
              alt={title}
              css={[
                tw`object-cover w-full h-full absolute top-0 left-0 transition-opacity duration-1000`,
                videoUrl && hovered ? tw`opacity-0` : tw`opacity-100`,
              ]}
            />
            {/* Video only rendered if videoUrl, fades in on hover */}
            {videoUrl && (
              <video
                src={videoUrl}
                autoPlay={hovered}
                muted
                key={hovered}
                loop
                preload="none"
                css={[
                  tw`object-cover w-full h-full absolute top-0 left-0 transition-opacity duration-1000`,
                  hovered ? tw`opacity-100` : tw`opacity-0`,
                ]}
              />
            )}
          </>
        )}
        <div
          css={tw`flex justify-between items-center absolute bottom-0 left-0 w-full px-[12px] pb-[8px]`}
        >
          <p
            css={tw`h-[20px] font-bold text-[8px] text-white px-[10px] py-[4px] bg-black rounded-[6px] opacity-50 flex items-center justify-center`}
          >
            {tagText}
          </p>
          {showActions && actionsList && actionsList.length > 0 && (
            <div css={tw`flex gap-1 items-center p-0`}>
              {actionsList.map((action, idx) => (
                <div key={idx} css={tw`h-[22px] w-[22px] flex justify-center items-center`}>
                  {action}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      {/* the bottom section */}
      <div css={tw`relative flex flex-col h-[40%] px-[12px] py-[2px]`}>
        <div css={tw`flex justify-between gap-2 items-start`}>
          <p css={tw`flex-1 font-semibold text-[0.8rem] truncate`}>{title}</p>
          {showActions && favourite && favourite}
        </div>
        {subTitle && <p css={tw`font-normal text-[12px] absolute bottom-[12px]`}>{subTitle}</p>}
        {params && (
          <div css={tw`absolute left-0 bottom-[12px] w-full px-[12px]`}>
            <ParamsSection
              register={register}
              errors={errors}
              readOnly={params.readOnly}
              handleSubmit={handleSubmit}
              onSubmit={onSubmit}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ExerciseCard;
