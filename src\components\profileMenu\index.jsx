import React, { useState, useRef } from 'react';
import <PERSON>uHeader from './menuHeader';
import SwitchButton from './switchButton';
import tw, { css } from 'twin.macro';
import MenuOptions from './menuOptions';
import ContactForm from '@components/contactForm/contactFormModal';
import { useContactFormModal } from '@/zustand/contact-form-modla';
import useClickOutside from '@/hooks/useClickOutside';
import usePortalPosition from '@/hooks/usePortalPosition';
import { createPortal } from 'react-dom';
import ProfileMenuIcon from './profileMenuIcon';

const ProfileMenu = ({
  profileImg = 'https://images.pexels.com/photos/4098274/pexels-photo-4098274.jpeg',
  name = '<PERSON>',
  therapistId = '1',
}) => {
  const [clinics, setClinics] = useState([
    { id: '1', name: 'Clinic Uno', active: false },
    { id: '2', name: 'Clinic Due', active: true },
    { id: '3', name: 'Clinic Tre', active: false },
    { id: '4', name: 'Clinic Quattro', active: false },
    { id: '5', name: 'Clinic Cinque', active: false },
    { id: '6', name: 'Clinic Sei', active: false },
  ]);
  const [open, setOpen] = useState(false);
  const menuRef = useRef(null);
  const iconRef = useRef(null);
  const { openContactForm } = useContactFormModal();

  useClickOutside([menuRef, iconRef], () => setOpen(false));

  // Use the custom hook to calculate portal position
  const { top, right } = usePortalPosition(iconRef);

  const handleIconClick = () => {
    setOpen(prev => !prev);
  };

  const activeClinic = clinics.find(clinic => clinic.active)?.name || 'No Active Clinic';
  const idleClinics = clinics.filter(clinic => !clinic.active);

  return (
    <div css={tw`relative`}>
      <ProfileMenuIcon ref={iconRef} onClick={handleIconClick} open={open} />
      {open &&
        createPortal(
          <div
            ref={menuRef}
            style={{
              position: 'fixed',
              top: top,
              right: right,
              zIndex: 9999,
              width: '17.2vw',
              minWidth: 200,
            }}
            css={[
              tw`rounded-[1rem] overflow-hidden`,
              css`
                box-shadow:
                  0px 0px 0px 1px rgba(152, 161, 178, 0.1),
                  0px 15px 35px -5px rgba(17, 24, 38, 0.15),
                  0px 5px 15px 0px rgba(0, 0, 0, 0.08);
              `,
            ]}
          >
            <MenuHeader profileImg={profileImg} name={name} activeClinic={activeClinic} />
            {idleClinics.length > 0 && (
              <div css={tw`bg-neutral_100 max-h-[10rem] overflow-y-auto`} className="element">
                {idleClinics.map(clinic => (
                  <SwitchButton
                    key={clinic.id}
                    clinicName={clinic.name}
                    clinicId={clinic.id}
                    setClinics={setClinics}
                    setOpen={setOpen}
                  />
                ))}
              </div>
            )}

            <MenuOptions therapistId={therapistId} setOpen={setOpen} />
            {openContactForm && <ContactForm userEmail="<EMAIL>" />}
          </div>,
          document.body
        )}
    </div>
  );
};

export default ProfileMenu;
