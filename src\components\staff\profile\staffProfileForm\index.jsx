import { useForm } from 'react-hook-form';
import 'twin.macro';
import tw from 'twin.macro';
import { initialTherapistData } from './module';
import { therapistValidationSchema } from './schema';
import { yupResolver } from '@hookform/resolvers/yup';

import SectionCards from '@/components/sectionsCard';
import StaffInformation from './staffInformation';
import StaffClinicInformation from './staffClinicInformation';
import PrimaryButton from '@/components/shared/primaryButton';
import SectionContainer from '@/components/patient/SectionContainer';
import AccountAactiveData from '@/components/accountActiveData';
import { fieldsThPermistions } from '../../constant';
const TherapistProfileForm = ({ therapist }) => {
  const defaultValues = initialTherapistData({
    ...therapist,
    role:
      therapist.Role === 'Therapist'
        ? { value: 'Therapist', label: 'Therapist' }
        : { value: 'Admin', label: 'Admin' },
  });

  const {
    register,
    control,
    getValues,
    setValue,
    formState: { errors, isValid, isDirty, touchedFields },
    trigger,
    handleSubmit,
  } = useForm({
    defaultValues,
    resolver: yupResolver(therapistValidationSchema),
    mode: 'onChange',
  });

  const formData = getValues();

  const onSubmit = obj => {
    console.log('Submit Object=> ', obj, isValid);
  };

  return (
    <SectionCards customStyle={tw`w-[55%] h-auto`}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <StaffInformation
          {...{
            staffData: therapist,
            formData,
            setValue,
            register,
            trigger,
            errors,
            fieldsThPermistions,
          }}
        />

        <SectionContainer customeClass={tw`!py-8`}>
          {/* Joined Date and Last Activity */}
          <AccountAactiveData
            joinedDate={therapist['Date joined']}
            lastActivity="10min ago"
            rootElementClasses={tw`flex-col-reverse`}
          />
        </SectionContainer>
        <StaffClinicInformation
          {...{
            control,
            register,
            errors,
            touchedFields,
            formData,
            fieldsThPermistions,
          }}
        />

        {/* SUBMIT BUTTON */}

        <div tw="flex justify-end px-6 py-5">
          <PrimaryButton
            text="Save Changes"
            type="submit"
            disable={!isValid || (formData.imageFile === defaultValues.imageFile && !isDirty)}
            customStyle={tw` !py-2 px-3 !rounded-md !font-medium`}
          />
        </div>
      </form>
    </SectionCards>
  );
};

export default TherapistProfileForm;
