import { useMemo, useState, useRef } from 'react';
import 'twin.macro';

import NotFound from './notFound';
import ExercisesList from './ExercisesList';
import MainExercise from './MainExercise';
import SessionHeader from './SessionHeader';
import SectionCards from '@/components/sectionsCard';
import tw from 'twin.macro';
const SessionSection = ({ prescription, parentHeight, patientData }) => {
  // All hooks must be called unconditionally
  const [currnetSessionIndex, setCurrentSessionIndex] = useState(0);
  const [currnetExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [openVideo, setOpenVideo] = useState(false);
  const [showControls, setShowControls] = useState(false);
  const videoRef = useRef();
  // Always use fallback values for hooks
  const safePrescription = prescription && typeof prescription === 'object' ? prescription : {};
  const sessions = Array.isArray(safePrescription.sessions) ? safePrescription.sessions : [];
  const exercises = Array.isArray(safePrescription.exercises) ? safePrescription.exercises : [];
  const name = safePrescription.name || '';

  const isNextDisabled = currnetSessionIndex === sessions.length - 1;
  const isPrevDisabled = currnetSessionIndex === 0;

  const sessionData = useMemo(
    () => sessions[currnetSessionIndex] || {},
    [currnetSessionIndex, sessions]
  );
  const exerciseData = useMemo(
    () => exercises[currnetExerciseIndex] || {},
    [currnetExerciseIndex, exercises]
  );
  const { session_number, session_time, feedback, completion_rate, pain_level, calories } =
    sessionData;
  const { title, set, inputs = [], thumb_url, video_url } = exerciseData;

  const handleOpenVideo = () => {
    videoRef.current.play();
    setOpenVideo(true);
  };
  const handleCloseVideo = () => {
    videoRef.current.pause(); // Stop the videoRef.current
    videoRef.current.currentTime = 0; // Rewind to the beginning
    videoRef.current.load();
    setOpenVideo(false);
  };

  // Show empty state if data is missing
  const isInvalid =
    !prescription ||
    typeof prescription !== 'object' ||
    sessions.length === 0 ||
    exercises.length === 0;

  if (isInvalid) {
    return (
      <NotFound
        style={{ height: parentHeight }}
        headline={'No activity detected'}
        subTitle={`${patientData.Name} didn’t have any assigned prescriptions for this date, or hasn’t
          completed any sessions.`}
      />
    );
  }

  const handleNext = () => {
    setCurrentSessionIndex(prev => prev + 1);
  };
  const handlePrev = () => {
    setCurrentSessionIndex(prev => prev - 1);
  };
  return (
    <SectionCards
      style={{ height: parentHeight }}
      customStyle={tw`w-2/3 overflow-hidden  p-3 text-text_primary space-y-4`}
    >
      {/* seesion data */}
      <SessionHeader
        name={name}
        session_number={session_number}
        sessions={sessions}
        session_time={session_time}
        feedback={feedback}
        completion_rate={completion_rate}
        pain_level={pain_level}
        calories={calories}
        handlePrev={handlePrev}
        handleNext={handleNext}
        isPrevDisabled={isPrevDisabled}
        isNextDisabled={isNextDisabled}
      />
      {/* Exersice */}
      <div tw="flex gap-3 h-[73%]">
        {/* active exercieses  */}
        <MainExercise
          title={title}
          set={set}
          inputs={inputs}
          video_url={video_url}
          thumb_url={thumb_url}
          openVideo={openVideo}
          setOpenVideo={setOpenVideo}
          showControls={showControls}
          setShowControls={setShowControls}
          videoRef={videoRef}
          handleOpenVideo={handleOpenVideo}
        />
        {/*List of exercises */}
        <ExercisesList
          exercises={exercises}
          setCurrentExerciseIndex={setCurrentExerciseIndex}
          setOpenVideo={setOpenVideo}
          currnetExerciseIndex={currnetExerciseIndex}
          handleCloseVideo={handleCloseVideo}
        />
      </div>
    </SectionCards>
  );
};

export default SessionSection;
