import { useState, useRef, useEffect } from 'react';
import SidebarMenuItem from '../sidebarMenuItem';

import tw from 'twin.macro';
import { Link } from 'react-router-dom';

/**
 * Sidebar component for navigation
 * @param {Object} props
 * @param {boolean} props.isCollapsed - Whether the sidebar is currently collapsed
 * @param {Function} props.setIsCollapsed - Function to toggle sidebar collapse state
 * @param {Array} props.sidebarList - List of sidebar menu items with id, title, and icon
 * @param {string} props.CollapseIcon - URL/path to the collapse/expand icon
 * @param {string} props.Logo - URL/path to the logo image
 * @param {ReactNode} props.footerSection - React node for the footer section
 * @param {string} props.isSelectedRoute - The currently selected route
 */
const Sidebar = ({
  isCollapsed,
  setIsCollapsed,
  sidebarList,
  CollapseIcon,
  Logo,
  footerSection,
  isSelectedRoute,
}) => {
  const [hoveredItem, setHoveredItem] = useState(-1);
  const [isSidebarHovered, setIsSidebarHovered] = useState(false);
  const sidebarRef = useRef(null);
  const hoverTimeoutRef = useRef(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 1240);
  const [userCollapsed, setUserCollapsed] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 1240;
      setIsMobile(mobile);
      if (mobile) {
        if (!isCollapsed) {
          setIsCollapsed(true);
        }
      } else {
        if (!userCollapsed && isCollapsed) {
          setIsCollapsed(false);
        }
      }
    };
    window.addEventListener('resize', handleResize);
    // Initial check
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, [isCollapsed, setIsCollapsed, userCollapsed]);

  // Clear any hover timeout when component unmounts
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  // Handle sidebar hover state
  const handleSidebarMouseEnter = () => {
    setIsSidebarHovered(true);
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
  };

  const handleSidebarMouseLeave = () => {
    // Add a small delay before hiding to prevent flickering
    hoverTimeoutRef.current = setTimeout(() => {
      setIsSidebarHovered(false);
      setHoveredItem(-1);
    }, 300);
  };

  // Handle individual item hover
  const handleItemHover = itemId => {
    if (isCollapsed) {
      setHoveredItem(itemId);
    }
  };

  const isEditFilter = localStorage.getItem('isEdit');
  const parseEditFilter = isEditFilter ? JSON.parse(isEditFilter) : false;

  return (
    <div
      ref={sidebarRef}
      tw="h-screen relative bg-[#2E2E2E] transition-all duration-300"
      css={isCollapsed ? tw`w-[5%] flex justify-center py-[2%]` : tw`w-[16%] p-[1.2%]`}
      onMouseEnter={handleSidebarMouseEnter}
      onMouseLeave={handleSidebarMouseLeave}
    >
      {!isMobile && (
        <img
          src={CollapseIcon}
          tw="cursor-pointer absolute z-[1] top-[4%]"
          alt="collapse"
          onClick={() => {
            setIsCollapsed(!isCollapsed);
            setUserCollapsed(!isCollapsed);
          }}
          css={isCollapsed ? tw`-right-[20%] rotate-180` : tw`-right-[6%]`}
        />
      )}
      <div tw="h-[85%]">
        <Link to={'/exercise-library'}>
          <img src={Logo} alt="logo" tw="xl:h-[50px] h-[30px]" />
        </Link>
        <div tw="flex flex-col xl:gap-[1.575em] xl:mt-[40px] mt-[20px]">
          {sidebarList.map(item => {
            const isActive = isSelectedRoute?.toLowerCase() === item.route.toLowerCase();
            const isItemHovered = hoveredItem === item.id;
            return (
              <SidebarMenuItem
                key={item.id}
                item={item}
                isCollapsed={isCollapsed}
                isActive={isActive}
                isSidebarHovered={isSidebarHovered}
                isItemHovered={isItemHovered}
                onHover={handleItemHover}
                isDirty={!!parseEditFilter}
                onAttemptNavigation={path => {
                  // Set pending path and trigger modal
                  localStorage.setItem('pendingPath', path);
                  localStorage.setItem('isEdit', 'false');
                  // Dispatch custom event to trigger modal
                  window.dispatchEvent(
                    new CustomEvent('showNavigationModal', { detail: { path } })
                  );
                }}
              />
            );
          })}
        </div>
      </div>
      {!isCollapsed && <div tw="min-h-[15%] flex items-end overflow-x-auto">{footerSection}</div>}
    </div>
  );
};

export default Sidebar;
