import React, { useRef, useEffect, useState } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  getSortedRowModel,
  getPaginationRowModel,
} from '@tanstack/react-table';
import sortIcon from '@assets/svgs/sort.svg';
import SortAscIcon from '@assets/svgs/asc-sort.svg';
import SortDescIcon from '@assets/svgs/desc-sort.svg';
import NormalTooltipIcon from '@assets/svgs/tooltip.svg';
import DisableTooltipIcon from '@assets/svgs/disable-tooltip-icon.svg';
import PaginationSelect from '@/components/paginationSelect/PaginationSelect';
import PaginationLeftIcon from '@assets/svgs/disable-arrow-select.svg';
import Skeleton from '@/components/shared/skeleton';
import TooltipIcon from '../shared/tooltipIcon';
import useScrollSwipe from '@/hooks/exercises-library/useScrollBySwipe';
import { mergedRefs } from '@/utils/helpers';
import tw from 'twin.macro';

/**
 * A comprehensive table component with sorting, pagination, and customizable features
 *
 * @param {Object} props
 * @param {Array} props.data - The data array to be displayed in the table
 * @param {Array} props.columns - Array of column definitions for the table
 * @param {Array<string>} [props.columnOrder] - Optional array specifying the order of columns.
 * @param {number} props.pageSize - Number of rows to display per page
 * @param {number} props.pageIndex - Current page index (0-based)
 * @param {Function} props.onPageSizeChange - Callback function when page size changes
 * @param {Function} props.onPreviousPage - Callback function to handle previous page navigation
 * @param {Function} props.onNextPage - Callback function to handle next page navigation
 * @param {Array<number>} [props.selectedRows=[]] - Array of selected row IDs
 * @param {Array} props.sorting - Current sorting state
 * @param {Function} props.onSortingChange - Callback function when sorting changes
 * @param {Array<number>} [props.pageSizeOptions=[5,10,20,30,40,50]] - Available page size options
 * @param {JSX.Element} [props.customHeader] - Optional custom header component
 * @param {boolean} [props.hasPagination=true] - Whether to show pagination controls
 * @param {number} props.totalPages - Total number of pages
 * @param {Object} [props.hoverEnabledRef] - Ref to control row hover state
 * @param {Function} [props.onCurrentRowsChange] - Callback with array of current page row IDs
 * @param {boolean} [props.loading] - Whether the table is in a loading state
 *
 * @example
 * <Table
 *   data={tableData}
 *   columns={[
 *     {
 *       header: "Name",
 *       accessorKey: "name",
 *       meta: { hasSortingIcon: true }
 *     }
 *   ]}
 *   pageSize={10}
 *   pageIndex={0}
 *   totalPages={5}
 *   onPageSizeChange={(size) => handlePageSize(size)}
 *   onPreviousPage={() => handlePrevPage()}
 *   onNextPage={() => handleNextPage()}
 * />
 *
 * @returns {JSX.Element} A fully featured table component
 *
 * @styling
 * - Table wrapper: Full width
 * - Header: Light gray background (#EEF0F3)
 * - Header text: Uppercase, 0.91875em size
 * - Row dividers: #D9DFE4 color
 * - Hover state: Light gray background (#F8F9FA)
 * - Selected rows: Light green background (#F7FAEB)
 * - Pagination: Border top, 26px padding
 * - Pagination controls: Flexible layout with gaps
 *
 * @features
 * - Column sorting with icons
 * - Tooltip icons for columns
 * - Row selection highlighting
 * - Pagination with size selection
 * - Custom header support
 * - Responsive column widths
 * - Alternating row colors
 * - Border customization per column
 */

const Table = ({
  data,
  columns,
  columnOrder,
  pageSize,
  pageIndex,
  onPageSizeChange,
  onPreviousPage,
  onNextPage,
  selectedRows = [],
  sorting,
  onSortingChange,
  pageSizeOptions = [5, 10, 20],
  customHeader,
  hasPagination = true,
  totalPages,
  hoverEnabledRef,
  onCurrentRowsChange,
  loading,
}) => {
  const [hoveredHeader, setHoveredHeader] = useState(null);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [visibleWidth, setVisibleWidth] = useState(0);
  const tableBodyRef = useRef(null);
  const { scrollRef } = useScrollSwipe();
  const mergedRef = mergedRefs(tableBodyRef, scrollRef);

  useEffect(() => {
    if (tableBodyRef.current) {
      setVisibleWidth(tableBodyRef.current.clientWidth);
    }
    //update on window resize:
    const handleResize = () => {
      if (tableBodyRef.current) {
        setVisibleWidth(tableBodyRef.current.clientWidth);
      }
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Calculate the visible data for current page
  const startIndex = pageIndex * pageSize;
  const endIndex = Math.min(startIndex + pageSize, data.length);
  const currentPageData = data.slice(startIndex, endIndex);

  const table = useReactTable({
    columns,
    data: currentPageData, // Use the sliced data for current page
    getRowId: row => row.id,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    state: {
      sorting,
      pagination: {
        pageSize,
        pageIndex,
      },
      columnOrder,
    },
    onSortingChange,
    manualPagination: true, // Tell the table we're handling pagination manually
    pageCount: totalPages,
  });

  //get the current page rows:
  useEffect(() => {
    const currentPageRowIds = table.getRowModel().rows.map(row => Number(row.id));

    onCurrentRowsChange && onCurrentRowsChange(currentPageRowIds);
  }, [table, pageIndex, pageSize, data, onCurrentRowsChange]);

  return (
    <div
      css={[
        tw`w-full pt-4 rounded-[12px] bg-white border border-stroke`,
        {
          boxShadow: '0px 3px 6.8px -4px rgba(0, 0, 0, 0.1)',
        },
      ]}
    >
      {customHeader && <>{customHeader}</>}
      <div
        className="custom-scroll bg-white max-h-[580px] overflow-x-auto overflow-y-scroll"
        ref={mergedRef}
        id="table-parent"
        onMouseDown={e => {
          e.preventDefault();
        }}
      >
        <table tw="min-w-max border-separate border-spacing-0">
          {/* Table Header */}
          <thead tw="">
            {table.getHeaderGroups().map(headerGroup => {
              return (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <th
                      key={header.id}
                      css={[
                        tw`bg-neutral_200 tracking-wider text-[14px] py-[10px] px-[24px] text-start font-normal text-text_primary  sticky top-0 z-10`,
                        {
                          width: `${header.column.getSize()}px`,
                          borderRightColor: header.column.columnDef.meta?.borderColor || '#bcc1c4',
                          zIndex: hoveredHeader === header.id ? 20 : 10,
                        },
                        // If column is sticky horizontally, add left and higher z-index
                        header.column.columnDef.meta?.sticky && {
                          left: header.column.columnDef.meta?.left,
                          zIndex: 30, // higher than normal header
                        },
                        header.column.columnDef.meta?.hasBorder && tw`border-r-[1px]`,
                      ]}
                      onMouseEnter={() => setHoveredHeader(header.id)}
                      onMouseLeave={() => setHoveredHeader(null)}
                    >
                      <div tw="flex gap-[6px] items-center">
                        <div
                          css={[
                            tw`flex gap-[6px] items-center`,
                            !header.column.columnDef.meta?.hasTooltipIcon && {
                              paddingRight: '24px',
                            },
                          ]}
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(header.column.columnDef.header, header.getContext())}
                          {header.column.columnDef.meta?.Tooltip && (
                            <TooltipIcon
                              src={
                                hoveredHeader === header.id ? NormalTooltipIcon : DisableTooltipIcon
                              }
                              text={header.column.columnDef.meta?.Tooltip}
                            />
                          )}
                        </div>

                        {header.column.columnDef.meta?.Sorting && (
                          <div css={tw`flex items-center justify-center w-[20px] h-[20px]`}>
                            <img
                              src={
                                header.column.getIsSorted() === 'asc'
                                  ? SortAscIcon
                                  : header.column.getIsSorted() === 'desc'
                                    ? SortDescIcon
                                    : sortIcon
                              }
                              alt="sort"
                              width={8}
                              tw="cursor-pointer"
                              onClick={() => header.column.toggleSorting()}
                            />
                          </div>
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              );
            })}
          </thead>
          {/* Table Body */}
          <tbody>
            {data.length === 0 ? (
              <tr>
                <td
                  css={[
                    tw`sticky text-center py-20`,
                    {
                      left: `${visibleWidth / 2}px`,
                      transform: 'translateX(-50%)',
                    },
                  ]}
                >
                  No data available
                </td>
              </tr>
            ) : (
              table.getRowModel().rows.map((row, idx) => {
                return (
                  <tr
                    key={row.id}
                    onMouseEnter={() => {
                      if (hoverEnabledRef.current) setHoveredRow(row.id);
                    }}
                    onMouseLeave={() => {
                      if (hoverEnabledRef.current) setHoveredRow(null);
                    }}
                  >
                    {row.getVisibleCells().map(cell => {
                      // Determine background class for the cell
                      let bgClass;
                      if (hoveredRow === row.id && !loading) {
                        bgClass = 'bg-Primary_100'; // hover color
                      } else if (selectedRows.includes(Number(row.id))) {
                        bgClass = 'bg-Primary_50';
                      } else if (idx % 2 !== 0) {
                        bgClass = 'bg-neutral_50';
                      } else {
                        bgClass = 'bg-white';
                      }

                      return (
                        <td
                          key={cell.id}
                          className={`whitespace-nowrap text-[14px] py-[8px] px-[24px] ${bgClass}`}
                          css={[
                            // Sticky positioning if specified in meta
                            cell.column.columnDef.meta?.sticky && {
                              position: 'sticky',
                              left: cell.column.columnDef.meta?.left,
                              zIndex: 20,
                            },
                            cell.column.columnDef.meta?.hasBorder && tw`border-r-[1px]`,
                          ]}
                          style={{
                            width: `${cell.column.getSize()}px`,
                            borderRightColor: cell.column.columnDef.meta?.borderColor || '#bcc1c4',
                          }}
                        >
                          {
                            // Render Skeleton if loading, otherwise "-" if value is nullish or empty string, otherwise render cell
                            (() => {
                              if (loading) {
                                return (
                                  <Skeleton
                                    skeletonStyle={{
                                      width: '100%',
                                      height: '20px',
                                      borderRadius: 4,
                                    }}
                                  />
                                );
                              }
                              const value = cell.getValue();
                              if (
                                value == null ||
                                (typeof value === 'string' && value.trim() === '')
                              ) {
                                return '-';
                              }
                              return flexRender(cell.column.columnDef.cell, cell.getContext());
                            })()
                          }
                        </td>
                      );
                    })}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
      {/* Pagination */}
      {hasPagination && (
        <div tw="flex justify-between items-center border-t border-stroke px-[26px] py-[10px]">
          <span tw="text-[0.91875em]">
            {!loading && (data.length === 0 ? 0 : pageIndex * pageSize + 1)} -{' '}
            {!loading && Math.min((pageIndex + 1) * pageSize, data.length)} of{' '}
            {loading ? '-' : data.length}
          </span>
          <div tw="flex gap-2 items-center h-fit">
            <div tw="flex gap-2 items-center h-fit">
              <PaginationSelect
                pageSizes={pageSizeOptions}
                onPageSizeChange={onPageSizeChange}
                currentPageSize={pageSize}
              />
            </div>
            <div tw="flex gap-4 items-center h-[32px]">
              <div
                tw="flex justify-center items-center h-full w-[32px] cursor-pointer px-[6px] py-[6px] border border-stroke rounded-[6px] bg-neutral_100"
                css={[
                  (pageIndex === 0 || totalPages === 0 || loading) &&
                    tw`opacity-50 cursor-not-allowed`,
                ]}
                onClick={!loading && pageIndex > 0 ? onPreviousPage : undefined}
              >
                <img src={PaginationLeftIcon} id="prev page" alt="pagination-left-icon" />
              </div>
              <p tw="text-[0.91875em]">
                {loading ? '-' : totalPages === 0 ? 0 : pageIndex + 1} /{' '}
                <span tw="text-text_secondary">{loading ? '-' : totalPages}</span>
              </p>
              <div
                tw="flex justify-center items-center rotate-180 h-full w-[32px] bg-neutral_100 cursor-pointer px-[6px] py-[6px] border border-stroke rounded-[6px]"
                css={[
                  (pageIndex === totalPages - 1 || totalPages === 0 || loading) &&
                    tw`opacity-50 cursor-not-allowed`,
                ]}
                onClick={!loading && pageIndex < totalPages - 1 ? onNextPage : undefined}
              >
                <img src={PaginationLeftIcon} id="next page" alt="pagination-left-icon" />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Table;
