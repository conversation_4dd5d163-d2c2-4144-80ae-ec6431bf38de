import tw from 'twin.macro';

/**
 * A reusable component for displaying date and frequency related information in a horizontal list
 *
 * @param {Object} props
 * @param {Array} [props.customData] - Optional custom data array to override default data structure
 * @param {string} props.startDate - The start date to display
 * @param {string} props.endDate - The end date to display
 * @param {string} props.frequency - The frequency value to display
 * @param {string} props.duration - The duration value to display
 * @param {string} props.eta - The ETA value to display
 * @param {JSX.Element} [props.customList] - Optional custom list component to override default rendering
 * @param {Object} [props.otherData] - Optional additional data to be included in the list
 *
 * @example
 * <FrequencyAndDate
 *   startDate="2024-03-20"
 *   endDate="2024-04-20"
 *   frequency="Weekly"
 *   duration="1 Month"
 *   eta="2 Days"
 *   otherData={{ id: 5, title: "Custom Field", value: "Custom Value" }}
 * />
 *
 * @returns {JSX.Element} A horizontal list displaying date and frequency information
 *
 * @styling
 * - Background color: #F8F9FA
 * - Border: Default border with rounded corners (8px)
 * - Padding: 10px vertical
 * - List items: Centered with 24px horizontal padding
 * - Text: 0.9em size, semi-bold (600) for titles
 * - Dividers: Vertical lines between items
 */

const FrequencyAndDate = ({
  customData,
  start_date,
  end_date,
  frequency,
  duration,
  eta,
  customList,
}) => {
  const data = [
    { id: 0, title: 'Start', value: start_date },
    { id: 1, title: 'End', value: end_date },
    {
      id: 2,
      title: 'Frequency',
      value: (
        <div tw="flex gap-1 items-center">
          <span>{frequency.per_day}x</span>
          <span tw="font-normal text-text_secondary">a day</span>
          <span>{'.'}</span>
          <span>{frequency.per_week}x</span>
          <span tw="font-normal text-text_secondary">a Week</span>
        </div>
      ),
    },
    {
      id: 3,
      title: 'Duration',
      value: (
        <div tw="flex gap-1 items-center">
          <span>{duration}</span>
          <span tw="font-normal text-text_secondary">Weeks</span>
        </div>
      ),
    },
    {
      id: 4,
      title: 'ETA',
      value: (
        <div tw="flex gap-1 items-center">
          <span>{eta.number}</span>
          <span tw="font-normal text-text_secondary">{eta.unit}</span>
        </div>
      ),
    },
  ];
  return customList ? (
    customList
  ) : (
    <ul tw="border rounded-lg flex justify-between bg-[#F8F9FA] py-2.5 mb-6">
      {(customData ? customData : data).map((item, index) => (
        <li
          key={item.id}
          tw="flex justify-center [flex:1 1 auto]  items-center px-5 gap-2"
          css={[
            index === 0 ? tw`border-l-0` : tw`border-l-[1px]`,
            tw`min-w-0`, // Prevents flex items from overflowing
          ]}
        >
          <span tw="text-sm font-medium">{item.title}</span>
          <span tw="text-sm font-medium">{item.value}</span>
        </li>
      ))}
    </ul>
  );
};

export default FrequencyAndDate;
