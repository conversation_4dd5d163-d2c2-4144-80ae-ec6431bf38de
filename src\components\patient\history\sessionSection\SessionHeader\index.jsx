import LeftArrow from '@assets/svgs/left-arrow.svg';
import CompletionRate from '@assets/svgs/patient/completion-rate.svg';
import PainRate from '@assets/svgs/patient/pain-rate.svg';
import Calories from '@assets/svgs/patient/calories.svg';
import TooltipIcon from '@/components/shared/tooltipIcon';
import tw from 'twin.macro';
import { useState } from 'react';
import OutSideClickHandler from '@/components/outSideClickHandler';
import ActionLabel from '@/components/actionLabel';

const SessionHeader = ({
  name,
  session_number,
  sessions,
  session_time,
  feedback,
  completion_rate,
  pain_level,
  calories,
  handlePrev,
  handleNext,
  isPrevDisabled,
  isNextDisabled,
}) => {
  const [showFeedback, setShowFeedback] = useState(false);
  return (
    <div tw="p-5 space-y-4 h-[25%] rounded-md border border-border_stroke">
      <h2 tw="text-lg font-semibold">{name}</h2>

      <div tw="flex gap-4 items-center">
        <div tw="flex gap-4 items-center">
          <div
            onClick={handlePrev}
            tw="border rounded-[.25rem] border-text_secondary w-7 h-7 cursor-pointer flex justify-center items-center"
            css={[isPrevDisabled && tw`cursor-not-allowed bg-disable pointer-events-none`]}
          >
            <img src={LeftArrow} alt="arrow" />
          </div>
          <div tw="text-base font-medium">
            <div tw="flex gap-1 justify-center items-center">
              Session {session_number}/{sessions.length}
            </div>
          </div>
          <div
            onClick={handleNext}
            css={[isNextDisabled && tw`cursor-not-allowed bg-disable pointer-events-none`]}
            tw="border rounded-[.25rem] border-text_secondary w-7 h-7 rotate-180 cursor-pointer flex justify-center items-center"
          >
            <img src={LeftArrow} alt="arrow" />
          </div>
        </div>
        <p tw="font-medium text-text_secondary">{session_time}</p>
        <OutSideClickHandler onClickOutside={() => setShowFeedback(false)}>
          {feedback && (
            <div tw="relative">
              <ActionLabel onClick={() => setShowFeedback(o => !o)}>
                Read patient feedback
              </ActionLabel>

              {showFeedback && (
                <div
                  style={{
                    boxShadow:
                      '0rem 0rem 0rem 0.0625rem rgba(152, 161, 178, 0.10), 0rem 0.9375rem 2.1875rem -0.3125rem rgba(17, 24, 38, 0.15), 0rem 0.3125rem 0.9375rem 0rem rgba(0, 0, 0, 0.08)',
                  }}
                  tw="w-[22.5rem] absolute left-[50%] translate-x-[-50%] rounded-md py-2.5 px-3.5 text-sm text-text_primary bg-neutral_50 border border-border_stroke h-[8rem] top-7"
                >
                  <div tw="h-[calc(100% - .625rem)] overflow-y-auto">{feedback}</div>
                </div>
              )}
            </div>
          )}
        </OutSideClickHandler>
      </div>

      <div tw="flex">
        <div tw="flex-1 space-y-1.5 py-2.5 px-5">
          <div tw="flex gap-1 items-center text-sm font-medium text-text_secondary">
            <img src={CompletionRate} />
            <p>Completion Rate</p>
            <TooltipIcon
              text={'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante el'}
              isActiveOnHover={true}
            />
          </div>
          <div tw="text-2xl font-semibold">{completion_rate}</div>
        </div>
        <div tw="flex-1 space-y-1.5 py-2.5 px-5 border-l border-stroke">
          <div tw="flex gap-1 items-center text-sm font-medium text-text_secondary">
            <img src={PainRate} />
            <p>Pain Level</p>
          </div>
          <div tw="text-2xl font-semibold">
            {pain_level}
            {pain_level !== 'N/A' && '/10'}
          </div>
        </div>
        <div tw="flex-1 space-y-1.5 py-2.5 px-5 border-l border-stroke">
          <div tw="flex gap-1 items-center text-sm font-medium text-text_secondary">
            <img src={Calories} />
            <p>Calories</p>
            <TooltipIcon
              text={'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante el'}
              isActiveOnHover={true}
            />
          </div>
          <div tw="flex gap-1 items-center text-2xl font-semibold">
            <p>{calories}</p>
            {calories !== 'N/A' && <p tw="text-sm font-normal"> kcal</p>}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionHeader;
