import { Color } from '@tiptap/extension-color';
import ListItem from '@tiptap/extension-list-item';
import TextStyle from '@tiptap/extension-text-style';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import Underline from '@tiptap/extension-underline';
import Image from '@tiptap/extension-image';
import <PERSON>Handler from '@tiptap/extension-file-handler'
import { Node, mergeAttributes,Mark } from '@tiptap/core';
const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('file', file);
  const res = await fetch('http://localhost:3000/upload', {
    method: 'POST',
    body: formData,
  });
  if (!res.ok) throw new Error('Upload failed')

  const data = await res.json()
  return data.url; // e.g. "/uploads/my-file.pdf"
};
export const DownloadableLink = Mark.create({
  name: 'downloadableLink',

  addAttributes() {
    return {
      href: {
        default: null,
      },
      download: {
        default: null,
      },
      class: {
        default: 'text-blue-500 underline hover:no-underline cursor-pointer',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'a[download]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['a', mergeAttributes(HTMLAttributes), 0]
  },

  addCommands() {
    return {
      setDownloadableLink:
        (attrs) =>
        ({ commands }) => {
          return commands.setMark(this.name, attrs)
        },
    }
  },
})


const ImageGallery = Node.create({
  name: 'imageGallery',
  group: 'block',
  content: 'image*', // can contain zero or more image nodes

  parseHTML() {
    return [{ tag: 'div.image-gallery' }];
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { class: 'image-gallery' }), 0];
  },
});

export const extensions = [
  Link.configure({
  openOnClick: true,
  HTMLAttributes: {
    class: 'text-blue-500 underline hover:no-underline cursor-pointer',
    target: '_blank', // opens in new tab
    rel: 'noopener noreferrer', // safe opening
  },
}),
  DownloadableLink,
  Color.configure({ types: [TextStyle.name, ListItem.name] }),
  TextStyle.configure({ types: [ListItem.name] }),
  Underline,
  ImageGallery,
  Image.configure({
    HTMLAttributes: {
      class: 'w-[153px] h-[104px] rounded-[6px]',
    },
    allowBase64: true,
    inline: true,
  }),
  Link.configure({
    openOnClick: true,

    HTMLAttributes: {
      class: 'text-blue-500 underline hover:no-underline cursor-context-menu',
    },

    inclusive: false, // avoids link overrun
  }),
  StarterKit.configure({
    document: true,
    bulletList: {
      keepMarks: true,
      keepAttributes: false,
    },
    orderedList: {
      keepMarks: true,
      keepAttributes: false,
    },
  }),

   FileHandler.configure({
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp','application/pdf'],

onDrop: async (editor, files, pos) => {
  for (const file of files) {
    if (file.type !== 'application/pdf') continue;

    try {
      const uploadedUrl = await uploadFile(file); // e.g., /uploads/file.pdf

      editor
        .chain()
        .insertContentAt(pos, [
          {
            type: 'text',
            text: '📄 ' + file.name,
            marks: [
              {
                type: 'link',
                attrs: {
                  href: `http://localhost:3000/${uploadedUrl}`, //uploadedUrl
                  target: '_blank',
                  rel: 'noopener noreferrer',
                },
              },
            ],
          },
          { type: 'paragraph' },
        ])
        .focus()
        .run();
    } catch (err) {
      console.error('Failed to upload:', err)
    }
  }
},

onPaste: (currentEditor, files, htmlContent) => {
  files.forEach(file => {
    if (htmlContent || !file.type.includes('pdf')) return false;

    const reader = new FileReader();
    reader.readAsDataURL(file);

    reader.onload = () => {
      const fileURL = reader.result;

      currentEditor
        .chain()
        .insertContentAt(currentEditor.state.selection.anchor, [
          {
            type: 'text',
            text: '📄 ' + file.name,
            marks: [
              {
                type: 'link',
                attrs: {
                  href: fileURL,
                  target: '_blank',
                  rel: 'noopener noreferrer',
                },
              },
            ],
          },
          { type: 'paragraph' },
        ])
        .focus()
        .run();
    };
  });
},
      }),
];

// Back-end code to test ur work
// server.js
// const path = require('path');
// const cors = require('cors');
// const multer = require('multer');
// const express = require('express');
// const app = express();

// app.use(cors());
// app.use(express.static(path.join(__dirname, 'public')));

// // Setup file storage
// const storage = multer.diskStorage({
//   destination: path.join(__dirname, 'public/uploads/'),
//   filename: (req, file, cb) => {
//     cb(null, Date.now() + '-' + file.originalname);
//   },
// });
// const upload = multer({ storage });

// // Upload route
// app.post('/upload', upload.single('file'), (req, res) => {
//   const filePath = `/uploads/${req.file.filename}`; // correct URL
//   res.json({ url: filePath }); // return path for accessing via browser
// });

// // Start server
// app.listen(3000, () => console.log('Server running on http://localhost:3000'));
