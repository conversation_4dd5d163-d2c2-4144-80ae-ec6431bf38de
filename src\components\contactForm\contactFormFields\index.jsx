import emailIcon from '@assets/icons/contact-form/email-icon.png';
import { useTranslation } from 'react-i18next';
import TextArea from '@/components/shared/textarea';
import BlackButton from '@/components/shared/blackButton';
import Input from '@/components/shared/input';
import tw from 'twin.macro';

const ContactFormFields = ({
  handleSubmit,
  register,
  isAuth,
  trigger,
  errors,
  inputRef,
  errorMessage,
  isSubmitting,
  emailReadOnly,
}) => {
  const { t } = useTranslation();

  return (
    <form onSubmit={handleSubmit} tw="lg:w-[70%] w-full md:justify-start justify-center grid gap-4">
      <Input
        name="email"
        register={register}
        disabled={isAuth}
        placeholder="Email address"
        onBlur={() => trigger('email')}
        containerSTyle={tw`px-4 bg-white border-border_stroke`}
        firstIcon={<img src={emailIcon} tw="w-[20px]" />}
        errorMessage={errors?.email?.message}
        inputRef={inputRef}
        readOnly={emailReadOnly}
      />
      <p tw="md:text-[14px] text-[12px] text-text_primary">
        *Email address associated with your account
      </p>
      <p tw="md:text-[16px] text-[14px] text-text_primary font-bold">Description</p>
      <TextArea
        register={register}
        placeholder="Your message"
        name="description"
        onBlur={() => trigger('description')}
        containerSTyle={tw`rounded-xl p-0 `}
        textAreaStye={tw`px-5 py-2 focus:rounded-xl`}
        errorMessage={errors?.description?.message}
      />
      <p tw="md:text-[14px] text-[12px] text-text_primary">
        Please enter the details of your request. A member of our support staff will respond as soon
        as possible.
      </p>
      {errorMessage && <p tw="md:text-[14px] text-[12px] text-error">{errorMessage}</p>}
      <div tw="flex justify-center mt-2 md:justify-end">
        <BlackButton isSubmitting={isSubmitting} type="submit" text={t('submit')} />
      </div>
    </form>
  );
};

export default ContactFormFields;
