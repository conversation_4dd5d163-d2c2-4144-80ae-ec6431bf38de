import {
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DndContext,
} from '@dnd-kit/core';
import { restrictToParentElement } from '@dnd-kit/modifiers';
import {
  arrayMove,
  horizontalListSortingStrategy,
  SortableContext,
  sortableKeyboardCoordinates,
} from '@dnd-kit/sortable';
import { useEffect, useRef } from 'react';
import SortableTargetItem from '../sortableItem';
import DroppableTarget from '../prescriptionHorizontalList';
import PrescriptionForm from '../prescriptionForm';
import PrescriptionFooter from '../prescriptionFooter';
import Collapse from '@/components/shared/collapse';
import { exerciseLibraryActions } from '@/reducers/exercise-library';
import tw from 'twin.macro';
import PrescriptionTab from '@/components/prescritopnTab';
import { useExerciseLibrary } from '@/zustand/exercise-library';

const PrescriptionCard = ({
  setSelectedCard,
  exercises,
  templates,
  duplicateCounter,
  selectedCard,
  handleDelete,
  width80,
  register,
  setValue,
  setFirstTemplateId,
  getCurrentTabIndex,
  state,
  dispatch,
  errors,
  touchedFields,
  watch,
  handleSettings,
  handleOpenPrescriptionModal,
  setRemoveId,
  resetPrescriptionForm,
}) => {
  const { setEditTemplateData, addEditTemplateData, EditTemplateData } = useExerciseLibrary();

  // Use useRef to store the current active tab value
  const activeTabRef = useRef(state.activePrescriptionTab);

  // Monitor changes to state.activePrescriptionTab
  useEffect(() => {
    activeTabRef.current = state.activePrescriptionTab;
  }, [state.activePrescriptionTab]);

  // Alternative approach: Get activePrescriptionTab directly from state
  const getActivePrescriptionTab = () => {
    // Use the ref value which is always up-to-date
    return state.activePrescriptionTab;
  };

  const handelClearAll = () => {
    const currentActiveTab = activeTabRef?.current;
    if (currentActiveTab === 0) {
      setSelectedCard([]);
      dispatch({
        type: exerciseLibraryActions.resetTemplatesIds,
      });
    } else {
      setEditTemplateData([]);
    }
  };

  const handelToggle = () => {
    dispatch({ type: exerciseLibraryActions.openCollapseModel, payload: !state.openCollapseModel });
  };

  const handleDrop = item => {
    if (item.type === 'source') {
      // Check if this is a template drop (templateId property exists)
      if (item.templateId) {
        // Find the template by id in templates
        const template = templates.find(t => t.id === item.templateId);
        if (template && template.exercises) {
          const now = Date.now();
          const currentActiveTab = activeTabRef?.current;
          const currentData = currentActiveTab === 0 ? selectedCard : EditTemplateData || [];

          const hasTemplate = currentData.some(card =>
            template.exercises.some(
              ex => card.id === `target-id-${ex.id}` || card.id.startsWith(`target-id-${ex.id}-`)
            )
          );

          const newExercises = template.exercises.map(exercise => ({
            ...exercise,
            id: hasTemplate
              ? `target-id-${exercise.id}-${duplicateCounter.current}`
              : `target-id-${exercise.id}`,
            isDuplicate: hasTemplate,
            createdAt: hasTemplate ? now + Math.random() : undefined,
          }));

          if (hasTemplate) {
            duplicateCounter.current += 1;
          }

          // Use the currentActiveTab variable that was already declared above
          if (currentActiveTab && currentActiveTab !== 0) {
            // Fix: Pass the array directly to addEditTemplateData
            addEditTemplateData(newExercises);
          } else {
            setSelectedCard(prev => [...prev, ...newExercises]);
          }

          if (template && state.firstTemplateIds && dispatch) {
            if (!state.firstTemplateIds.includes(template.id)) {
              dispatch({
                type: exerciseLibraryActions.setFirstTemplateIds,
                payload: [...state.firstTemplateIds, template.id],
              });
            }
          }

          if (currentData.length === 0) {
            setValue('frequency_day', template.frequency.day);
            setValue('frequency_week', template.frequency.week);
            setValue('duration', template.duration);
            setFirstTemplateId(template.id);
          }
        }
      } else {
        // Handle single exercise drop (existing logic)
        const sourceItem = exercises.find(card => card.id === item.id);
        if (sourceItem) {
          const currentActiveTab = activeTabRef?.current;

          if (currentActiveTab === 0) {
            setSelectedCard(prev => {
              // Check if the item already exists in selectedCard
              const existingItem = prev.find(card => card.id === `target-id-${sourceItem.id}`);

              if (existingItem) {
                // If item exists, add it as a duplicate
                const newItem = {
                  ...sourceItem,
                  id: `target-id-${sourceItem.id}-${duplicateCounter.current}`,
                  isDuplicate: true,
                };
                duplicateCounter.current = duplicateCounter.current + 1;
                return [...prev, newItem];
              } else {
                // If item doesn't exist, add it as a new item
                return [
                  ...prev,
                  {
                    ...sourceItem,
                    id: `target-id-${sourceItem.id}`,
                    isDuplicate: false,
                  },
                ];
              }
            });
          } else {
            // Fix: Use the current EditTemplateData and add the new exercise properly
            const currentData = EditTemplateData || [];
            // Check if the item already exists in EditTemplateData
            const existingItem = currentData.find(card => card.id === `target-id-${sourceItem.id}`);

            if (existingItem) {
              // If item exists, add it as a duplicate
              const newItem = {
                ...sourceItem,
                id: `target-id-${sourceItem.id}-${duplicateCounter.current}`,
                isDuplicate: true,
              };
              duplicateCounter.current = duplicateCounter.current + 1;
              addEditTemplateData([newItem]);
            } else {
              // If item doesn't exist, add it as a new item
              const newItem = {
                ...sourceItem,
                id: `target-id-${sourceItem.id}`,
                isDuplicate: false,
              };
              addEditTemplateData([newItem]);
            }
          }
        }
      }
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = event => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const currentActiveTab = activeTabRef?.current;

      if (currentActiveTab === 0) {
        setSelectedCard(items => {
          const oldIndex = items.findIndex(item => item.id === active.id);
          const newIndex = items.findIndex(item => item.id === over.id);

          return arrayMove(items, oldIndex, newIndex);
        });
      } else {
        // Fix: Use the current EditTemplateData and update it properly
        const currentData = EditTemplateData || [];
        const oldIndex = currentData.findIndex(item => item.id === active.id);
        const newIndex = currentData.findIndex(item => item.id === over.id);

        const newData = arrayMove(currentData, oldIndex, newIndex);
        setEditTemplateData(newData);
      }
    }
  };

  return (
    <Collapse
      open={state.openCollapseModel}
      handelToggleCollapse={handelToggle}
      header={
        state.prescriptionTabArray.length === 0 ? (
          <div tw="">Prescription</div>
        ) : (
          <PrescriptionTab items={state.prescriptionTabArray} dispatch={dispatch} state={state} />
        )
      }
      arrowIconCustomStyle={tw`self-center mr-2`}
      headerCustomStyle={
        state.prescriptionTabArray.length > 0 &&
        tw`bg-neutral_50 border border-border_stroke items-stretch gap-0`
      }
      body={
        <div tw="flex gap-4 w-full mt-[10px]">
          <div
            style={{ maxWidth: width80 }}
            tw="bg-neutral_50 w-full flex flex-col px-[18px] py-[14px] border rounded-card border-border_stroke"
          >
            <DroppableTarget
              onDrop={handleDrop}
              showPlaceholderText
              getCurrentTabIndex={getCurrentTabIndex}
            >
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
                modifiers={[restrictToParentElement]}
              >
                <SortableContext
                  items={(getActivePrescriptionTab() === 0
                    ? selectedCard
                    : EditTemplateData || []
                  )?.map(item => item.id)}
                  strategy={horizontalListSortingStrategy}
                >
                  {(getActivePrescriptionTab() === 0 ? selectedCard : EditTemplateData || []).map(
                    item => (
                      <SortableTargetItem
                        key={item.id}
                        id={item.id}
                        item={item}
                        handleSettings={handleSettings}
                        handleDelete={handleDelete}
                        setRemoveId={setRemoveId}
                      />
                    )
                  )}
                </SortableContext>
              </DndContext>
            </DroppableTarget>
            <PrescriptionFooter
              disable={
                (getActivePrescriptionTab() === 0 ? selectedCard : EditTemplateData || [])
                  .length === 0
              }
              handelClearAll={handelClearAll}
              text={'Estimated Time: 12 min'}
            />
          </div>
          <PrescriptionForm
            register={register}
            selectedCard={getActivePrescriptionTab() === 0 ? selectedCard : EditTemplateData || []}
            options={[
              {
                id: 0,
                text: 'Save as Template',
                onClick: () => {
                  dispatch({ type: exerciseLibraryActions.openSaveTemplateModal, payload: true });
                },
              },
            ]}
            dispatch={dispatch}
            state={state}
            setSelectedCard={setSelectedCard}
            errors={errors}
            touchedFields={touchedFields}
            watch={watch}
            resetPrescriptionForm={resetPrescriptionForm}
            handleOpenPrescriptionModal={handleOpenPrescriptionModal}
          />
        </div>
      }
    />
  );
};

export default PrescriptionCard;
