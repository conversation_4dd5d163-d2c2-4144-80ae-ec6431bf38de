import DurationInput from '../durationInput';
import EditTemplateContainerSections from '../editTemplateContainerSections';
import PrescriptionFrequencyInputs from '../prescriptionFrequeancyInputs';
import tw from 'twin.macro';

const EditTemplateModalDuration = ({ register }) => {
  return (
    <EditTemplateContainerSections customStyle={tw`flex gap-2 items-start`}>
      <div tw="basis-[50%] h-full">
        <PrescriptionFrequencyInputs register={register} labelStyle={tw`text-[1rem] mb-0!`} />
      </div>
      <div tw="basis-[50%] border-s border-[#D9D9D9] ps-2">
        <DurationInput
          register={register}
          name={'duration_template'}
          placeholder={'week'}
          label={'Duration'}
          inputStye={tw`py-80`}
          labelStyle={tw`text-[1rem]`}
        />
      </div>
    </EditTemplateContainerSections>
  );
};

export default EditTemplateModalDuration;
