import Menu from '@/components/menu';
import { PatientProfileActionTypes } from '@/reducers/patient-profile';
import 'twin.macro';
import PatientModals from './modals';
import { useState } from 'react';
const PatientActionsMenu = ({ state, dispatch, patientId, patientName }) => {
  const [openModal, setOpenModal] = useState(null); // 'transfer' | 'discharge' | 'delete' | null

  const handleOpen = type => {
    setOpenModal(type);
  };
  const handleCloseModal = () => {
    setOpenModal(null);
  };

  return (
    <>
      <div tw="relative w-12 h-10">
        <Menu
          width="w-full"
          options={[
            {
              id: 1,
              text: 'Transfer patient',
              onClick: () => handleOpen('transfer'),
            },
            {
              id: 2,
              text: 'Discharge patient',
              onClick: () => handleOpen('discharge'),
            },
            {
              id: 3,
              text: 'Delete patient',
              onClick: () => handleOpen('delete'),
            },
          ]}
          handelClose={() =>
            dispatch({ type: PatientProfileActionTypes.TOGGLE_MENU_OPEN, payload: false })
          }
          disable={false}
          openMenu={state.openMenu}
          handleOpenMenu={() =>
            dispatch({ type: PatientProfileActionTypes.TOGGLE_MENU_OPEN, payload: true })
          }
        />
      </div>
      {openModal && (
        <PatientModals
          {...{ openModal, handleCloseModal, patient: { patient_id: patientId, patientName } }}
        />
      )}
    </>
  );
};

export default PatientActionsMenu;
