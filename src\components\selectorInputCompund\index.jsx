import React from 'react';
import GenericSelect from '../shared/select';
import Input from '../shared/input';
import tw from 'twin.macro';
import LabelWithTooltip from '../labelWithTooltip';

const SelectorInputCompund = ({ labelProps, selectorProps, inputProps, errors, touchedFields }) => {
  return (
    <div css={tw`flex-1 flex flex-col gap-1`}>
      {/* label */}
      <LabelWithTooltip {...labelProps} />

      <div css={tw`flex`}>
        {/* generic select*/}
        <div css={tw`basis-[40%]`}>
          <GenericSelect
            {...selectorProps}
            errorMessage={
              errors[selectorProps.name] && touchedFields[selectorProps.name]
                ? errors[selectorProps.name].message
                : ''
            }
          />
        </div>
        <div css={tw`basis-[60%]`}>
          {/* input field */}
          <Input
            {...inputProps}
            containerSTyle={tw`rounded-s-none h-[100%]`}
            inputStye={tw`h-full rounded-[6px] focus:outline-none focus:ring-2 focus:ring-Primary !rounded-s-none`}
            hideErrorMessage={true}
            errorMessage={
              errors[inputProps.name] && touchedFields[inputProps.name]
                ? errors[inputProps.name].message
                : ''
            }
          />
        </div>
      </div>
      {errors[inputProps.name] && touchedFields[inputProps.name] && (
        <span css={tw`text-error text-sm`}>{errors[inputProps.name].message}</span>
      )}
    </div>
  );
};

export default SelectorInputCompund;
