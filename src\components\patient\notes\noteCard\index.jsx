import { useState } from 'react';
import DeleteModal from '../../../actionModal';
import ToolTip from '@/components/shared/toolTip';
import attechmentsIcon from '@/assets/svgs/patient/link-outline.svg';

import 'twin.macro';
import { extractPlainText } from '../utils';
import ActionModal from '../../../actionModal';
import Delete from '@/components/shared/icons/delete';

const NoteCard = ({ id, published_date, author, images, documents, content, handleDeleteNote }) => {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  return (
    <>
      {isDeleteModalOpen && (
        <ActionModal
          open={isDeleteModalOpen}
          title={'Delete Note'}
          handleClose={() => setIsDeleteModalOpen(false)}
          primaryActionHandler={() => {
            handleDeleteNote(id);
            setIsDeleteModalOpen(false);
          }}
          description="Are you sure you want to delete this note? This action cannot be undone."
        />
      )}

      <div tw="p-5 rounded-[.625rem] border border-stroke bg-neutral-50">
        {/* header */}
        <div tw="flex justify-between items-start mb-5">
          {/* data adwn author  */}
          <div tw=" space-y-1.5">
            <p tw="text-black font-semibold text-sm">{published_date}</p>
            <p tw="text-black/50  text-sm">{author}</p>
          </div>
          {/* icons  */}
          <div tw="flex gap-1.5" onClick={e => e.stopPropagation()}>
            {(images.length > 0 || documents.length > 0) && (
              <div tw="relative cursor-pointer" className="group">
                <ToolTip text="Includes Attechemnet" />
                <img src={attechmentsIcon} />
              </div>
            )}
            <div
              tw="relative w-5 h-5 cursor-pointer"
              className="group"
              onClick={() => setIsDeleteModalOpen(true)}
            >
              <Delete color={'fill-text_tertiary group-hover:fill-error'} />
            </div>
          </div>
        </div>

        <div
          tw="h-[6.25rem] overflow-hidden text-ellipsis text-black text-sm leading-normal line-clamp-5"
          style={{
            display: 'box',
            lineClamp: 5,
            boxOrient: 'vertical',
          }}
        >
          {extractPlainText(content)}
        </div>
      </div>
    </>
  );
};
export default NoteCard;
