import SourceItem from '../sourceExerciseLibraryItem';
import useScrollSwipe from '@/hooks/exercises-library/useScrollBySwipe';
import 'twin.macro';

const ExerciseDetailsModelListOfExercise = ({
  exerciseList,
  onRelatedExerciseClick,
  handleDelete,
  handleFavouriteClick,
  selectedCard,
  setSelectedCard,
  handleDuplicate,
}) => {
  const { scrollRef } = useScrollSwipe();

  return (
    <div tw="">
      <p tw="text-[1rem] my-[20px] font-medium">Frequently assigned with</p>
      <ul ref={scrollRef} className="element" tw="flex overflow-x-auto gap-4">
        {exerciseList.map(item => (
          <div key={item.id} tw="w-[155px] flex-shrink-0">
            <SourceItem
              item={item}
              handleDelete={handleDelete}
              handleDuplicate={handleDuplicate}
              handleFavouriteClick={handleFavouriteClick}
              selectedCard={selectedCard}
              setSelectedCard={setSelectedCard}
              canDrag={false}
              handelCardClick={() => onRelatedExerciseClick && onRelatedExerciseClick(item)}
            />
          </div>
        ))}
      </ul>
    </div>
  );
};

export default ExerciseDetailsModelListOfExercise;
