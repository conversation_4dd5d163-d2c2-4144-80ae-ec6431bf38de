import React from 'react';
import LoveIcon from '@assets/svgs/love-icon.svg';
import LoveIconFilled from '@assets/svgs/love-icon-filled.svg';
import tw from 'twin.macro';

const Favourite = ({ checked, onClick }) => (
  <button
    type="button"
    css={tw`relative  w-[18px] h-[18px] p-0 border-none bg-none flex items-center justify-center transition-transform duration-100 hover:scale-105`}
    onClick={onClick}
    aria-pressed={checked}
  >
    <img
      src={LoveIconFilled}
      alt="Remove from favourites"
      css={[
        tw`absolute top-0 left-0  w-[18px] h-[18px] transition-opacity duration-300`,
        checked ? tw`opacity-100` : tw`opacity-0`,
      ]}
      draggable={false}
    />
    <img
      src={LoveIcon}
      alt="Add to favourites"
      css={[
        tw`absolute top-0 left-0  w-[18px] h-[18px] transition-opacity duration-300`,
        checked ? tw`opacity-0` : tw`opacity-100`,
      ]}
      draggable={false}
    />
  </button>
);

export default Favourite;
