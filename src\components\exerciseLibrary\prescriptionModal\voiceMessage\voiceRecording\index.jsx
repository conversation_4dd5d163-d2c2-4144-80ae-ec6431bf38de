import { useState, useRef } from 'react';
import Recording from '@/components/recording';
import React from 'react';
import { ModalViews } from '../..';
import Actions from '../actions';

const VoiceRecording = ({ setModalView, field, setValue }) => {
  const [audioUrl, setAudioUrl] = useState('');
  const audioDurationRef = useRef(0);

  // Determine if the Save button should be disabled
  const isDisabled = audioUrl === '';

  // Handle Save button click
  const handleSave = () => {
    if (audioUrl && audioDurationRef.current) {
      const voiceObject = {
        audioUrl,
        duration: audioDurationRef.current,
      };
      setValue(field, voiceObject); // Update the form state
      setModalView(ModalViews.PRE_SESSION_MESSAGES); // Navigate back to the previous view
    }
  };

  return (
    <div>
      <Recording
        audioUrl={audioUrl}
        setAudioUrl={setAudioUrl}
        audioDurationRef={audioDurationRef}
      />
      {/* actions */}
      <Actions
        isPrimaryDisabled={isDisabled}
        handlClickPrimary={handleSave}
        primaryText="Save"
        setModalView={setModalView}
      />
    </div>
  );
};

export default VoiceRecording;
