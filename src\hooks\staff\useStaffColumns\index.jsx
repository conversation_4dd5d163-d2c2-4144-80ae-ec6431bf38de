import { useMemo } from 'react';
import NameCell from '@/components/table/cells/nameCell';

import NameHeader from '@/components/table/headers/nameHeader';
import { formatDate } from '@/utils/helpers';
import { TableActionTypes } from '@/reducers/table';
import TherapistActions from '@/components/table/cells/nameCell/staffActions';

export function useStaffColumns({ state, dispatch, hoverEnabledRef, setStaffRemoveId }) {
  const columnSize = 300;
  return useMemo(
    () => [
      {
        accessorKey: 'Name',
        header: () => (
          <NameHeader
            currentRows={state.currentRows}
            selectedRows={state.selectedRows}
            setSelectedRows={rows =>
              dispatch({ type: TableActionTypes.SET_SELECTED_ROWS, payload: rows })
            }
            loading={state.loading}
          />
        ),
        cell: info => (
          <NameCell
            value={info.getValue()}
            rowId={info.row.id}
            selectedRows={state.selectedRows}
            setSelectedRows={rows =>
              dispatch({ type: TableActionTypes.SET_SELECTED_ROWS, payload: rows })
            }
            hoverEnabledRef={hoverEnabledRef}
            baseRoute={'staff'}
          >
            {({ handleMenuClose }) => (
              <TherapistActions
                handleOpen={() => {
                  setStaffRemoveId(info.row.id);
                  handleMenuClose();
                }}
              />
            )}
          </NameCell>
        ),
        size: columnSize,
        meta: {
          hasBorder: true,
          sticky: true,
          left: 0,
        },
      },
      {
        accessorKey: 'Email address',
        header: 'Email',
        size: columnSize,
        meta: { Sorting: true },
      },
      {
        accessorKey: 'Role',
        header: 'Role',
        size: columnSize,
        meta: { Sorting: true },
      },
      {
        accessorKey: 'No of patients',
        header: 'No. of patients',
        size: columnSize,
        meta: {
          Sorting: true,
        },
      },
      {
        accessorKey: 'Date joined',
        header: 'Date joined',
        cell: info => formatDate(info.getValue(), state.dateFormat),
        size: columnSize,
        meta: {
          Sorting: true,
        },
      },
      {
        accessorKey: 'Last activity',
        header: 'Last activity',
        cell: info => formatDate(info.getValue(), state.dateFormat),
        size: columnSize,
        meta: {
          Sorting: true,
        },
      },
    ],
    [
      dispatch,
      hoverEnabledRef,
      setStaffRemoveId,
      state.currentRows,
      state.dateFormat,
      state.loading,
      state.selectedRows,
    ]
  );
}
