import { useSortable } from '@dnd-kit/sortable';
import Exerciseimg3 from '@assets/images/exercise-library/exercise3.png';
import tw from 'twin.macro';
import ExerciseCard from '@/components/exerciseCard';
import { CSS } from '@dnd-kit/utilities';
import Settings from '@/components/exerciseCard/buttons/settings';
import Delete from '@/components/exerciseCard/buttons/delete';

const SortableTargetItem = ({ item, handleDelete, id, handleSettings, setRemoveId }) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    cursor: 'move',
    zIndex: isDragging ? 9999 : 'auto',
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <ExerciseCard
        containerStyle={tw`w-[155px] flex-shrink-0`}
        hasShadowOnHover
        setRemoveId={setRemoveId}
        imageUrl={Exerciseimg3}
        id={item.templateId}
        videoUrl="https://assets.mixkit.co/videos/40881/40881-720.mp4"
        tagText="Exercise"
        title={item?.title}
        actionsList={[
          <Settings
            key="settings"
            handleClick={() => {
              handleSettings(item.id);
            }}
          />,
          <Delete
            key="delete"
            handleClick={() => {
              handleDelete(item.id, false);
            }}
          />,
        ]}
        params={{ sets: item.sets, reps: item.reps }}
      />
    </div>
  );
};

export default SortableTargetItem;
