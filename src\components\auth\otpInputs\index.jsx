import { useEffect, useRef, useState } from 'react';
import tw from 'twin.macro';

const OtpInput = ({ isTrueValue, isFalseValue, setOtpLastValue, iseReset, errorMessage }) => {
  const initialSlide = ['', '', '', '', '', ''];
  const inputRefs = useRef([]);
  const [otpValues, setOtpValues] = useState(initialSlide);

  useEffect(() => {
    if (iseReset) {
      setOtpValues(initialSlide);
      inputRefs.current[0].focus();
    }
  }, [iseReset]);

  const handleChange = (index, event) => {
    const value = event.target.value;

    // Allow only single digit input
    if (/^\d?$/.test(value)) {
      const updatedOtpValues = [...otpValues];
      updatedOtpValues[index] = value;
      setOtpValues(updatedOtpValues);

      if (value.length === 1 && index < 5) {
        // Move focus to the next input if a digit is entered
        inputRefs.current[index + 1].focus();
      }
    } else if (event.nativeEvent.inputType === 'deleteContentBackward') {
      // Handle deletion
      const updatedOtpValues = [...otpValues];
      updatedOtpValues[index] = '';
      setOtpValues(updatedOtpValues);

      if (index > 0) {
        // Move focus to the previous input if content is deleted
        inputRefs.current[index - 1].focus();
      }
    }
  };

  const handlePaste = (index, event) => {
    event.preventDefault(); // Prevent default paste behavior

    const pasteData = (event.clipboardData || window.clipboardData).getData('text');

    // Extract the first 6 characters from the pasted data
    const pasteValues = pasteData.slice(0, 6).split('');

    const updatedOtpValues = [...otpValues];

    // Fill the OTP values based on the pasted data
    pasteValues.forEach((value, i) => {
      if (i < updatedOtpValues.length) {
        updatedOtpValues[i] = value;
      }
    });

    setOtpValues(updatedOtpValues);

    // Move focus to the last input field if pasted data is valid
    if (pasteValues.length > 0 && inputRefs.current[5]) {
      inputRefs.current[5].focus();
    }
  };

  const concatenatedOtp = otpValues.join('');

  useEffect(() => {
    if (concatenatedOtp) {
      setOtpLastValue(concatenatedOtp);
    }
  }, [concatenatedOtp]);

  const getActiveIndex = () => {
    const activeElement = document.activeElement;
    return inputRefs.current.findIndex(ref => ref === activeElement);
  };

  return (
    <div>
      <div tw="grid grid-cols-6 gap-5">
        {new Array(6).fill('').map((_, index) => (
          <input
            key={index}
            className="no-arrows"
            type="text"
            maxLength={1}
            value={otpValues[index]}
            autoFocus={index === 0}
            ref={el => (inputRefs.current[index] = el)}
            onChange={event => handleChange(index, event)}
            onPaste={event => handlePaste(index, event)}
            onKeyDown={event => {
              if (event.key === 'Backspace' && otpValues[index] === '') {
                if (index > 0) {
                  inputRefs.current[index - 1].focus();
                }
              }
            }}
            tw="text-[25px] flex border-stroke justify-center items-center text-center h-[70px] border-[1px] rounded-[16px] outline-none"
            css={[
              getActiveIndex() === index ? tw`border border-Primary_600` : tw``,
              isTrueValue ? tw`border-success` : tw``,
              isFalseValue ? tw`border-error` : tw``,
            ]}
          />
        ))}
      </div>
      <p tw="mt-1 text-sm font-['Inter']" style={{ color: '#D12E3C' }}>
        {errorMessage}
      </p>
    </div>
  );
};

export default OtpInput;
