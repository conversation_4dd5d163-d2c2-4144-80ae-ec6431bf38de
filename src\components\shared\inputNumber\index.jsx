import { useState, useEffect } from 'react';
import ArrowUp from '@assets/svgs/auth/input-arrow.svg';
import LabelWithTooltip from '@/components/labelWithTooltip';
import tw, { css } from 'twin.macro';

const InputNumber = ({
  name,
  register,
  placeholder,
  errorMessage,
  type = 'string',
  firstIcon,
  lsatIcon,
  inputStye,
  containerSTyle,
  onBlur,
  inputRef,
  max = 999,
  defaultValue,
  validation,
  hideErrorMessage = false,
  showArrow = true,
  label,
  tooltipText,
  hasTooltip,
  maxWidth,
  labelStyle,
  errorStyle,
  ...rest
}) => {
  const [value, setValue] = useState(defaultValue);
  const [inputWidth, setInputWidth] = useState('2ch');
  const { onChange, onBlur: registerOnBlur, ref } = register(name, validation);
  const [focus, setFocus] = useState(false);

  useEffect(() => {
    const event = {
      target: {
        name,
        value,
      },
    };
    onChange(event);
  }, [value, onChange, name]);

  useEffect(() => {
    const inputElement = document.querySelector(`input[name="${name}"]`);
    if (inputElement) {
      const handleInputEvent = e => {
        if (e.target.value !== value) {
          setValue(e.target.value);
          // Update width based on input length
          setInputWidth(`${Math.max(2, e.target.value.length)}ch`);
        }
      };
      inputElement.addEventListener('input', handleInputEvent);
      return () => inputElement.removeEventListener('input', handleInputEvent);
    }
  }, [name, value]);

  return (
    <div ref={inputRef} tw="">
      {label && (
        <LabelWithTooltip
          label={label}
          tooltipText={tooltipText}
          hasTooltip={hasTooltip}
          labelStyle={labelStyle}
        />
      )}
      <div
        tw="inline-flex w-full items-center bg-white border rounded-[6px] transition-all duration-300 hover:bg-neutral_50 px-[16px]"
        style={{ background: errorMessage ? '#FFF1F2' : '' }}
        css={[
          css`
            ${containerSTyle} !important
          `,
          focus
            ? tw`border-Primary_600`
            : errorMessage
              ? tw`border-error`
              : tw`border-border_stroke`,
        ]}
        onClick={() => {
          const inputElement = document.querySelector(`input[name="${name}"]`);
          if (inputElement) {
            inputElement.focus();
          }
        }}
      >
        {firstIcon}
        <input
          name={name}
          ref={ref}
          css={[
            tw`bg-none text-center focus:border-[none] outline-0 text-[0.8rem] py-[12px]`,
            inputStye,
          ]}
          onFocus={() => setFocus(true)}
          type={type}
          onBlur={e => {
            registerOnBlur(e);
            if (onBlur) onBlur(e);
            setFocus(false);
          }}
          min={1}
          max={max}
          onChange={e => setValue(e.target.value)}
          value={value}
          style={{
            background: 'none',
            width: inputWidth,
            maxWidth: maxWidth,
            marginRight: 2,
          }}
          {...rest}
        />
        <span tw="text-[0.75rem] font-normal text-text_secondary">
          <span tw="text-black">x</span> {placeholder}
        </span>
        {lsatIcon ? (
          lsatIcon
        ) : type === 'number' && showArrow ? (
          <div tw="flex flex-col h-full gap-2 px-3 py-3 border-l-[1px] border-stroke">
            <img src={ArrowUp} alt="arrow-up" onClick={() => setValue(prev => Number(prev) + 1)} />
            <img
              tw="rotate-180"
              src={ArrowUp}
              alt="arrow-up"
              onClick={() => setValue(prev => Number(prev) - 1)}
            />
          </div>
        ) : (
          <></>
        )}
      </div>
      {!hideErrorMessage && (
        <span tw="mt-1 text-sm font-['Inter']" css={errorStyle} style={{ color: '#D12E3C' }}>
          {errorMessage}
        </span>
      )}
    </div>
  );
};
export default InputNumber;
