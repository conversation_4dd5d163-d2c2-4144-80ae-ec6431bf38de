import { useForm } from 'react-hook-form';
import { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AuthInput from '@/components/auth/authInput';
import PrimaryButton from '@/components/shared/primaryButton';
import PhoneInput from '@/components/shared/phoneInput';
import AuthRecaptchaButton from '@/components/auth/authRecaptchaButton';
import AuthLayout from '@/components/auth/authLayout';
import { createFormErrorHandler } from '@/utils/form-error-handler';
import BackButton from '@/components/shared/backButton';
import 'twin.macro';
import GenericSelect from '@/components/shared/select';
import tw from 'twin.macro';
import PhoneNumber from '@/components/phoneNumber';

const ClinicInfo = () => {
  const navigate = useNavigate();
  const recaptchaRef = useRef(null);
  const [recaptchError, setRecaptchaError] = useState(false);
  const [errorMessage, setErrorMessage] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    control,
    setError,
    formState: { errors, isSubmitting },
    watch,
  } = useForm({
    // defaultValues: initialSignUp,
    // resolver: yupResolver(schemaRequestDemo),
    mode: 'onBlur', // Validate on blur
  });

  const countryOptions = [
    { label: 'Syria', value: 1 },
    { label: 'Turky', value: 2 },
  ];

  const cityOptions = [
    { label: 'aleppo', value: 1 },
    { label: 'damascus', value: 2 },
  ];

  const handleContinue = async data => {
    try {
      setErrorMessage('');
      setRecaptchaError(false);
      const token = await recaptchaRef.current.executeAsync();
      //   const { detail } = await AuthApis.signup(
      //     {
      //       email: data.email,
      //       firstName: data.firstName,
      //       lastName: data.lastName,
      //       phoneNumber: "+" + data.phone_ch + data.phoneNumber,
      //     },
      //     token
      //   );
      recaptchaRef.current.reset();
      // showSuccess(detail);
      localStorage.setItem('from', JSON.stringify('signUp'));
      reset();
      navigate('/otp-page');
    } catch (error) {
      console.log('error', error);
      recaptchaRef.current.reset();
      if (error?.response?.data?.detail) {
        setErrorMessage(error.response.data.detail);
      }
      if (error.response.data.recaptchaToken) {
        setRecaptchaError(true);
      } else {
        // Use the error handler
        const handleError = createFormErrorHandler(setError);
        handleError(error);
      }
    }
  };

  return (
    <AuthLayout
      aboveTitle={<BackButton handelClick={() => navigate('/sign-up', { replace: true })} />}
      authSection={
        <>
          <AuthInput
            label={'Clinic name'}
            name={'clinic_name'}
            placeholder={'Clinic name'}
            register={register}
            errorMessage={errors.clinic_name?.message}
          />
          <div>
            <label tw="font-[600] text-[0.95rem] font-['Inter']">Address</label>
            <div tw="flex gap-6 mt-2 mb-2">
              <div tw="basis-[50%]">
                <GenericSelect
                  control={control}
                  name={'country'}
                  placeholder={'Country'}
                  errorMessage={errors.country?.message}
                  options={countryOptions}
                />
              </div>
              <div tw="basis-[50%]">
                <GenericSelect
                  name={'city'}
                  placeholder={'City'}
                  control={control}
                  errorMessage={errors.city?.message}
                  options={cityOptions}
                />
              </div>
            </div>
            <AuthInput
              name={'address'}
              placeholder={'Address'}
              register={register}
              errorMessage={errors.address?.message}
            />
          </div>
          {/* <div>
            <label tw="font-[600] text-[0.95rem] font-['Inter']">
              Clinic Information (optional)
            </label>
            <div tw="flex gap-6 mt-2 mb-2">
              <AuthInput
                name={'textNumber'}
                placeholder={'Tax number'}
                register={register}
                errorMessage={errors.textNumber?.message}
              />
              <AuthInput
                name={'commercial_register'}
                placeholder={'Commercial register'}
                register={register}
                errorMessage={errors.commercial_register?.message}
              />
            </div>
          </div> */}
          {/* <div>
            <label tw="font-[600] text-[0.95rem] font-['Inter']">Phone Number</label>
            <div tw="flex items-start mt-2 w-full">
              <div tw="w-[19%]">
                <PhoneInput
                  control={control}
                  // defaultCountry={countryCode.toLocaleUpperCase() || "EG"}
                  defaultCountry={'EG'}
                  name={'phone_ch'}
                  errorMessage={errors.phoneNumber?.message}
                />
              </div>
              <div tw="w-[80%]">
                <AuthInput
                  name={'phoneNumber'}
                  placeholder={'Phone number'}
                  register={register}
                  type={'number'}
                  hideErrorMessage
                  showArrow={false}
                  containerSTyle={tw`rounded-s-[0px]  border-s-0`}
                  errorMessage={errors.phoneNumber?.message}
                />
              </div>
            </div>
          </div>
          {errors.phoneNumber?.message && (
            <span tw="pt-2 text-sm font-['Inter']" style={{ color: '#D12E3C' }}>
              {errors.phoneNumber?.message}
            </span>
          )} */}

          <PhoneNumber
            control={control}
            defaultCountry={'EG'}
            label={'Phone number'}
            name={'phoneNumber'}
            phoneName={'phoneCh'}
            placeholder={'Phone number'}
            register={register}
            errors={errors?.phoneNumber?.message}
          />

          <AuthRecaptchaButton
            control={control}
            recaptchError={recaptchError}
            recaptchaRef={recaptchaRef}
          />
          <div tw="pt-[1.5rem]">
            <PrimaryButton
              disable={isSubmitting}
              text={'continue'}
              tw="w-full py-[12px] px-[16px] rounded-[6px] text-[1rem] font-[500] font-['Inter']"
            />
          </div>
          {errorMessage && (
            <span tw="pt-2 text-sm font-['Inter']" style={{ color: '#D12E3C' }}>
              {errorMessage}
            </span>
          )}
        </>
      }
      footerLinkText={'Let us know'}
      footerNormalText={'Having problems creating an account?'}
      hasAuthFooter
      isLogIn
      hasAuthRedirectFooter={false}
      subtitle="Enter your clinic’s name, location and phone number."
      title={'Clinic information'}
      handelSubmit={handleSubmit(handleContinue)}
    />
  );
};

export default ClinicInfo;
