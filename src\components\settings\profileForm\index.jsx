import { useForm } from 'react-hook-form';
import { initialState } from './module';
import SectionCards from '@/components/sectionsCard';
import PersonalInformation from '../personalInformation';
import { showError } from '@/libs/react.toastify';
import LabelSections from '@/components/sectionLabels';
import SaveChangesButton from '../saveChangesButton';
import CLinicForm from '../clinicForm';
import 'twin.macro';

const ProfileForm = () => {
  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({ defaultValues: initialState });

  const onSubmit = async data => {
    try {
      console.log('data', data);
    } catch (error) {
      showError(error);
    }
  };

  return (
    <SectionCards>
      <form tw="relative h-full" onSubmit={handleSubmit(onSubmit)}>
        <PersonalInformation register={register} control={control} />
        <div tw="p-[24px] space-y-[24px]">
          <LabelSections text={'Clinic information'} />
          <CLinicForm register={register} control={control} errors={errors} />
        </div>
        <SaveChangesButton />
      </form>
    </SectionCards>
  );
};

export default ProfileForm;
