import { getWindowDimensions } from '@/utils/helpers';
import { useEffect, useState } from 'react';

// for get window size when change
export const useWindowDimensions = () => {
  const [windowDimensions, setWindowDimensions] = useState(getWindowDimensions());
  useEffect(() => {
    const handleResize = () => {
      setWindowDimensions(getWindowDimensions());
    };

    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    clientWindowHeight: windowDimensions.height,
    clientWindowWidth: windowDimensions.width,
  };
};
