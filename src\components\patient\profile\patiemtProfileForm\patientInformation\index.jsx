import SectionContainer from '@/components/patient/SectionContainer';
import LabelSections from '@/components/sectionLabels';
import PatientActionsMenu from '../patientActions';
import ProfilePicture from '@/components/profilePicture';
import 'twin.macro';
import AccountAactiveData from '@/components/accountActiveData';
import tw from 'twin.macro';
import Input from '@/components/shared/input';
function PatientInformation({
  dispatch,
  patientData,
  formData,
  setValue,
  register,
  trigger,
  errors,
  fieldsThPermistions,
  state,
  id,
}) {
  const inputsData = [
    {
      id: 0,
      name: 'patient_name',
      label: 'Full Name',
      placeholder: 'Patient Name',
      defaultValue: formData.patient_name,
      disabled: !fieldsThPermistions.patient_name,
      onBlur: () => trigger('patient_name'),
      errorMessage: errors?.patient_name?.message,
    },
    {
      id: 1,
      name: 'assigned_pt',
      label: 'Current PT',
      placeholder: 'Current PT',
      defaultValue: formData.assigned_pt,
      disabled: !fieldsThPermistions.current_pt,
      onBlur: () => trigger('assigned_pt'),
      errorMessage: errors?.assigned_pt?.message,
    },
  ];
  return (
    <SectionContainer>
      <div tw="flex justify-between items-center mb-4">
        <LabelSections text={'Personal Information'} />
        <PatientActionsMenu
          dispatch={dispatch}
          patientName={patientData.Name}
          patientId={id}
          state={state}
        />
      </div>

      <div tw="flex gap-6 items-center mb-4">
        {/* Image uploader */}
        <ProfilePicture
          onFileUpload={e => setValue('image_file', e)}
          handelRemoveImage={() => setValue('image_file', null)}
          disabled={true}
          containerStyle={tw`w-[80px] h-[80px]`}
          iconStyle={tw`p-0`}
        />

        <div tw="flex gap-y-3 gap-x-8 w-full">
          {inputsData.map((item, idx) => (
            <div tw="basis-[50%]" key={idx}>
              <Input
                {...item}
                {...{
                  register,
                  disabledStyle: tw`bg-disable cursor-not-allowed hover:bg-disable`,
                  containerSTyle: tw`bg-white border-border_stroke`,
                  labelStyle: tw`text-[0.9rem]`,
                  inputStye: tw`text-[0.8rem]`,
                }}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Joined Date and Last Activity */}
      <AccountAactiveData joinedDate={patientData['Date joined']} lastActivity="10min ago" />
    </SectionContainer>
  );
}

export default PatientInformation;
