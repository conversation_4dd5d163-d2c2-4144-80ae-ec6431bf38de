import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import SectionCards from '@/components/sectionsCard';
import LogsHeader from '../logsHeader';
import DIvider from '@/components/payment/divider';
import 'twin.macro';
import tw from 'twin.macro';
import Timeline from '@/components/timeLine';
import useYesterdayLogsStore from '@/zustand/settings/yesterday-logs-store';
import useOlderLogsStore from '@/zustand/settings/older-logs-store';
import useTodayLogsStore from '@/zustand/settings/today-logs-store';
import TimeLineLoading from '@/components/timeLine/timeLineLoading';
import TimeLineEmpty from '@/components/timeLine/timeLineEmpty';

const Logs = () => {
  const todayHead = useTodayLogsStore(state => state.head);
  const yesterdayHead = useYesterdayLogsStore(state => state.head);
  const olderHead = useOlderLogsStore(state => state.head);
  const [isActive, setIsActive] = useState(false);
  const [emptyLogs, setEmptyLogs] = useState(false);
  const [loading, setLoading] = useState(todayHead === 0 && yesterdayHead === 0 && olderHead === 0);
  const { control, watch } = useForm({ defaultValues: { therapist_id: null } });

  // Get fetch functions from stores
  const fetchTodayLogs = useTodayLogsStore(state => state.fetchLogs);
  const fetchYesterdayLogs = useYesterdayLogsStore(state => state.fetchLogs);
  const fetchOlderLogs = useOlderLogsStore(state => state.fetchLogs);

  // Fetch all logs on mount and set loading to false when done
  useEffect(() => {
    if (todayHead === 0 && yesterdayHead === 0 && olderHead === 0) {
      setLoading(true);
      Promise.all([fetchTodayLogs(), fetchYesterdayLogs(), fetchOlderLogs()]).then(() => {
        setLoading(false);
        if (
          (useTodayLogsStore.getState().items?.length ?? 0) === 0 &&
          (useYesterdayLogsStore.getState().items?.length ?? 0) === 0 &&
          (useOlderLogsStore.getState().items?.length ?? 0) === 0
        ) {
          setEmptyLogs(true);
        }
      });
    }
  }, [fetchTodayLogs, fetchYesterdayLogs, fetchOlderLogs]);

  const handelToggle = () => {
    setIsActive(prev => !prev);
  };

  const therapistOptions = [
    { label: 'All therapists', value: 0 },
    { label: 'therapist1', value: 1 },
    { label: 'therapist2', value: 2 },
    { label: 'therapist3', value: 3 },
    { label: 'therapist3', value: 3 },
    { label: 'therapist3', value: 3 },
    { label: 'therapist3', value: 3 },
    { label: 'therapist3', value: 3 },
    { label: 'therapist3', value: 3 },
    { label: 'therapist3', value: 3 },
    { label: 'therapist3', value: 3 },
    { label: 'therapist3', value: 3 },
    { label: 'therapist3', value: 3 },
    { label: 'therapist3', value: 3 },
  ];

  return (
    <div tw="max-h-full overflow-y-auto grid gap-[24px]" className="element">
      <div tw="sticky top-0 bg-[#F8F9FA] z-[10] pb-2">
        <LogsHeader
          handelToggle={handelToggle}
          isActive={isActive}
          therapistOptions={therapistOptions}
          control={control}
          watch={watch}
        />
      </div>
      {loading ? (
        <div tw="flex flex-col gap-[3rem]">
          {Array(3)
            .fill()
            .map(index => (
              <TimeLineLoading key={index} />
            ))}
        </div>
      ) : (
        <>
          {emptyLogs ? (
            <div tw="w-full h-[65vh]">
              <TimeLineEmpty
                label="No logs available"
                subtitle="It’s a little quiet here. Your logs will pop up as soon as there’s something new."
              />
            </div>
          ) : (
            <>
              <SectionCards customStyle={tw`p-[24px]`}>
                <Timeline
                  label={'Today'}
                  useStore={useTodayLogsStore}
                  emptyLabel="Nothing to see here"
                  emptySubtitle="There are no logs for today. Check back later."
                />
              </SectionCards>
              <DIvider />
              <SectionCards customStyle={tw`p-[24px]`}>
                <Timeline
                  label={'Yesterday'}
                  useStore={useYesterdayLogsStore}
                  emptyLabel="Nothing to see here"
                  emptySubtitle="There were no logs yesterday."
                />
              </SectionCards>
              <DIvider />
              <SectionCards customStyle={tw`p-[24px]`}>
                <Timeline
                  label={'Older'}
                  useStore={useOlderLogsStore}
                  emptyLabel="Nothing to see here"
                  emptySubtitle="There've been no logs lately."
                />
              </SectionCards>
            </>
          )}
        </>
      )}
    </div>
  );
};

export default Logs;
