import AccountCreatedImage from '@/assets/svgs/payment/payment-complete.svg';
import PrimaryButton from '@/components/shared/primaryButton';
import { useNavigate } from 'react-router-dom';
import 'twin.macro';

const PaymentComplete = () => {
  const navigate = useNavigate();
  return (
    <div tw="flex flex-col gap-4 justify-center items-center w-screen h-screen text-center">
      <img src={AccountCreatedImage} alt="Sign-up Complete" />
      <div tw="px-[2%] w-[35%] grid gap-2">
        <h1 tw="font-['Inter'] font-[600] text-[2rem]">Sign-up Complete</h1>
        <div tw="mb-6 text-center">
          <span tw="font-['Inter'] font-[400] text-text_primary [line-height: 130%] text-[1rem]">
            Congratulations, you’re now officially registered on Rehabitaire! Time to revolutionize
            rehab.
          </span>
        </div>
        <PrimaryButton
          tw="w-full py-[12px] px-[16px] rounded-[6px] text-[1rem] font-[600] font-['Inter']"
          text={'Continue'}
          type="button"
          handleClick={() => navigate('/exercise-library', { replace: true })}
        />
      </div>
    </div>
  );
};

export default PaymentComplete;
