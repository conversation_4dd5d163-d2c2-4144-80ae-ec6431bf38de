# Standard Structure

## 1- Each component has Folder Name as component Name, inside this folder I have index.jsx File

## 2- Types of components:

```
A- shared component:A general component located inside **shared folder**, and this component can use in this project and other projects.
components => Shared=> componentNameFolder => index.jsx

B- Specific component: located inside folder holding name of its page.
components => pageName=> componentNameFolder => index.jsx

C-  common component: A component exists in one or two pages not in the whole pages, located inside component folder as following components => componentNameFolder => index.jsx
```

## New Modules: Add inside Folder inside component Folder ("Related to its component")

## New Schemas: should add inside component

## Actions: define inside Reducers

## For set font size depend on width=1440 and height is 1024 you need to calculate this font size by this equation

```
const result= design-size (font size on figma) / 1.04 / 16 (default size)
the result must be just one value after comma
```
