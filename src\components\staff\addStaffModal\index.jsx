import plus from '@assets/svgs/plus.svg';
import PrimaryButton from '@/components/shared/primaryButton';
import tw from 'twin.macro';
import 'twin.macro';
import { useForm } from 'react-hook-form';
import checkCircle from '@/assets/svgs/patient/check-circle.svg';
import { useState } from 'react';
import { InitialState } from './module';
import { schemaAddStaff } from './schema';
import { yupResolver } from '@hookform/resolvers/yup';
import AddStaffView from './addStaffView';
import PaymentView from './paymentView';
import ActionModal from '@/components/actionModal';
import { MOADL_VIEW } from '../constant';
import AddButton from '@/components/addButton';

function AddStaffModal() {
  const [modelView, setModalView] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors, isValid, touchedFields },
    control,
    getValues,
    reset,
  } = useForm({
    defaultValues: InitialState,
    resolver: yupResolver(schemaAddStaff),
    mode: 'onChange',
  });

  const onSubmit = data => {
    // console.log({
    //   data,
    // });

    setModalView(MOADL_VIEW.SUCCESS_MODAL);
  };

  const handleClose = () => {
    setModalView('');
    reset(InitialState);
  };

  return (
    <>
      <AddButton text={'Add Staff'} onClick={() => setModalView(MOADL_VIEW.ADD_STAFF)} />

      {modelView === MOADL_VIEW.ADD_STAFF && (
        <AddStaffView
          {...{
            open: modelView === MOADL_VIEW.ADD_STAFF,
            handleClose,
            handelAction: () => setModalView(MOADL_VIEW.PAYMENT_VIEW),
            register,
            errors,
            touchedFields,
            control,
            isValid,
          }}
        />
      )}
      {modelView === MOADL_VIEW.PAYMENT_VIEW && (
        <PaymentView
          {...{
            open: modelView === MOADL_VIEW.PAYMENT_VIEW,
            handleClose,
            handelAction: handleSubmit(onSubmit),
            register,
            errors,
            touchedFields,
            control,
            isValid,
            paymentInfo: {
              name: `${getValues('firstName')} ${getValues('lastName')}`,
              method: getValues('paymentMethod.title'),
            },
          }}
        />
      )}

      {modelView === MOADL_VIEW.SUCCESS_MODAL && (
        <ActionModal
          open={modelView === MOADL_VIEW.SUCCESS_MODAL}
          handleClose={handleClose}
          customIcon={checkCircle}
          customHeaderStyle={tw`p-3 border-8 mb-2 border-success_50 bg-success_100 rounded-full`}
          title={'Add Staff'}
          description={
            <div tw="max-w-[22rem]">
              <span tw="text-text_primary font-medium">
                {getValues('firstName')} {getValues('lastName')}
              </span>{' '}
              has been invited to
              <span tw="text-text_primary font-medium">Your Clinic.</span>
            </div>
          }
          PrimaryButtonStyle={tw`flex-1 !py-2.5 !rounded-md !font-medium !bg-white hover:!bg-white border-border_stroke text-text_primary`}
          primaryActionHandler={handleClose}
          actionButtonText="Done"
          hasSecondaryButton={false}
        />
      )}
    </>
  );
}

export default AddStaffModal;
