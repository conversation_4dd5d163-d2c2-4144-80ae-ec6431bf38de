import LabelWithTooltip from '@/components/labelWithTooltip';
import 'twin.macro';

const TextArea = ({
  name,
  register,
  placeholder,
  errorMessage,
  firstIcon,
  lsatIcon,
  textAreaStye,
  label,
  containerSTyle,
  labelStyle,
  onBlur,
  rows = 12,
  tooltipText,
  hasTooltip,
  maxLength, // Add maxLength as a prop
  ...rest
}) => {
  return (
    <div>
      {!hasTooltip && (
        <p tw="text-sm font-bold text-text_primary" css={labelStyle}>
          {label}
        </p>
      )}
      {label && hasTooltip && (
        <LabelWithTooltip
          label={label}
          tooltipText={tooltipText}
          hasTooltip={hasTooltip}
          labelStyle={labelStyle}
        />
      )}
      <div
        tw="flex items-center px-6 py-3 border bg-neutral_50 border-border_stroke"
        css={containerSTyle}
      >
        {firstIcon}
        <textarea
          {...rest}
          tw="px-2 bg-none w-full border outline-0 text-sm resize-none focus:border-Primary_600 rounded-[6px]"
          {...register(name)}
          css={textAreaStye}
          placeholder={placeholder}
          style={{ background: 'none' }}
          onBlur={onBlur}
          rows={rows}
          maxLength={maxLength} // Enforce maxLength here
        />
        {lsatIcon}
      </div>
      <p style={{ color: '#f00' }}>{errorMessage}</p>
    </div>
  );
};
export default TextArea;
