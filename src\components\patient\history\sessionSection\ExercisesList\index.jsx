import 'twin.macro';
import tw from 'twin.macro';
const ExercisesList = ({
  exercises,
  currnetExerciseIndex,
  setCurrentExerciseIndex,
  setOpenVideo,
  handleCloseVideo,
}) => {
  return (
    <div
      className="element"
      tw="xl:w-[28%] lg:w-[35%] overflow-y-auto px-4 py-5 space-y-4 max-h-full rounded-md border  border-border_stroke"
    >
      <h4 tw="text-lg font-semibold">List of exercises</h4>

      {exercises.map((item, index) => (
        <div
          key={item.id}
          tw="cursor-pointer"
          onClick={() => {
            handleCloseVideo();
            setCurrentExerciseIndex(index);
          }}
        >
          <div
            tw="rounded-md h-[6.5rem] w-full mb-1.5 transition overflow-hidden"
            css={[index === currnetExerciseIndex && tw`border border-Primary_600`]}
          >
            <img src={item.thumb_url} tw="object-cover aspect-[16/12] w-full h-full" />
          </div>
          <p
            tw="text-base font-medium transition-colors text-text_secondary"
            css={[index === currnetExerciseIndex && tw` text-Primary_600`]}
          >
            {index + 1}. {item.title} {item?.set}
          </p>
        </div>
      ))}
    </div>
  );
};

export default ExercisesList;
