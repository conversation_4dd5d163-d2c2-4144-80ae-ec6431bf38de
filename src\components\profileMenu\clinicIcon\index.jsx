import React from 'react';
import tw from 'twin.macro';

const ClinicIcon = ({ clinicName }) => {
  // Function to extract initials
  const getInitials = clinicName => {
    const words = clinicName.split(' ').filter(Boolean); // Split into words and remove empty strings
    return words
      .slice(0, 2)
      .map(word => word[0])
      .join(''); // Get the first character of the first two words
  };

  const initials = getInitials(clinicName);
  return (
    <div
      css={tw`w-[1.5rem] h-[1.5rem] bg-Primary flex justify-center items-center text-[0.7rem] font-semibold rounded-full`}
    >
      {initials}
    </div>
  );
};

export default ClinicIcon;
