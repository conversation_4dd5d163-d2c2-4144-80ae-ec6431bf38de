import React from 'react';
import MenuList from '@/components/menu/menuList';
import tw from 'twin.macro';
import { useNavigate } from 'react-router-dom';
import { useContactFormModal } from '@/zustand/contact-form-modla';

const MenuOptions = ({ therapistId, setOpen }) => {
  const { changeOpenContactForm } = useContactFormModal();
  const navigate = useNavigate();
  const navigateToProfile = () => {
    setOpen(false); // Close the profile menu
    navigate(`/staff/${therapistId}`); // Navigate to the therapist's profile
  };
  const openContactForm = () => {
    changeOpenContactForm(true); // Open the contact form modal
  };
  const navigateToTerms = () => {
    navigate('/terms'); // Navigate to the terms&privacy page
  };
  const handleSignOut = () => {
    localStorage.removeItem('rehab-email'); // Clear 'rehab-email' from local storage
    navigate('/login'); // Navigate to the login page
  };
  return (
    <MenuList
      listContainerStyle={tw`static border-none rounded-none shadow-none w-full text-left`}
      listItemStyle={tw`py-[0.7rem] px-[0.8rem] font-medium text-[0.7rem] border-stroke`}
      options={[
        {
          id: '1',
          text: 'Profile',
          onClick: navigateToProfile,
        },
        {
          id: '2',
          text: 'Contact Us',
          onClick: openContactForm,
        },
        {
          id: '3',
          text: 'Terms & Privacy',
          onClick: navigateToTerms,
        },
        {
          id: '4',
          text: 'Sign Out',
          onClick: handleSignOut,
        },
      ]}
    />
  );
};

export default MenuOptions;
