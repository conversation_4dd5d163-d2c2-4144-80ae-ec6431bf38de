import 'twin.macro';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '@tailwind';
import GenericSelect from '@/components/shared/select';
import ToggleSwitch from '@/components/shared/toggleSwitch';

const fullConfig = resolveConfig(tailwindConfig);
const { text_primary, Primary_100, Primary_600, neutral_300 } = fullConfig.theme.colors;

const LogsHeader = ({ handelToggle, isActive, therapistOptions, control, watch }) => {
  return (
    <div tw="flex justify-between items-center">
      <GenericSelect
        control={control}
        name={'therapist_id'}
        placeholder={'All therapists'}
        label={''}
        customStyles={{
          placeholder: () => ({
            color: text_primary,
            opacity: 1,
            fontWeight: '500',
          }),
          control: () => ({
            background: watch('therapist_id')?.value ? Primary_100 : '',
            border: `1px solid ${Primary_600} !important`,
          }),
          menuList: base => ({
            ...base,
            maxHeight: '200px',
            overflowY: 'auto',
            width: '200px',
            '::-webkit-scrollbar': {
              width: '6px',
              background: '#E4E7EB',
            },
            '::-webkit-scrollbar-track': {
              background: 'transparent',
            },
            '::-webkit-scrollbar-thumb': {
              background: Primary_600,
              borderRadius: '4px',
            },
          }),
        }}
        options={therapistOptions}
      />

      <div tw="flex gap-2 items-center">
        <p tw="text-text_secondary text-[0.875rem]">Auto refresh</p>
        <ToggleSwitch isActive={isActive} onToggle={handelToggle} />
      </div>
    </div>
  );
};

export default LogsHeader;
