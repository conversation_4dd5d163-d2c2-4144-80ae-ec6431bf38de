import tw from 'twin.macro';

/**
 * Primary button component for form submission
 * @param {Object} props
 * @param {string} props.text - Text to display on the button
 * @param {string} props.otherStyle - Additional CSS styles for the button
 * @param {string} props.type - Type of the button (e.g., "submit", "button")
 * @param {Object} props.rest - Additional props to pass to the button element
 * @param {Boolean} props.disable - Disable props to disable the button
 * @param {Function} props.handelClick -
 */

const SecondaryButton = ({ text, otherStyle, type, handelClick, disable, ...rest }) => {
  return (
    <button
      type={type}
      tw="w-full border-primary rounded-[10px] p-[10px] text-Primary_800 font-[700]"
      css={[
        otherStyle,
        disable
          ? tw`bg-disable border-border_stroke cursor-not-allowed`
          : tw`bg-none  border-Primary_800 border-[1px]`,
      ]}
      onClick={handelClick}
      disabled={disable}
      {...rest}
    >
      {text}
    </button>
  );
};

export default SecondaryButton;
