import { useEffect, useRef, useState } from 'react';
import RecordRTC from 'recordrtc';
import StopRecord from '@assets/svgs/recording/stop-record.svg';
import recordIcon from '@assets/svgs/recording/record-icon.svg';
import Play from '@assets/svgs/recording/play-icon.svg';
import pause from '@assets/svgs/recording/pause-icon.svg';
import { formatTime } from '@/utils/helpers';
import 'twin.macro';
import tw from 'twin.macro';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '@tailwind';
import trash from '@assets/svgs/recording/delete-record.svg';

const fullConfig = resolveConfig(tailwindConfig);
const { Primary_100, Primary_50 } = fullConfig.theme.colors;

const Recording = ({ audioUrl, setAudioUrl, audioDurationRef }) => {
  const mediaRecorderRef = useRef(null);
  const playIntervalRef = useRef(null);
  const audioRef = useRef(null);
  const recordIntervalRef = useRef(null);
  const progressBarRef = useRef(null); // Ref for the progress bar

  const audioContextRef = useRef(null); // Ref for the AudioContext
  const analyserRef = useRef(null); // Ref for the AnalyserNode
  const sourceRef = useRef(null); // Ref for the MediaStreamSource
  const canvasRef = useRef(null); // Ref for the canvas element
  const animationFrameRef = useRef(null); // Ref for requestAnimationFrame
  const isDraggingRef = useRef(false); // Ref to track dragging state

  const [audioFile, setAudioFile] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [permissionsGranted, setPermissionsGranted] = useState(false);
  const [isPaused, setIsPaused] = useState(false); // New state for pause/resume
  const [isPlaying, setIsPlaying] = useState(false); // State to track play/pause
  const [progress, setProgress] = useState(0); // State to track audio progress

  useEffect(() => {
    checkPermissions();
  }, []);

  useEffect(() => {
    const updateProgress = () => {
      if (audioRef.current) {
        const currentTime = audioRef.current.currentTime;
        const duration = audioDurationRef.current || 1; // Avoid division by zero
        setProgress((currentTime / duration) * 100); // Calculate progress as a percentage
      }

      if (isPlaying) {
        animationFrameRef.current = requestAnimationFrame(updateProgress); // Schedule the next frame
      }
    };

    if (isPlaying) {
      animationFrameRef.current = requestAnimationFrame(updateProgress); // Start the animation loop
      return () => cancelAnimationFrame(animationFrameRef.current); // Cleanup on unmount or pause
    }
  }, [isPlaying]);

  useEffect(() => {
    // Check if the browser supports the MediaSession API
    if ('mediaSession' in navigator) {
      // Set a handler for the 'play' action triggered by media keys or external controls
      navigator.mediaSession.setActionHandler('play', () => {
        playAudio(); // Call the playAudio function to start audio playback
      });

      // Set a handler for the 'pause' action triggered by media keys or external controls
      navigator.mediaSession.setActionHandler('pause', () => {
        pauseAudio(); // Call the pauseAudio function to pause audio playback
      });
    }
  }, []);

  // capture the audio duration when metadata is loaded
  useEffect(() => {
    const audio = audioRef.current;
    const onMetadata = () => {
      // workaround for some browsers that do not provide duration immediately
      if (!Number.isFinite(audio.duration)) {
        audio.currentTime = 1e101;
        audio.addEventListener('timeupdate', function handler() {
          audio.removeEventListener('timeupdate', handler);
          audioDurationRef.current = audio.duration; // Store the duration in the ref
          audio.currentTime = 0; // reset
        });
      } else {
        audioDurationRef.current = audio.duration; // Store the duration in the ref
      }
    };

    audio?.addEventListener('loadedmetadata', onMetadata);

    return () => {
      audio?.removeEventListener('loadedmetadata', onMetadata);
    };
  }, [audioUrl]);

  async function requestMediaPermissions() {
    try {
      // Request access to both video and audio
      await navigator.mediaDevices.getUserMedia({ audio: true });
      return true; // Permissions granted
    } catch (err) {
      if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
        return false; // Permissions denied
      } else {
        return false; // Other errors
      }
    }
  }
  async function checkPermissions() {
    const granted = await requestMediaPermissions();
    setPermissionsGranted(granted);
  }

  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording && !isPaused) {
      mediaRecorderRef.current.pauseRecording(); // Pause the recording
      setIsPaused(true); // Update state to indicate recording is paused

      // Stop the record timer
      clearInterval(recordIntervalRef.current);

      // Stop the circular outline drawing
      cancelAnimationFrame(animationFrameRef.current);

      // Clear the canvas
      if (canvasRef.current) {
        const ctx = canvasRef.current.getContext('2d');
        ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height); // Clear the entire canvas
      }
    }
  };

  const resumeRecording = () => {
    if (mediaRecorderRef.current && isRecording && isPaused) {
      mediaRecorderRef.current.resumeRecording(); // Resume the recording
      setIsPaused(false); // Update state to indicate recording is resumed

      // Resume the record timer
      recordIntervalRef.current = setInterval(() => {
        setElapsedTime(prevTime => prevTime + 0.1);
      }, 100);

      // Resume the circular outline drawing
      drawCircularOutline();
    }
  };

  const startRecording = async () => {
    if (!permissionsGranted) {
      handleToggleNoPermissionModal();
    } else {
      setAudioUrl('');
      stopAudio();
      setElapsedTime(0);
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: true,
      });

      let options = { mimeType: 'audio/webm' };
      if (!MediaRecorder.isTypeSupported(options.mimeType)) {
        options = { mimeType: 'audio/ogg' }; // fallback to a different supported MIME type
      }

      {
        /* analyze the audio being recorded for visualisation */
      }
      // Initialize Web Audio API
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      // Smaller size for faster updates but less detail
      analyserRef.current.fftSize = 2048;
      // Create a MediaStreamSource from the audio stream
      sourceRef.current = audioContextRef.current.createMediaStreamSource(stream);
      // Connect the source to the analyser
      sourceRef.current.connect(analyserRef.current);

      mediaRecorderRef.current = new RecordRTC(stream, {
        type: 'audio',
        mimeType: options.mimeType,
      });

      mediaRecorderRef.current.startRecording();
      setIsRecording(true);
      setIsPaused(false); // Ensure recording starts in a non-paused state

      recordIntervalRef.current = setInterval(() => {
        setElapsedTime(prevTime => prevTime + 0.1);
      }, 100); // Update elapsed time every second

      // Start drawing the circular outline visualization
      drawCircularOutline();
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef) {
      mediaRecorderRef?.current?.stopRecording(() => {
        const blob = mediaRecorderRef.current.getBlob();
        const url = URL.createObjectURL(blob);
        setAudioUrl(url);
        setAudioFile(new File([blob], 'recording.mp3', { type: 'audio/mp3' }));
        clearInterval(recordIntervalRef.current); // Stop the interval when recording stops
        setIsRecording(false);
        setElapsedTime(0); // Reset elapsed time to 00:00
        // Stop the animation frame
        cancelAnimationFrame(animationFrameRef.current);
        // Clear the canvas
        if (canvasRef.current) {
          const ctx = canvasRef.current.getContext('2d');
          ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height); // Clear the entire canvas
        }
        // Disconnect Web Audio API nodes
        if (sourceRef.current) {
          sourceRef.current.disconnect();
          sourceRef.current = null; // Reset the sourceRef
        }
      });
    }
  };

  // Draw the circular outline based on volume
  const drawCircularOutline = () => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const baseRadius = 30; // Fixed base radius of the circle

    const draw = () => {
      const dataArray = new Float32Array(analyserRef.current.fftSize);
      analyserRef.current.getFloatTimeDomainData(dataArray);
      // Calculate average volume
      const averageVolume =
        dataArray.reduce((sum, value) => sum + Math.abs(value), 0) / dataArray.length;

      // Map the RMS value to a thickness
      const sensitivityModifier = 500; // Adjust this value for more sensitivity
      const thickness = Math.max(1, Math.min(averageVolume * sensitivityModifier, 50));

      // Clear the canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw the circular outline (fixed base radius, grows outwardly)
      ctx.beginPath();
      ctx.arc(centerX, centerY, baseRadius, 0, 2 * Math.PI);
      ctx.strokeStyle = Primary_100; // Semi-transparent red
      ctx.lineWidth = thickness; // Outline grows outwardly
      ctx.stroke();

      // Draw the semi-transparent background (fixed base radius)
      ctx.beginPath();
      ctx.arc(centerX, centerY, baseRadius, 0, 2 * Math.PI);
      ctx.fillStyle = Primary_50; // Semi-transparent green background
      ctx.fill();

      // Request the next animation frame
      animationFrameRef.current = requestAnimationFrame(draw);
    };

    draw();
  };

  const playAudio = () => {
    if (audioRef.current) {
      // Initialize Web Audio API for playback visualization
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      }

      if (!analyserRef.current) {
        analyserRef.current = audioContextRef.current.createAnalyser();
        analyserRef.current.fftSize = 2048; // Smaller size for faster updates but less detail
      }

      // Create a MediaElementSource from the audio element
      if (!sourceRef.current) {
        sourceRef.current = audioContextRef.current.createMediaElementSource(audioRef.current);
        sourceRef.current.connect(analyserRef.current);
        analyserRef.current.connect(audioContextRef.current.destination);
      }

      // Start the circular outline visualization
      drawCircularOutline();

      // Play the audio
      audioRef.current.play();
      setIsPlaying(true);

      // Start updating elapsed time
      playIntervalRef.current = setInterval(() => {
        setElapsedTime(prevTime => prevTime + 1);
      }, 1000); // Update elapsed time every second
    }
  };

  const pauseAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
      // Stop updating elapsed time
      clearInterval(playIntervalRef.current);
    }
  };

  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause(); // Pause the audio
      audioRef.current.currentTime = 0; // Reset playback position to the beginning
      clearInterval(playIntervalRef.current); // Stop the interval that updates elapsed time
      setIsPlaying(false); // Update state to indicate audio is not playing
      setElapsedTime(0); // Reset elapsed time to 00:00
      setProgress(0); // Reset progress bar to 0%

      // Stop the circular outline visualization
      cancelAnimationFrame(animationFrameRef.current);
    }
  };

  const handleProgressBarClick = e => {
    if (audioRef.current && progressBarRef.current) {
      // Get the bounding rectangle of the progress bar
      const rect = progressBarRef.current.getBoundingClientRect();
      // Calculate the click position as a percentage of the progress bar width
      const clickPosition = Math.min(Math.max((e.clientX - rect.left) / rect.width, 0), 1); // Ensure position is between 0 and 1
      // Update the audio's current time based on the click position
      const duration = audioDurationRef.current || 1;
      audioRef.current.currentTime = clickPosition * duration;
      // Update the progress state immediately
      setProgress(clickPosition * 100);
      // Update elapsed time immediately
      setElapsedTime(audioRef.current.currentTime);
    }
  };

  const handleRetryAudio = () => {
    // Reset audio playback states using stopAudio
    stopAudio();

    // Destroy the audio file and reset related states
    setAudioFile(null); // Remove the audio file to switch back to the recording view
    setAudioUrl(''); // Clear the audio URL
  };

  const handleThumbDrag = e => {
    if (progressBarRef.current && audioRef.current) {
      const rect = progressBarRef.current.getBoundingClientRect();
      const dragPosition = Math.min(Math.max((e.clientX - rect.left) / rect.width, 0), 1); // Ensure position is between 0 and 1
      const duration = audioDurationRef.current || 1;
      const newTime = dragPosition * duration; // Calculate new time based on drag position
      setProgress(dragPosition * 100); // Update progress state
      setElapsedTime(newTime); // Update elapsed time state
      audioRef.current.currentTime = newTime; // Update audio playback position
    }
  };

  const handleMouseDown = e => {
    e.preventDefault(); // Prevent default browser behavior
    if (audioRef.current) {
      isDraggingRef.current = true; // Set dragging state to true

      // If playback is currently playing, pause the audio and clear the interval
      if (isPlaying) {
        audioRef.current.pause(); // Pause the audio
        clearInterval(playIntervalRef.current); // Clear the interval that updates elapsed time
      }

      // Attach mousemove and mouseup listeners to the document
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
  };

  const handleMouseUp = () => {
    isDraggingRef.current = false; // Set dragging state to false
    if (audioRef.current) {
      // If playback was playing before dragging, resume playback and set the interval
      if (isPlaying) {
        audioRef.current.play(); // Resume audio playback
        playIntervalRef.current = setInterval(() => {
          setElapsedTime(prevTime => prevTime + 1); // Update elapsed time every second
        }, 1000);
      }
    }

    // Remove mousemove and mouseup listeners from the document
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = e => {
    if (isDraggingRef.current) {
      handleThumbDrag(e);
    }
  };

  return (
    <div css={tw`px-8 py-[70px]`}>
      <div tw=" relative flex flex-col items-center">
        {/* the top section*/}
        <div css={tw`relative mb-8`}>
          <img
            src={recordIcon}
            alt="recording-icon"
            css={[
              tw`w-[66px] h-[66px] z-20 relative opacity-70 `,
              !isRecording &&
                !audioFile &&
                tw`cursor-pointer hover:opacity-100 transition-opacity duration-300`,
            ]}
            onClick={!isRecording && !audioFile ? startRecording : null}
          />
          <canvas
            ref={canvasRef}
            width={200}
            height={200}
            tw="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          />
        </div>
        {/* divider */}
        <div tw="bg-stroke h-[1px] w-full" />
        {/* Audio playback */}
        {audioFile && (
          <div css={tw`w-[60%] mt-5`}>
            <audio ref={audioRef} src={audioUrl} onEnded={stopAudio} />
            <div tw="w-full p-[3px] bg-white rounded-full shadow-[0px_4px_14px_0px_rgba(0,0,0,0.15)]">
              <div
                ref={progressBarRef}
                tw="w-full h-[10px] rounded  cursor-pointer relative "
                onClick={handleProgressBarClick}
              >
                {/* Progress bar */}
                <div tw="bg-Primary h-full rounded-full" style={{ width: `${progress}%` }} />

                {/* Thumb */}
                <div
                  tw="absolute top-[50%] h-[14px] w-[14px] bg-Primary rounded-full transform -translate-y-1/2 -translate-x-1/2 cursor-pointer"
                  style={{ left: `${progress}%` }}
                  onMouseDown={handleMouseDown} // Start dragging
                />
              </div>
            </div>
          </div>
        )}
        {/* controls */}
        <div css={tw`w-full flex justify-center items-center px-4`}>
          <div css={tw`flex items-center  p-[10px]`}>
            {isRecording && (
              <div css={tw`flex gap-1`}>
                <img
                  onClick={stopRecording}
                  src={StopRecord}
                  alt="Stop Recording"
                  tw="cursor-pointer"
                />
                <img
                  onClick={isPaused ? resumeRecording : pauseRecording}
                  src={isPaused ? Play : pause}
                  alt={isPaused ? 'Resume Recording' : 'Pause Recording'}
                  tw="cursor-pointer"
                />
              </div>
            )}
            {audioFile && (
              <div css={tw` flex gap-1`}>
                <img
                  onClick={handleRetryAudio}
                  src={trash}
                  alt="retry Recording"
                  tw="cursor-pointer"
                />
                <img
                  onClick={isPlaying ? pauseAudio : playAudio}
                  src={isPlaying ? pause : Play}
                  alt={isPlaying ? 'pause audio' : 'resume audio'}
                  tw="cursor-pointer"
                />
              </div>
            )}
            <p tw="font-medium text-[32px] p-[10px]">{formatTime(elapsedTime)}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Recording;
