import { Controller } from 'react-hook-form';
import 'twin.macro';
import tw from 'twin.macro';

const Radio = ({
  control,
  name,
  label,
  value,
  rules,
  customable,
  sectionToRenderAfter,
  customLabelStyle,
  inputId,
}) => {
  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      render={({ field: { onChange, value: fieldValue } }) => {
        const isChecked =
          typeof fieldValue === 'object' ? fieldValue.id === value.id : fieldValue === value;

        return (
          <div tw="flex flex-col gap-4">
            <div tw="flex gap-2 items-center">
              <input
                type="radio"
                id={inputId || name}
                name={name}
                value={value}
                checked={isChecked}
                onChange={() => onChange(value)}
                css={
                  !isChecked
                    ? tw`after:content-[''] after:absolute after:top-1/2 after:left-1/2 after:-translate-x-1/2 after:-translate-y-1/2 after:w-[9px] after:h-[9px] after:bg-border_stroke after:rounded-full after:opacity-0 after:scale-75 after:transition-all after:duration-200 hover:after:opacity-100 hover:after:scale-100`
                    : tw`checked:bg-white checked:border-Primary_600 checked:after:content-[''] checked:after:absolute checked:after:top-1/2 checked:after:left-1/2 checked:after:-translate-x-1/2 checked:after:-translate-y-1/2 checked:after:w-[8px] checked:after:h-[8px] checked:after:bg-Primary_600 checked:after:rounded-full`
                }
                tw={
                  'relative rounded-full transition-all duration-200 appearance-none cursor-pointer w-[20px] h-[20px] border-[2px] border-border_stroke'
                }
              />
              {customable ? (
                customable
              ) : (
                <div tw="text-[1rem] font-medium" css={customLabelStyle}>
                  <label htmlFor={name}>{label}</label>
                </div>
              )}
            </div>
            {isChecked && sectionToRenderAfter}
          </div>
        );
      }}
    />
  );
};

export default Radio;
