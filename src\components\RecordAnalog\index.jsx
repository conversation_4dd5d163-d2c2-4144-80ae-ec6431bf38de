import React, { useEffect, useRef, useState } from "react";

import WaveSurfer from "wavesurfer.js";
import RecordPlugin from "wavesurfer.js/dist/plugins/record.esm.js";
import Timeline from "wavesurfer.js/dist/plugins/timeline.esm.js";

export default function AudioRecorderWaveform() {
  const micRef = useRef(null);
  const recordingsRef = useRef(null);
  const [wavesurfer, setWavesurfer] = useState(null);
  const [record, setRecord] = useState(null);
  const [continuousWaveform, setContinuousWaveform] = useState(true);
  const [progress, setProgress] = useState("00:00");
  const [isPaused, setIsPaused] = useState(false);

  const [isRecordingNow, setIsRecordingNow] = useState(false);
  useEffect(() => {
    //create instance
    createWaveSurfer();
    // fetching available Microphone devices
    // RecordPlugin.getAvailableAudioDevices().then(setAvailableMics);
    //showing Time line under Record
    RecordPlugin.Timeline;
    //escape warning
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    const body = document.body;
    if (isRecordingNow) {
      body.classList.add("recording-active");
    } else {
      body.classList.remove("recording-active");
    }
  }, [isRecordingNow]);
  // Create  timeline "bottom line"
  const bottomTimeline = Timeline.create({
    height: 1,
    timeInterval: 0.1,
    primaryLabelInterval: 1,
    primaryLabelSpacing: "15px",
    style: {
      fontSize: "10px",
      color: "##5E738A",
    },
  });

  const createWaveSurfer = () => {
    if (wavesurfer) wavesurfer.destroy();
    const ws = WaveSurfer.create({
      container: micRef.current, //Definition container
      waveColor: "#fff", 
      progressColor: "#F94144",
      cursorColor: "#285FF5",
      plugins: [bottomTimeline],
    });

    const rec = ws.registerPlugin(
      RecordPlugin.create({
        renderRecordedAudio: false,
        continuousWaveform,
        continuousWaveformDuration: 30,
      })
    );

    rec.on("record-end", (blob) => {
      const url = URL.createObjectURL(blob);
      const newWs = WaveSurfer.create({
        container: recordingsRef.current,
        waveColor: "#656565",
        progressColor: "#656565",
        cursorColor: "#285FF5",
        url,
      });
        //showing Recorded voice
      const playBtn = document.createElement("button");
      playBtn.textContent = "Play";
      playBtn.onclick = () => newWs.playPause();
      newWs.on("pause", () => (playBtn.textContent = "Play"));
      newWs.on("play", () => (playBtn.textContent = "Pause"));
      recordingsRef.current.appendChild(playBtn);
      //If I want to download video
      // const downloadLink = document.createElement("a");
      // Object.assign(downloadLink, {
      //   href: url,
      //   download: `recording.${
      //     blob.type.split(";")[0].split("/")[1] || "webm"
      //   }`,
      //   textContent: "Download recording",
      //   style: "display:block; margin-top: 0.5rem;",
      // });
      // recordingsRef.current.appendChild(downloadLink);
    });

    rec.on("record-progress", (time) => {
      const mins = Math.floor((time % 3600000) / 60000);
      const secs = Math.floor((time % 60000) / 1000);
      setProgress(
        `${mins < 10 ? "0" + mins : mins}:${secs < 10 ? "0" + secs : secs}`
      );
    });

    setWavesurfer(ws);
    setRecord(rec);
  };
    //Handle Recoding state
  const handleRecord = async () => {
    if (record.isRecording() || record.isPaused()) {
      record.stopRecording();
      setIsPaused(false);
      setIsRecordingNow(false);
    } else {
      await record.startRecording();
      setIsRecordingNow(true);
    }
  };
    //Handling pause state
  const handlePause = () => {
    if (record.isPaused()) {
      record.resumeRecording();
      setIsPaused(false);
    } else {
      record.pauseRecording();
      setIsPaused(true);
    }
  };

  return (
    <div>
      <div
        id="mic"
        ref={micRef}
        style={{ border: "1px solid #ddd", borderRadius: 4, marginTop: "1rem" }}
        className={`mic-waveform ${
          isRecordingNow ? "recording" : "not-recording"
        }`}
      ></div>

      <div
        id="recordings"
        ref={recordingsRef}
        style={{ margin: "1rem 0" }}
      ></div>

      <div className="center">
        <p>{progress}</p>
      </div>
      <button onClick={handleRecord} disabled={!record}>
        {record?.isRecording() || record?.isPaused() ? "Stop" : "Record"}
      </button>

      {record?.isRecording() || record?.isPaused() ? (
        <button onClick={handlePause}>{isPaused ? "Resume" : "Pause"}</button>
      ) : null}
    </div>
  );
}
