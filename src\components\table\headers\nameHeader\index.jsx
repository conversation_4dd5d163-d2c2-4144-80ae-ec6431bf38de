import Checkbox from '@/components/shared/checkbox';
import React, { useEffect, useRef } from 'react';
import tw from 'twin.macro';

/**
 * NameHeader component
 *
 * Renders a header cell with a checkbox for selecting or deselecting all rows on the current page.
 * Supports indeterminate state when only some rows are selected, and disables the checkbox when loading or no rows are present.
 *
 * @param {Object} props - The component props.
 * @param {Array<number>} props.currentRows - Array of row IDs on the current page.
 * @param {Array<number>} props.selectedRows - Array of currently selected row IDs.
 * @param {Function} props.setSelectedRows - Function to update the selected rows.
 * @param {boolean} props.loading - Whether the table is in a loading state.
 */

const NameHeader = ({ currentRows, selectedRows, setSelectedRows, loading }) => {
  const allSelected = currentRows.length > 0 && currentRows.every(id => selectedRows.includes(id));
  const someSelected =
    currentRows.length > 0 && currentRows.some(id => selectedRows.includes(id)) && !allSelected;
  const disabled = loading || currentRows.length === 0;

  // Ref for the checkbox to set indeterminate state
  const checkboxRef = useRef(null);

  useEffect(() => {
    if (checkboxRef.current) {
      checkboxRef.current.indeterminate = someSelected;
    }
  }, [someSelected]);

  return (
    <div css={[tw`flex`, { transform: 'translateX(-8px)' }]}>
      <Checkbox
        ref={checkboxRef}
        disabled={disabled}
        checked={allSelected}
        indeterminate={someSelected}
        onChange={e => {
          if (someSelected) {
            // If indeterminate, remove all current page rows from selectedRows
            setSelectedRows(selectedRows.filter(id => !currentRows.includes(id)));
          } else if (e.target.checked) {
            // Add all current page IDs to selectedRows (avoid duplicates)
            setSelectedRows([...new Set([...selectedRows, ...currentRows])]);
          } else {
            // Remove all current page IDs from selectedRows
            setSelectedRows(selectedRows.filter(id => !currentRows.includes(id)));
          }
        }}
        containerStyle={tw`!py-0 !px-0 !items-center !gap-0 pr-3 !cursor-default`}
        checkboxCustomStyle={disabled ? tw`!top-0 opacity-50 cursor-not-allowed` : tw`!top-0`}
      />
      Name
    </div>
  );
};

export default NameHeader;
