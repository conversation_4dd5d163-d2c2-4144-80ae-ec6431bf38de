import tw from 'twin.macro';
import BlackButton from '@/components/shared/blackButton';
import NormalModal from '@/components/shared/normalModal';
import { getWindowDimensions } from '@/utils/helpers';

const ThanksModal = ({ openModal, handleCLoseModal, handleBackButton }) => {
  const { width } = getWindowDimensions();
  return (
    <NormalModal
      handleClose={handleCLoseModal}
      backgroundModal={'rgba(0, 0, 0,0.25)'}
      hideIcon
      containerModalStyle={tw`bg-[rgba(255, 255, 255, 0.7)] rounded-[20px]! p-2 h-auto`}
      content={
        <div tw="p-8 text-center">
          {width < 767 ? (
            <h1 tw="font-bold xl:text-3xl lg:text-xl text-[36px]">Thank you </h1>
          ) : (
            <h1 tw="mb-8 text-lg font-bold xl:text-3xl lg:text-xl">
              Thank you for connecting with us!
            </h1>
          )}
          {width < 767 && <p tw="text-[24px] max-sm:mb-4">for connecting with us!</p>}
          <BlackButton text="Back to Rehabitaire" handleCLickButton={handleBackButton} />
        </div>
      }
      open={openModal}
    />
  );
};
export default ThanksModal;
