import PrimaryButton from '@/components/shared/primaryButton';
import tw from 'twin.macro';

const SaveChangesButton = () => {
  return (
    <div tw="relative px-[24px] py-[20px] flex justify-end border-t border-border_stroke">
      <PrimaryButton
        customStyle={tw`rounded-[6px] font-medium text-[.875rem]`}
        text={'Save Change'}
      />
    </div>
  );
};

export default SaveChangesButton;
