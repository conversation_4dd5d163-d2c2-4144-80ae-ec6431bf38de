import LoadingElement from '@/components/shared/loadingElement';
import tw from 'twin.macro';

const PrimaryButton = ({
  text,
  handleClick,
  isSubmitting,
  disable,
  type = 'submit',
  customStyle,
  ref,
  ...rest
}) => {
  return (
    <button
      tw="bg-Primary text-sm px-4 py-2 transition-all duration-500 rounded-[10px] font-bold border-[1px] border-Primary_700"
      className="primary-button"
      onClick={handleClick}
      type={type}
      ref={ref}
      css={[
        disable
          ? tw`bg-neutral_300 border-border_stroke opacity-70 border`
          : tw`bg-Primary border-Primary_700 hover:bg-Primary_600`,
        customStyle,
      ]}
      disabled={disable}
      {...rest}
    >
      {isSubmitting ? (
        <div tw="flex justify-center items-center">
          <LoadingElement width={28} height={28} />
        </div>
      ) : (
        <p>{text}</p>
      )}
    </button>
  );
};
export default PrimaryButton;
