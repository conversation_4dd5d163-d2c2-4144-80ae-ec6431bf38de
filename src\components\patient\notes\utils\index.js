/**
 * Strip HTML, images, and attachments from input and return clean text.
 * @param {string} html The input string containing HTML content.
 * @return {string} A single-string concatenation of all text content.
 */
export function extractPlainText(html) {
  // 1. Remove <img> tags entirely
  const noImages = html.replace(/<img[^>]*>/gi, '');

  // 2. Remove all remaining HTML tags
  const noTags = noImages.replace(/<\/?[^>]+(>|$)/g, '');

  // 3. Decode HTML entities (optional, for things like &amp;, &lt;)
  const txtArea = document.createElement('textarea');
  txtArea.innerHTML = noTags;
  let text = txtArea.value;

  // 4. Normalize whitespace: collapse multiple newlines/spaces into a single space
  text = text.replace(/\s+/g, ' ').trim();

  return text;
}
