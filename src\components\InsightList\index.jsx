// components/shared/InsightCardList.js
import TooltipIcon from '@/components/shared/tooltipIcon';
import 'twin.macro';
import SectionCards from '../sectionsCard';
import tw from 'twin.macro';
// import tw from 'twin.macro';

const InsightCardList = ({
  title = 'Insights',
  data = [],
  unitSuffix = '', // e.g., '%'
  rootElementCalsses,
}) => {
  return (
    <SectionCards customStyle={[tw`w-[45%]  px-6 py-5`, rootElementCalsses]}>
      <h2 tw="text-text_primary font-semibold text-lg mb-5 py-2.5">{title}</h2>
      <div tw="grid grid-cols-1 sm:grid-cols-2 gap-6">
        {data.map(item => (
          <div
            key={item.title}
            tw="rounded-[.625rem] p-6"
            style={{ backgroundColor: item.color || '#F5F5F5' }}
          >
            <div tw="flex items-center gap-1.5 mb-2.5">
              <p tw="text-base text-text_primary">{item.title}</p>
              {item.tooltipText && <TooltipIcon isActiveOnHover={true} text={item.tooltipText} />}
            </div>
            <div tw="flex items-center gap-2 font-semibold text-[1.8rem] text-text_primary">
              <span>{item.insight}</span>
              {item.unit ? (
                <span tw="text-lg font-medium text-text_secondary">{item.unit}</span>
              ) : (
                unitSuffix && <span>{unitSuffix}</span>
              )}
            </div>
          </div>
        ))}
      </div>
    </SectionCards>
  );
};

export default InsightCardList;
