import 'twin.macro';
import tw, { css } from 'twin.macro';
import DownArrow from '@assets/svgs/down-arrow.svg';
import moment from 'moment';
/**
 * CustomToolbar Component
 *
 * Renders the calendar navigation toolbar with month/year display and prev/next buttons
 *
 * @param {Object} toolbar - Toolbar properties from react-big-calendar
 * @param {string} toolbar.label - The current view's label (e.g., "April 2025")
 * @param {Function} toolbar.onNavigate - Function to navigate between dates
 * @param {string} toolbar.grayIcon - Icon source for the navigation arrows
 * @param {Function} toolbar.onMonthYearClick - Function to handle month/year click
 */
const CustomToolbar = toolbar => {
  const MonthName = toolbar.label.split(' ')[0];
  const Year = toolbar.label.split(' ')[1];
  // console.log('toolbar.label => ', new Date(toolbar.label));

  const monthYear = `${MonthName}, ${Year}`;

  // const isPrevDisabled = toolbar.joinedDate && toolbar.joinedDate === monthYear;

  const isCurrentMonth = moment(monthYear).isSame(moment(), 'month');
  const goToBack = () => {
    toolbar.onNavigate('PREV');
  };

  const goToNext = () => {
    toolbar.onNavigate('NEXT');
  };

  const handleMonthYearClick = () => {
    if (toolbar.onMonthYearClick) {
      toolbar.onMonthYearClick();
    }
  };

  return (
    <div tw="space-y-4 mb-4">
      <div tw="flex gap-4 justify-between items-center w-full  ">
        <div
          onClick={goToBack}
          tw="border rounded-[4px] border-text_secondary w-[28px] h-[28px] cursor-pointer flex justify-center items-center"
        >
          <img src={toolbar?.grayIcon} alt="arrow" />
        </div>
        <div
          tw="flex gap-1 justify-center items-center text-[1.05em] font-[700] cursor-pointer hover:border-text_secondary rounded-md py-1.5 px-3"
          css={css`
            border: ${toolbar.isYearMonthPickerOpen
              ? '1px solid #656565'
              : '1px solid transparent'};
          `}
          onClick={handleMonthYearClick}
        >
          <div tw="flex gap-1 justify-center items-center">
            <p>{monthYear}</p>
          </div>

          {/* icon */}
          <img src={DownArrow} alt="icon" />
        </div>
        <div
          onClick={goToNext}
          css={[isCurrentMonth && tw`cursor-not-allowed bg-disable pointer-events-none`]}
          tw="border rounded-[4px] border-text_secondary w-[28px] h-[28px] rotate-180 cursor-pointer flex justify-center items-center"
        >
          <img src={toolbar.grayIcon} alt="arrow" />
        </div>
      </div>
      {toolbar?.rollingAdherence && (
        <div
          tw="rounded-md py-2.5 text-center  text-sm font-semibold text-white"
          css={[
            css`
              background-color: ${toolbar?.rollingAdherence?.color};
            `,
          ]}
        >
          Rolling adherence: {toolbar?.rollingAdherence?.rate}
        </div>
      )}
    </div>
  );
};

export default CustomToolbar;
