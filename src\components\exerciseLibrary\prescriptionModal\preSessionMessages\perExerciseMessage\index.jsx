import { useState, useRef } from 'react';
import tw from 'twin.macro';
import VoiceIcon from '@assets/svgs/voice.svg';
import TextArea from '@/components/shared/textarea';
import Collapse from '@/components/shared/collapse';
import MessageTag from './messageTag';
import ExerciseImage from '@assets/images/exercise-library/exercise3.png';
import FullScreen from '@assets/svgs/exercise-library/full screen.svg';
import { ModalViews } from '../..';
import Audio from '@/components/shared/audio'; // Import the Audio component

const MessageTagTypes = {
  NO_AUDIO: 'No audio',
  NO_NOTES: 'No notes',
  RECORDED: 'Voice message added',
  NOTE_ADDED: 'Note added',
  AI_VOICE: 'Voice message added',
};

const PerExerciseMessage = ({
  exerciseId,
  exerciseNumber,
  exerciseTitle,
  imageUrl = ExerciseImage,
  videoUrl = 'https://assets.mixkit.co/videos/40881/40881-720.mp4',
  register,
  errors,
  setVoiceField,
  setModalView,
  watch,
  setValue,
  setCurrentlyPlayingAudioUrl,
  currentlyPlayingAudioUrl,
}) => {
  // Watch the message object for this exercise
  const message = watch(`exerciseMessages.${exerciseId}`) || {};

  // Watch the voice field for this exercise
  const exerciseVoice = watch(`exerciseMessages.${exerciseId}.voice`);

  // Determine tag types
  const noteTag = message.note ? MessageTagTypes.NOTE_ADDED : MessageTagTypes.NO_NOTES;
  const audioTag = !message.voice
    ? MessageTagTypes.NO_AUDIO
    : message.voice.isGenerated
      ? MessageTagTypes.AI_VOICE
      : MessageTagTypes.RECORDED;
  const [open, setOpen] = useState(true);
  const [hovered, setHovered] = useState(false);
  const videoRef = useRef(null);

  const handleFullScreen = () => {
    if (videoRef.current) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen();
      } else if (videoRef.current.webkitRequestFullscreen) {
        videoRef.current.webkitRequestFullscreen();
      } else if (videoRef.current.mozRequestFullScreen) {
        videoRef.current.mozRequestFullScreen();
      } else if (videoRef.current.msRequestFullscreen) {
        videoRef.current.msRequestFullscreen();
      }
    }
  };
  const handleRecordPerExerciseMessage = () => {
    setVoiceField(`exerciseMessages.${exerciseId}.voice`); // Set the field to update
    setModalView(ModalViews.VOICE_RECORDING); // Navigate to the voice recording view
  };

  return (
    <Collapse
      header={
        <div css={tw`flex items-center gap-3`}>
          <div css={tw`flex gap-[6px] text-[18px] font-bold`}>
            <p>Exercise {exerciseNumber}</p>
            <p css={tw`text-text_tertiary`}>{exerciseTitle}</p>
          </div>
          {/* Message tags with transition */}
          <div
            css={[
              tw`flex gap-3 transition-all duration-500`,
              open ? tw`opacity-0 pointer-events-none` : tw`opacity-100 pointer-events-auto`,
            ]}
          >
            {/* tags section depending on the form state */}
            <MessageTag isActive={!!exerciseVoice} type={audioTag} />
            <MessageTag isActive={message?.note?.length > 0} type={noteTag} />
          </div>
        </div>
      }
      body={
        <div css={tw`flex gap-6 pb-[10px]`}>
          <div css={tw`relative basis-[26%] h-[180px] flex flex-col`}>
            {/* Image always rendered, fades out on hover if videoUrl */}
            <div onMouseEnter={() => setHovered(true)} onMouseLeave={() => setHovered(false)}>
              <img
                src={imageUrl}
                css={[
                  tw` rounded-[6px] object-cover w-full h-full absolute top-0 left-0 transition-opacity duration-1000`,
                  videoUrl && hovered ? tw`opacity-0` : tw`opacity-100`,
                ]}
              />
              {/* Video only rendered if videoUrl, fades in on hover */}
              {videoUrl && (
                <div>
                  <img
                    src={FullScreen}
                    alt="FullScreen"
                    css={[
                      tw` absolute top-[60%] right-[5px] transition-opacity duration-1000 cursor-pointer z-10`,
                      hovered ? tw`opacity-100` : tw`opacity-0`,
                    ]}
                    onClick={handleFullScreen}
                  />
                  <video
                    ref={videoRef}
                    src={videoUrl}
                    autoPlay={hovered}
                    muted
                    key={hovered}
                    loop
                    preload="none"
                    css={[
                      tw`rounded-[7px] object-cover w-full h-full absolute top-0 left-0 transition-opacity duration-1000 cursor-pointer`,
                      hovered ? tw`opacity-100` : tw`opacity-0`,
                    ]}
                  />
                </div>
              )}
            </div>
            <div
              css={[
                tw`w-full flex items-center justify-center absolute bottom-0 bg-neutral_50 rounded-b-[6px]`,
                exerciseVoice ? tw`px-2 py-2` : tw`px-4 py-[10px]`,
              ]}
            >
              {exerciseVoice ? (
                // Render the Audio component if exerciseMessages.${exerciseId}.voice exists
                <Audio
                  audioUrl={exerciseVoice.audioUrl}
                  duration={exerciseVoice.duration}
                  handleDeleteAudio={() => {
                    setValue(`exerciseMessages.${exerciseId}.voice`, null); // Remove the voice object from the form state
                  }}
                  currentlyPlayingAudioUrl={currentlyPlayingAudioUrl}
                  setCurrentlyPlayingAudioUrl={setCurrentlyPlayingAudioUrl}
                  shouldPause={!open}
                />
              ) : (
                // Render the button if exerciseMessages.${exerciseId}.voice does not exist
                <button
                  css={tw`flex items-center justify-center gap-2 text-[15px] font-medium text-Primary_800`}
                  onClick={handleRecordPerExerciseMessage}
                >
                  <img src={VoiceIcon} alt="voice-button-icon" />
                  Add voice message
                </button>
              )}
            </div>
          </div>
          <div css={tw`basis-[74%]`}>
            <TextArea
              name={`exerciseMessages.${exerciseId}.note`}
              register={register}
              placeholder="Add notes.."
              containerSTyle={tw`h-[180px] p-0 rounded-[6px]`}
              textAreaStye={tw`h-full px-5 py-2 text-[15px] font-medium`}
              maxLength={255}
              errorMessage={errors?.exerciseMessages?.[exerciseId]?.note?.message}
            />
          </div>
        </div>
      }
      open={open}
      handelToggleCollapse={() => {
        setOpen(prev => !prev);
      }}
      containerStyle={tw`!px-5 !pt-[5px] flex flex-col gap-3 bg-neutral_200 border border-border_stroke rounded-[10px] overflow-visible`}
      headerCustomStyle={tw`!px-0 !py-0 flex items-center gap-[6px]`}
      arrowIconCustomStyle={tw`w-4 h-4`}
    />
  );
};

export default PerExerciseMessage;
