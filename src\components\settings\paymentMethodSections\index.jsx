import SectionCards from '@/components/sectionsCard';
import PrimaryButton from '@/components/shared/primaryButton';
import PaymentCardList from '../paymentCardList';
import Visa from '@/assets/svgs/settings/visa icon.svg';
import Master from '@/assets/svgs/settings/master.svg';
import Paypal from '@/assets/svgs/settings/paypal.svg';
import Sepa from '@/assets/svgs/settings/SEPA.svg';
import { useForm } from 'react-hook-form';
import { useEffect, useState } from 'react';
import GenericModal from '@/components/genericModal';
import PaymentMethods from '../../payment/paymentMethods';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import ExerciseDetailsModelHeader from '@/components/exerciseLibrary/exerciseDetailsModelHeader';
import setAsDefault from '@assets/svgs/settings/set as default icon.svg';
import ActionModalHeader from '@/components/actionModelHeader';
import ActionModal from '@/components/actionModal';
import tw from 'twin.macro';

const PaymentMethodSections = () => {
  const [openModal, setOpeModal] = useState(false);
  const [stripePromise, setStripePromise] = useState(null);
  const [openAsDefault, setOpenAsDefault] = useState(false);
  const [openRemovePaymentModal, setOPenRemovePaymentModal] = useState(false);
  const [clientSecret, setClientSecret] = useState('');

  const handelOpenModal = () => {
    setOpeModal(true);
  };
  const handelCloseModal = () => {
    setOpeModal(false);
  };
  const {
    control,
    register,
    formState: { errors },
    watch,
  } = useForm({ defaultValues: { paymentMethod: 'card', PayAs: 'individual' } });

  const cards = [
    {
      id: 1,
      title: 'Visa ending in 3233',
      expire: 'Expires 07/27',
      isDefault: true,
      imageUrl: Visa,
      isExpires: false,
    },
    {
      id: 2,
      title: 'Mastercard ending in 1234',
      expire: 'Expires 01/26',
      isDefault: false,
      imageUrl: Master,
      isExpires: false,
    },
    {
      id: 3,
      title: 'Visa ending in 1122',
      expire: 'Expires 01/26',
      isDefault: false,
      imageUrl: Visa,
      isExpires: true,
    },
  ];
  const paypal = [
    {
      id: 1,
      title: 'Visa ending in 3233',
      expire: 'Expires 07/27',
      isDefault: true,
      imageUrl: Paypal,
      isExpires: false,
    },
  ];
  const SepaData = [
    {
      id: 1,
      title: 'Visa ending in 3233',
      expire: 'Expires 07/27',
      isDefault: true,
      imageUrl: Sepa,
      isExpires: false,
    },
  ];

  useEffect(() => {
    try {
      fetch('http://localhost:5252/config').then(async r => {
        const { publishableKey } = await r.json();
        setStripePromise(loadStripe(publishableKey));
      });
    } catch (error) {
      console.log('error', error);
    }
  }, []);

  useEffect(() => {
    try {
      fetch('http://localhost:5252/create-payment-intent', {
        method: 'POST',
        body: JSON.stringify({}),
      }).then(async result => {
        var { clientSecret } = await result.json();
        setClientSecret(clientSecret);
      });
    } catch (error) {
      console.log('error', error);
    }
  }, []);

  const handeleCloseDeafuatModal = () => {
    setOpenAsDefault(false);
  };
  const handeleCloseRemovePaymentModal = () => {
    setOPenRemovePaymentModal(false);
  };

  const handelOpenDeleteModal = id => {
    setOPenRemovePaymentModal(true);
    console.log('id', id);
  };

  const handelSetAsDefault = id => {
    setOpenAsDefault(true);
    console.log('id', id);
  };

  return (
    <SectionCards customStyle={tw`p-[24px]`}>
      <div tw="flex justify-between items-center">
        <p tw="font-medium text-[1.25rem]">Payment Methods</p>
        <PrimaryButton
          customStyle={tw`px-[16px] py-[12px] rounded-[8px] [box-shadow: 0px 1px 2px 0px #1018280D]`}
          text={'+ Add payment method'}
          handleClick={handelOpenModal}
        />
      </div>
      <PaymentCardList
        cards={cards}
        title={'Cards'}
        clickOnDeleteIcon={handelOpenDeleteModal}
        handelSetAsDefault={handelSetAsDefault}
      />
      <PaymentCardList cards={paypal} title={'Paypal'} clickOnDeleteIcon={handelOpenDeleteModal} />
      <PaymentCardList cards={SepaData} title={'Sepa'} clickOnDeleteIcon={handelOpenDeleteModal} />
      {clientSecret && stripePromise && (
        <Elements stripe={stripePromise} options={{ clientSecret }}>
          {openModal && (
            <GenericModal
              backgroundModal={'rgba(0,0,0,0.4'}
              PrimaryButtonText={'Confirm'}
              clickOnPrimaryButton={handelCloseModal}
              clickOnSecondaryButton={handelCloseModal}
              secondaryButtonText={'Cancel'}
              PrimaryButtonStyle={tw`basis-[50%] flex-1 rounded-[6px]`}
              SecondaryButtonStyle={tw`basis-[50%] flex-1 rounded-[6px]`}
              contetnContainerStyle={tw`w-[40vw] max-h-[70vh] overflow-y-auto overflow-hidden`}
              openModel={openModal}
              containerModalStyle={tw`rounded-[12px]!`}
              handelCloseMode={handelCloseModal}
              title={
                <ExerciseDetailsModelHeader
                  handelCloseClick={handelCloseModal}
                  title="Add Payment Method"
                  hideArrow={true}
                />
              }
              content={
                <PaymentMethods
                  control={control}
                  showTitle={false}
                  register={register}
                  watch={watch}
                  errors={errors}
                  customStyle={tw`border-none`}
                />
              }
            />
          )}
        </Elements>
      )}
      {openAsDefault && (
        <ActionModal
          open={openAsDefault}
          handleClose={handeleCloseDeafuatModal}
          primaryActionHandler={() => console.log('asds')}
          customIcon={setAsDefault}
          title="Set as default"
          actionButtonText="Apply"
          PrimaryButtonStyle={tw`border-[#285FF5]! bg-[#EEF3FF]! text-[#285FF5] font-medium`}
          customHeaderStyle={tw`border-[#EEF3FF] bg-[#EEF3FF80]`}
          description="Set Mastercard ending in 1234 to your default payment method?"
        />
      )}
      {openRemovePaymentModal && (
        <ActionModal
          open={openRemovePaymentModal}
          handleClose={handeleCloseRemovePaymentModal}
          primaryActionHandler={() => console.log('asds')}
          title="Remove payment method"
          actionButtonText="Remove"
          description="Remove Mastercard ending in 1234 from your payment methods?"
        />
      )}
    </SectionCards>
  );
};

export default PaymentMethodSections;
