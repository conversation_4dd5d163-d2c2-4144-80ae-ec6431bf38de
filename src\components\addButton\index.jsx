import tw from 'twin.macro';
import PrimaryButton from '../shared/primaryButton';
import plus from '@assets/svgs/plus.svg';
function AddButton({ text, customeIcon, onClick }) {
  return (
    <PrimaryButton
      text={
        <span tw="text-sm flex gap-2 items-center">
          <img src={customeIcon || plus} />
          <span>{text}</span>
        </span>
      }
      customStyle={tw`!bg-Primary py-2 pl-2.5 pr-3 rounded-md text-text_primary text-sm font-medium`}
      handleClick={onClick}
    />
  );
}

export default AddButton;
