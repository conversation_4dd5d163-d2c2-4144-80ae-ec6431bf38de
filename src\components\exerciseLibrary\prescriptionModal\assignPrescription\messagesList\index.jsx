import React, { useState } from 'react';
import tw from 'twin.macro';
import Audio from '@/components/shared/audio';

const MessagesList = ({ messages }) => {
  // Single source of truth for currently playing audio
  const [currentlyPlayingAudioUrl, setCurrentlyPlayingAudioUrl] = useState(null);

  // If no messages exist, render nothing
  if (messages.length === 0) {
    return null;
  }

  return (
    <div css={tw`mt-[1.125rem] flex flex-col gap-5`}>
      {messages.map((message, index) => (
        <div key={index} css={tw`flex flex-col gap-[0.625rem]`}>
          {/* Title */}
          <div
            css={tw`w-fit px-[0.625rem] py-1 flex justify-center items-center rounded-[0.25rem] bg-Primary_100 text-Primary_800 font-medium text-[0.93rem]`}
          >
            {message.type === 'general' ? 'General Message' : message.title}
          </div>
          {/* Body */}
          <div
            css={tw`rounded-[0.625rem] bg-neutral_50 px-5 pt-[6px] pb-4 flex flex-col gap-[0.625rem] break-words`}
          >
            {/* Voice */}
            {message.data?.voice && (
              <div>
                <Audio
                  audioUrl={message.data.voice.audioUrl}
                  duration={message.data.voice.duration}
                  containerStyle={tw`gap-[2.6%]`}
                  playPauseStyle={tw`w-[6%]`}
                  hideActions
                  currentlyPlayingAudioUrl={currentlyPlayingAudioUrl}
                  setCurrentlyPlayingAudioUrl={setCurrentlyPlayingAudioUrl}
                />
              </div>
            )}
            {/* Note */}
            {message.data?.note && (
              <p css={tw`text-[0.93rem] text-text_primary font-medium`}>{message.data.note}</p>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default MessagesList;
