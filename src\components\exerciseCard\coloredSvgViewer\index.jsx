import React, { useMemo } from 'react';
import SVGComponent from '@/components/exerciseCard/svgComponent';


function getRandomColor() {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
}

// Solution 1: Access DOm directly

// export default function ColoredSvgViewer() {

//       useEffect(()=>{
//         PATH_IDS.map((item)=>{
//             const joint=document.getElementById(item);
//             joint.style.fill=getRandomColor();
//         })
//       },[])

//       const originalSvg = <SVGComponent />;

//     return {originalSvg}
//     }

// Solution 2: using Memo
export default function ColoredSvgViewer({ svgWidth, svgHeight, PATH_IDS }) {
  const pathFills = useMemo(() => {
    return PATH_IDS.reduce((acc, item) => {
      acc[item.id] = item.color;
      return acc;
    }, {});
  }, []);

  return <SVGComponent pathFills={pathFills} svgWidth={svgWidth} svgHeight={svgHeight} />;
}
