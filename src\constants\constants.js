export const screenSizes = {
  sm_custom: 600,
  sm: 640,
  md: 768,
  md_custom: 900,
  lg: 1024,
  xl: 1280,
  custom_xl: 1440,
  '2xl': 1536,
  '3xl': 1706,
  '4xl': 1920,
  '6xl': 2400,
};

export const gender_opetions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
];

export const height_options = [
  { value: 'cm', label: 'cm' },
  { value: 'm', label: 'm' },
  { value: 'in', label: 'in' },
  { value: 'ft', label: 'ft' },
];

export const weight_options = [
  { value: 'kg', label: 'kg' },
  { value: 'g', label: 'g' },
  { value: 'lb', label: 'lb' },
];

export const dayPerformanceStatus = {
  MISSED: 'missed',
  INCOMPLETE: 'incomplate',
  COMPLETE: 'complated',
};
export const prescriptionMode = {
  TEMPLATE: 'template',
  PRESCRIPTION: 'prescription',
  EDIT: 'edit',
};

export const patietReorderableColumns = [
  'Status',
  'Prescription',
  'Overall adherence',
  'Adherence',
  'Pain level',
  'Assigned PT',
  'Email address',
  'Date joined',
  'Last visit',
  'Overall period',
  'Time left',
  'Expiration date',
];

export const staffReorderableColumns = [
  'Email address',
  'Role',
  'No of patients',
  'Date joined',
  'Last activity',
];
