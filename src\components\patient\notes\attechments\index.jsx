import 'twin.macro';
import AttechmentsList from './attechmentsList';

export default function Attechments({ images, documents, removeFile, setSelectedImage }) {
  return (
    <div tw="px-4 pt-4 space-y-4" onClick={e => e.stopPropagation()}>
      <AttechmentsList list={documents} type="doc" removeFile={removeFile} />
      <AttechmentsList
        list={images}
        type="img"
        removeFile={removeFile}
        onClickOnAttechment={setSelectedImage}
      />
    </div>
  );
}
