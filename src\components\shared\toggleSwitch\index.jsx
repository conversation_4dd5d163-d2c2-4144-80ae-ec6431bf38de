import tw from 'twin.macro';

const ToggleSwitch = ({ isActive, onToggle }) => {
  return (
    <div
      onClick={onToggle}
      tw={
        'flex items-center rounded-full transition-colors duration-300 cursor-pointer w-[44px] h-[24px]'
      }
      css={isActive ? tw`bg-Primary_600` : tw`bg-gray-300`}
    >
      <div
        tw={'w-5 h-5 bg-white rounded-full shadow-md transition-transform duration-300 transform'}
        css={isActive ? tw`translate-x-[125%]` : tw`translate-x-[15%]`}
      />
    </div>
  );
};
export default ToggleSwitch;
