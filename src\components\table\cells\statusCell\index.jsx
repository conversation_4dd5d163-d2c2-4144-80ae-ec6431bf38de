import tw from "twin.macro";

/**
 * StatusCell component renders a colored status dot and the status label for a patient.
 * The color of the dot and text reflects the patient's status.
 *
 * @param {Object} props - The component props.
 * @param {string} props.value - The status value to display (e.g., "High adherence", "High risk").
 */

const StatusCell = ({ value }) => {
  const statusClasses = {
    "High adherence": "bg-success text-success",
    "High risk": "bg-error text-error", // Use your Tailwind class here
    "Low adherence": "bg-warning text-warning",
    Inactive: "bg-stroke text-text_secondary",
    "No prescription": "bg-stroke text-text_secondary",
    "Expired prescription": "bg-stroke text-text_secondary",
    Discharged: "bg-stroke text-text_secondary",
  };

  const classes = statusClasses[value] || "bg-stroke text-text_secondary";

  return (
    <div css={tw`flex items-center`}>
      <span
        className={`inline-block w-2 h-2 rounded-full ${classes.split(" ")[0]}`}
      />
      <span className={`ml-2 ${classes.split(" ")[1]}`}>{value}</span>
    </div>
  );
};

export default StatusCell;
