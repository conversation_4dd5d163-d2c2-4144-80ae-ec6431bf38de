import { AnimatePresence, motion } from 'framer-motion';
import 'twin.macro';

const PreviewImage = ({ imagePreview, setImagePreview }) => {
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.25 }}
        tw="flex fixed inset-0 z-[999] justify-center items-center bg-[rgba(0,0,0,0.4)] backdrop-blur-sm bg-opacity-80"
        onClick={() => setImagePreview(null)}
      >
        <img src={imagePreview} alt="imagePreview" tw="object-cover max-w-full max-h-full" />
        <button
          tw="absolute top-5 right-5 text-3xl font-bold text-white"
          onClick={e => {
            e.stopPropagation();
            setImagePreview(null);
          }}
        >
          ✖
        </button>
      </motion.div>
    </AnimatePresence>
  );
};

export default PreviewImage;
