import complated from '@/assets/svgs/patient/complated.svg';
import incomplate from '@/assets/svgs/patient/incomplate.svg';
import missedIcon from '@assets/svgs/patient/missed-icon.svg';
import reportedPain from '@assets/svgs/patient/reported-pain.svg';
import feedback from '@assets/svgs/patient/feedback.svg';

export const fieldsThPermistions = {
  patient_image: true,
  patient_name: true,
  current_pt: false,
  email: true,
  phone_number: true,
  birthdata: false,
  gender: true,
  height: false,
  weight: false,
  medical_condition: true,
  pain_area: false,
};

export const insightsData = [
  {
    title: 'Overall adherence',
    color: '#EBFDF5',
    insight: '74.3%',
    tooltipText: 'This is overall adherence insght',
  },
  {
    title: 'Rolling adherence',
    color: '#FFF1F2',
    insight: '52.3%',
    tooltipText: 'This is overall adherence insght',
  },
  {
    title: 'Exercise hours',
    color: '#F5F5F5',
    insight: '30.5',
    unit: 'Hours',
  },
  {
    title: 'Overall Period',
    color: '#F5F5F5',
    insight: '13',
    tooltipText: 'This is overall adherence insght',
    unit: 'Weeks',
  },
  {
    title: 'Completed Sessions',
    color: '#F5F5F5',
    insight: '19',
    unit: 'Sessions',
  },
  {
    title: 'Missed Sessions',
    color: '#F5F5F5',
    insight: '5',
    unit: 'Sessions',
  },
  {
    title: 'Total Prescriptions',
    color: '#F5F5F5',
    insight: '3',
    unit: 'Sessions',
  },
];

export const isSubmit = Object.values(fieldsThPermistions).some(val => val);

export const legend_data = [
  {
    icon: complated,
    text: 'Completed session',
    color: '#0B834F',
  },
  {
    icon: reportedPain,
    text: 'Reported Pain',
  },
  {
    icon: incomplate,
    text: 'Incomplete session',
    color: '#FC9D14',
  },
  {
    icon: feedback,
    text: 'Patient sent feedback',
  },
  {
    icon: missedIcon,
    text: 'Missed session',
    color: '#D12E3C',
  },
];
