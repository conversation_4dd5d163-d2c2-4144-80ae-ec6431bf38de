import binTrash from '@/assets/svgs/patient/bin-trash-outline.svg';
import GenericModal from '@/components/genericModal';
import ActionModalHeader from '../actionModelHeader';
import ExerciseDetailsModelHeader from '../exerciseLibrary/exerciseDetailsModelHeader';
import tw from 'twin.macro';

const ActionModal = ({
  open,
  handleClose,
  primaryActionHandler,
  title,
  description,
  actionButtonText = 'Delete',
  cancelButtonText = 'Cancel',
  containerModalStyle,
  containerButtonsStyle,
  contetnContainerStyle,
  SecondaryButtonStyle,
  customIcon = binTrash,
  PrimaryButtonStyle,
  customHeaderStyle,
  hasSecondaryButton = true,
}) => {
  return (
    <GenericModal
      handelCloseMode={handleClose}
      openModel={open}
      modalStyle={tw`bg-[rgba(0,0,0,0.4)] z-[100]`}
      title={
        <ExerciseDetailsModelHeader
          containerStyle={tw`border-none pb-0`}
          handelCloseClick={handleClose}
          customTitle={
            <ActionModalHeader
              customStyle={[tw`border-error_50 bg-error_100`, customHeaderStyle]}
              icon={customIcon}
              alt={title}
            />
          }
          hideArrow={true}
        />
      }
      titleStyle={tw`rounded-t-[12px] overflow-hidden !pb-0`}
      content={
        <>
          <div tw="space-y-1.5 px-6 pb-6">
            <p tw="text-text_primary font-semibold text-[18px]">{title}</p>
            <div tw=" text-text_secondary text-[14px]">{description}</div>
          </div>
        </>
      }
      hasDeleteButton={true}
      typeOfPrimaryButton={'button'}
      deleteText={actionButtonText}
      hasPrimaryButton={false}
      handelCLick={primaryActionHandler}
      secondaryButtonText={cancelButtonText}
      hasSecondaryButton={hasSecondaryButton}
      typeOfSecondaryButton="button"
      clickOnSecondaryButton={handleClose}
      SecondaryButtonStyle={[
        tw`flex-1 !py-[10px] !rounded-[6px] !font-medium bg-white border-border_stroke`,
        SecondaryButtonStyle,
      ]}
      customDeleteButtonStyle={[PrimaryButtonStyle, tw`hover:(bg-error_100)`]}
      containerModalStyle={[
        tw`!max-w-[400px] !min-w-0 h-auto !rounded-[12px]`,
        containerModalStyle,
      ]}
      contetnContainerStyle={[tw`min-h-full`, contetnContainerStyle]}
      containerButtonsStyle={[
        tw`flex justify-between gap-3 bg-neutral_50  border-t border-border_stroke rounded-b-[12px] !px-6 !pt-4 !pb-4`,
        containerButtonsStyle,
      ]}
    />
  );
};

export default ActionModal;
