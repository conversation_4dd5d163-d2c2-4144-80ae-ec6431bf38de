import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import Actions from '../actions';
import Textarea from '@/components/shared/textarea';
import tw from 'twin.macro';
import GenericSelect from '@/components/shared/select';
import { textToSpeechSchema } from './schema';
import { defaultTextToSpeechState } from './module';
import VoiceLoader from './voiceLoader';
import Audio from '@/components/shared/audio';
import ResetIcon from '@assets/svgs/audio/reset.svg';
import { ModalViews } from '../..';

const TextToSpeech = ({ setModalView, setValue, field }) => {
  const [loading, setLoading] = useState(false); // State to track loading
  const [audio, setAudio] = useState(null); // State to store audio URL
  const {
    register,
    handleSubmit,
    watch,
    control,
    formState: { errors, isValid },
  } = useForm({
    resolver: yupResolver(textToSpeechSchema),
    defaultValues: defaultTextToSpeechState,
    mode: 'onChange',
  });

  const onSubmit = async data => {
    setLoading(true);
    setAudio(null);

    // Simulate form submission (e.g., API call)
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate delay
    setAudio({
      audioUrl:
        'https://p.scdn.co/mp3-preview/2726a9595503bf33fdf44d0e85ae8abc7d876d44?cid=774b29d4f13844c495f206cafdad9c86',
      duration: 30,
    });
    setLoading(false);
  };

  const handleSaveAudio = () => {
    const voiceObject = {
      audioUrl: audio.audioUrl,
      duration: audio.duration,
      isGenerated: true, // Indicate that this audio was generated
    };
    setValue(field, voiceObject); // Update the form state
    setModalView(ModalViews.PRE_SESSION_MESSAGES); // Navigate back to the previous view
  };

  const textValue = watch('text') || '';
  const currentTextLength = textValue.length;
  const maxTextLength = 1000;
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div css={tw`px-[2rem] py-[1.8125rem] flex justify-between`}>
        <div css={tw`basis-[45%] flex flex-col gap-[1rem]`}>
          <Textarea
            name="text"
            register={register}
            placeholder="Add text.."
            containerSTyle={tw`w-full p-0 rounded-[0.375rem]`}
            textAreaStye={tw`w-full h-[15.375rem] px-[1.25rem] py-[0.875rem] text-[1rem] text-text_secondary focus:outline-none focus:ring-2 focus:ring-Primary focus:rounded-[0.375rem] resize-none font-medium`}
            maxLength={maxTextLength}
            errorMessage={errors.text?.message}
          />
          <p css={tw`text-[#979ca1] font-medium`}>
            {currentTextLength}/{maxTextLength}
          </p>
        </div>
        <div css={tw`basis-[52%]`}>
          <div css={tw`flex flex-col gap-[1rem]`}>
            <GenericSelect
              control={control}
              name="cloningVoice"
              placeholder="Select a voice"
              options={[
                { value: 'Cloned Voice - PT J. Dizon', label: 'Cloned Voice - PT J. Dizon' },
                { value: 'Female Voice - Default', label: 'Female Voice - Default' },
                { value: 'Rehabitaire Voice', label: 'Rehabitaire Voice' },
              ]}
              errorMessage={errors.cloningVoice?.message}
              customStyles={{
                control: () => ({
                  padding: '0.5rem',
                  cursor: 'pointer',
                }),
                menuList: () => ({
                  maxHeight: '190px',
                }),
              }}
              inputLength={10}
            />
            {loading && <VoiceLoader />} {/* Show VoiceLoader when loading */}
            {audio && (
              <Audio
                audioUrl={audio.audioUrl}
                duration={audio.duration}
                showElapsedTime
                customActionbtns={[
                  { icon: ResetIcon, alt: 'retry generating', callback: onSubmit },
                ]}
                actionContainerStyle={tw`w-[14%]`}
                handleDeleteAudio={() => setAudio(null)}
              />
            )}
          </div>
        </div>
      </div>
      {/* actions */}
      <Actions
        isPrimaryDisabled={(!isValid && !audio) || loading}
        PrimaryType={!audio ? 'submit' : 'button'}
        primaryText={audio || loading ? 'Save' : 'Generate voice'}
        setModalView={setModalView}
        handlClickPrimary={audio && handleSaveAudio}
      />
    </form>
  );
};

export default TextToSpeech;
