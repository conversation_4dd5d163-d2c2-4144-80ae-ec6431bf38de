import 'twin.macro';
/**
 * CustomDayName Component
 *
 * Renders the day name header in the calendar
 *
 * @param {Object} props
 * @param {string} props.label - The day name to display
 */
const CustomDayName = ({ label }) => {
  return (
    <div tw="flex justify-center items-center py-[8px] w-full h-full bg-[#2E2E2E]">
      <span tw="text-[0.78em] text-white font-semibold">{label.slice(0, 1)}</span>
    </div>
  );
};

export default CustomDayName;
