import LabelWithTooltip from '@/components/labelWithTooltip';
import InputNumber from '@/components/shared/inputNumber';
import tw from 'twin.macro';

const PrescriptionFrequencyInputs = ({
  register,
  hasTooltip,
  tooltipText,
  label = 'Frequency',
  labelStyle,
  containerInputStyle,
  inputStye,
}) => {
  return (
    <div tw="w-full h-full">
      <LabelWithTooltip
        label={label}
        tooltipText={tooltipText}
        hasTooltip={hasTooltip}
        labelStyle={labelStyle}
      />
      <div tw="flex gap-2 items-center w-full" css={containerInputStyle}>
        <div tw="w-[50%] h-full">
          <InputNumber
            register={register}
            name={'frequency_day'}
            type="number"
            showArrow={false}
            inputStye={[tw`py-[10px]`, inputStye]}
            placeholder={'a day'}
            max={99}
            containerSTyle={tw`h-full py-[2px] bg-[#EEF0F3]`}
            disabled
            labelStyle={tw`text-[0.8rem]`}
            maxWidth={15}
          />
        </div>
        <div tw="w-[50%]">
          <InputNumber
            register={register}
            name={'frequency_week'}
            type="number"
            showArrow={false}
            inputStye={[tw`py-[10px]`, inputStye]}
            placeholder={'a week'}
            max={99}
            disabled
            containerSTyle={tw`h-full py-[2px] bg-[#EEF0F3]`}
            labelStyle={tw`text-[0.8rem]`}
            maxWidth={15}
          />
        </div>
      </div>
    </div>
  );
};

export default PrescriptionFrequencyInputs;
