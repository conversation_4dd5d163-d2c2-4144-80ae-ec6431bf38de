import React from 'react';
import tw from 'twin.macro';
import SelectorInputCompund from '@/components/selectorInputCompund';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '@tailwind';

const fullConfig = resolveConfig(tailwindConfig);
const { text_secondary } = fullConfig.theme.colors;

export const styles = {
  control: () => ({
    padding: '8px',
    borderRadius: '6px 0 0 6px',
    borderRight: 'none !important',
    '&:hover': {
      borderRight: 'none !important',
    },
  }),
  placeholder: () => ({
    color: text_secondary,
    opacity: '100%',
    textTransform: 'uppercase',
    fontWeight: '700',
  }),
  singleValue: () => ({
    color: text_secondary,
    fontWeight: '700',
    textTransform: 'uppercase',
    width: 'fit-content',
  }),
  input: () => ({
    color: text_secondary,
    fontWeight: '700',
  }),
};

const UnitCompound = ({
  labelText,
  labelTooltipText,
  control,
  selectorName,
  selectorPlaceHolder,
  selectorOptions,
  inputName,
  inputPlaceholder,
  register,
  errors,
  touchedFields,
  customStyles = styles,
}) => {
  return (
    <SelectorInputCompund
      labelProps={{
        label: (
          <label css={tw`flex items-center gap-1`}>
            <span>{labelText}</span>
            <span css={tw`text-info font-light text-sm`}>(recommended)</span>
          </label>
        ),
        hasTooltip: true,
        tooltipText: labelTooltipText,
        toolTipActiveOnHover: true,
        containerStyle: tw`gap-1 mb-0`,
      }}
      selectorProps={{
        control: control,
        name: selectorName,
        placeholder: selectorPlaceHolder,
        options: selectorOptions,
        customStyles,
        inputLength: 3,
        isFixed: true,
      }}
      inputProps={{
        type: 'number',
        showArrow: false,
        name: inputName,
        placeholder: inputPlaceholder,
        register: register,
        rootElementStyle: tw`h-full`,
      }}
      errors={errors}
      touchedFields={touchedFields}
    />
  );
};

export default UnitCompound;
