import React from 'react';
import tw from 'twin.macro';
import ToolTip from '@components/shared/toolTip';
import DeleteIcon from '@assets/svgs/delete-red.svg';

/**
 * Menue component
 *
 * @param {Array} options - Array of option objects to display in the menu.
 * @param {string|number} selected - The currently selected option (by id, label, or value).
 * @param {function} onSelect - Function called with the selected option when an item is clicked.
 * @param {function} setOpen - Function to control the open/close state of the menu.
 * @param {boolean} hasToolTip - If true, shows a tooltip for each option.
 * @param {string|JSX.Element} selectedIcon - Optional icon to display next to the selected option.
 */

const Menue = ({
  options,
  selected,
  onSelect,
  onDeselect,
  setOpen,
  hasToolTip,
  selectedIcon,
  menueStyle,
  menueItemStyle,
}) => {
  return (
    <ul
      css={[
        tw`
              absolute
              left-0
              mt-2
              bg-neutral_100
              border
              border-border_stroke
              z-40
              rounded-[12px]
              w-max
            `,
        {
          boxShadow: `
                0px 5px 15px 0px rgba(0, 0, 0, 0.08),
                0px 15px 35px -5px rgba(17, 24, 38, 0.15),
                0px 0px 0px 1px rgba(152, 161, 178, 0.1)
              `,
        },
        menueStyle,
      ]}
    >
      {options.map(opt => {
        const isSelected = Array.isArray(selected) && selected.includes(opt.id);
        return (
          <li
            key={opt.id}
            className="group"
            css={[
              tw`
                relative
                flex
                items-center
                h-[50px]
                border-b
                border-stroke
                last:border-b-0
                text-[14px]
                font-medium
                px-[16px]
                py-[10px]
                pr-[80px]
                cursor-pointer
                transition
                first:rounded-t-[12px]
                last:rounded-b-[12px]
              `,
              !isSelected && tw`hover:bg-neutral_300`, // Only apply hover bg if not selected
              isSelected && tw`bg-Primary_100 text-Primary_800`,
              menueItemStyle,
            ]}
            onClick={() => {
              if (opt.customClickHandler) {
                opt.customClickHandler(opt);
              } else if (isSelected && onDeselect) {
                onDeselect(opt);
              } else {
                onSelect && onSelect(opt);
                setOpen(false);
              }
            }}
          >
            {opt.label}
            {/* Tooltip, only if hasToolTip is true */}
            {hasToolTip && <ToolTip text={opt.value} />}
            <div css={tw`flex gap-2 absolute right-[16px]`}>
              {isSelected && selectedIcon && (
                <img src={selectedIcon} alt="selected-icon" css={tw`w-[16px]`} />
              )}
            </div>
          </li>
        );
      })}
    </ul>
  );
};

export default Menue;
