import PrimaryButton from '@/components/shared/primaryButton';
import AddNewNoteModal from './modal';
import { useState } from 'react';
import PreviewImage from '@/components/privewImage';
import tw from 'twin.macro';
import 'twin.macro';

function AddNewNote({
  open,
  setOpen,
  noteToUpdate,
  setNoteToUpdate,
  handleAddNewNote,
  handleUpdateNote,
}) {
  const [selectedImage, setSelectedImage] = useState('');
  return (
    <>
      {selectedImage && (
        <PreviewImage imagePreview={selectedImage} setImagePreview={setSelectedImage} />
      )}
      {/* Button Add  */}

      {open && !noteToUpdate && (
        <AddNewNoteModal
          open={open}
          handleClose={() => setOpen(false)}
          handleAddNewNote={handleAddNewNote}
          setSelectedImage={setSelectedImage}
        />
      )}
      {open && noteToUpdate && (
        <AddNewNoteModal
          open={open}
          handleClose={() => {
            setOpen(false);
            setNoteToUpdate(null);
          }}
          handleUpdateNote={handleUpdateNote}
          updateMode={true}
          noteToUpdate={noteToUpdate}
          setSelectedImage={setSelectedImage}
        />
      )}
    </>
  );
}

export default AddNewNote;
