import AccountCreatedImage from '@/assets/svgs/auth/link-expire.svg';
import PrimaryButton from '@/components/shared/primaryButton';
import { useNavigate } from 'react-router-dom';
import 'twin.macro';

const LinkExpire = () => {
  const navigate = useNavigate();
  return (
    <div tw="flex flex-col gap-4 justify-center items-center w-screen h-screen text-center">
      <img src={AccountCreatedImage} alt="account created" />
      <div tw="px-[2%] w-[35%] grid gap-2">
        <h1 tw="font-['Inter'] font-[600] text-[2.1rem]">Your subscription has expired</h1>
        <div tw="mb-6 text-center">
          <span tw="font-['Inter'] font-[400] text-text_secondary [line-height: 130%] text-[1.1rem]">
            Renew your subscription to access Rehabitaire again. Your patients can still access
            their training program.
          </span>
        </div>
        <PrimaryButton
          tw="w-full py-[12px] px-[16px] rounded-[6px] text-[1rem] font-[600] font-['Inter']"
          text={'Continue'}
          type="button"
          handleClick={() => navigate('/login', { replace: true })}
        />
      </div>
    </div>
  );
};

export default LinkExpire;
