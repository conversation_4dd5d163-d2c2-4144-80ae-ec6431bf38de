import React from 'react';
import tw from 'twin.macro';
import Skeleton from '@/components/shared/skeleton';
const VoiceLoader = () => {
  return (
    <div css={tw`flex gap-4 items-center `}>
      <div css={tw`w-[2.5rem] h-[2.5rem]`}>
        <Skeleton skeletonStyle={tw`rounded-full`} />
      </div>
      <div css={tw`h-[0.5rem] flex-1 relative`}>
        <Skeleton skeletonStyle={tw`rounded-full`} />
        <div css={tw`absolute -bottom-[1.4375rem] w-full flex justify-between`}>
          <div css={tw`w-[1.8125rem] h-[1rem] `}>
            <Skeleton />
          </div>
          <div css={tw`w-[1.8125rem] h-[1rem] `}>
            <Skeleton />
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoiceLoader;
