/* eslint-disable react-refresh/only-export-components */
import Calendar from 'react-calendar';
import { useState, useRef, useEffect } from 'react';
import ActionButtons from './actionButton';
import YearsList from './yearList';
import NavigationLabel from './navigationLabel';
import 'twin.macro';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '@tailwind';

const fullConfig = resolveConfig(tailwindConfig);
const { Primary_600, Primary, stroke, neutral_50, text_primary, text_secondary } =
  fullConfig.theme.colors;

// Common styles extracted for reuse
export const styles = {
  primaryColor: Primary_600,
  secondaryColor: Primary,
  borderColor: stroke,
  bgLight: neutral_50,
  textDark: text_primary,
  textLight: '#868685',
  borderRadius: '6px',
  calendarStyles: {
    navigation: {
      backgroundColor: '#fff',
      border: 'none',
      height: 'fit-content',
      paddingBlock: '12px',
      marginBottom: '0px',
      display: 'flex',
      justifyContent: 'center',
      borderBottom: `1px solid ${stroke}`,
    },
    tile: {
      active: {
        backgroundColor: `${Primary_600} !important`,
        borderRadius: '50% !important',
        color: '#fff !important',
        fontWeight: 'bold !important',
      },
    },
  },
};

// Calendar Icon Component
const CalendarIcon = ({ iconColor = text_primary, iconWidth = 20, iconHeight = 20 }) => (
  <svg
    tw="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none"
    width={iconWidth}
    height={iconHeight}
    viewBox={`0 0 ${iconWidth} ${iconHeight}`}
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g opacity="0.5">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.83366 1.4585C5.99942 1.4585 6.15839 1.52434 6.2756 1.64155C6.39281 1.75876 6.45866 1.91774 6.45866 2.0835V2.71933C7.01033 2.7085 7.61783 2.7085 8.28699 2.7085H11.7137C12.3828 2.7085 12.9903 2.7085 13.542 2.71933V2.0835C13.542 1.91774 13.6078 1.75876 13.7251 1.64155C13.8423 1.52434 14.0012 1.4585 14.167 1.4585C14.3328 1.4585 14.4917 1.52434 14.6089 1.64155C14.7261 1.75876 14.792 1.91774 14.792 2.0835V2.77266C15.0087 2.78933 15.2137 2.81016 15.4078 2.836C16.3845 2.96766 17.1753 3.24433 17.7995 3.86766C18.4228 4.49183 18.6995 5.28266 18.8312 6.25933C18.8728 6.57183 18.9012 6.91266 18.9203 7.28433C18.9658 7.40725 18.9714 7.5414 18.9362 7.66766C18.9587 8.33516 18.9587 9.09433 18.9587 9.9535V11.7135C18.9587 13.2452 18.9587 14.4585 18.8312 15.4077C18.6995 16.3843 18.4228 17.1752 17.7995 17.7993C17.1753 18.4227 16.3845 18.6993 15.4078 18.831C14.4578 18.9585 13.2453 18.9585 11.7137 18.9585H8.28699C6.75533 18.9585 5.54199 18.9585 4.59283 18.831C3.61616 18.6993 2.82533 18.4227 2.20116 17.7993C1.57783 17.1752 1.30116 16.3843 1.16949 15.4077C1.04199 14.4577 1.04199 13.2452 1.04199 11.7135V9.9535C1.04199 9.09433 1.04199 8.33516 1.06449 7.66683C1.02972 7.54046 1.03555 7.40637 1.08116 7.2835C1.09949 6.91266 1.12783 6.57183 1.16949 6.25933C1.30116 5.28266 1.57783 4.49183 2.20116 3.86766C2.82533 3.24433 3.61616 2.96766 4.59283 2.836C4.78699 2.81016 4.99283 2.78933 5.20866 2.77266V2.0835C5.20866 1.91774 5.27451 1.75876 5.39172 1.64155C5.50893 1.52434 5.6679 1.4585 5.83366 1.4585ZM2.30283 8.12516C2.29199 8.66933 2.29199 9.2885 2.29199 10.0002V11.6668C2.29199 13.256 2.29366 14.3852 2.40866 15.2418C2.52116 16.0793 2.73283 16.5627 3.08533 16.9152C3.43783 17.2677 3.92116 17.4793 4.75949 17.5918C5.61616 17.7068 6.74449 17.7085 8.33366 17.7085H11.667C13.2562 17.7085 14.3853 17.7068 15.242 17.5918C16.0795 17.4793 16.5628 17.2677 16.9153 16.9152C17.2678 16.5627 17.4795 16.0793 17.592 15.241C17.707 14.3852 17.7087 13.256 17.7087 11.6668V10.0002C17.7087 9.2885 17.7087 8.66933 17.6978 8.12516H2.30283ZM17.6403 6.87516H2.36033C2.37366 6.71683 2.38949 6.56766 2.40866 6.42516C2.52116 5.58766 2.73283 5.10433 3.08533 4.75183C3.43783 4.39933 3.92116 4.18766 4.75949 4.07516C5.61616 3.96016 6.74449 3.9585 8.33366 3.9585H11.667C13.2562 3.9585 14.3853 3.96016 15.242 4.07516C16.0795 4.18766 16.5628 4.39933 16.9153 4.75183C17.2678 5.10433 17.4795 5.58766 17.592 6.426C17.6112 6.56766 17.627 6.71766 17.6403 6.87516ZM13.7503 13.1252C13.5846 13.1252 13.4256 13.191 13.3084 13.3082C13.1912 13.4254 13.1253 13.5844 13.1253 13.7502C13.1253 13.9159 13.1912 14.0749 13.3084 14.1921C13.4256 14.3093 13.5846 14.3752 13.7503 14.3752C13.9161 14.3752 14.0751 14.3093 14.1923 14.1921C14.3095 14.0749 14.3753 13.9159 14.3753 13.7502C14.3753 13.5844 14.3095 13.4254 14.1923 13.3082C14.0751 13.191 13.9161 13.1252 13.7503 13.1252ZM11.8753 13.7502C11.8753 13.5039 11.9238 13.2601 12.0181 13.0326C12.1123 12.8051 12.2504 12.5984 12.4245 12.4243C12.5986 12.2502 12.8053 12.1121 13.0328 12.0179C13.2603 11.9237 13.5041 11.8752 13.7503 11.8752C13.9966 11.8752 14.2404 11.9237 14.4679 12.0179C14.6953 12.1121 14.902 12.2502 15.0762 12.4243C15.2503 12.5984 15.3884 12.8051 15.4826 13.0326C15.5768 13.2601 15.6253 13.5039 15.6253 13.7502C15.6253 14.2474 15.4278 14.7244 15.0762 15.076C14.7245 15.4276 14.2476 15.6252 13.7503 15.6252C13.253 15.6252 12.7761 15.4276 12.4245 15.076C12.0729 14.7244 11.8753 14.2474 11.8753 13.7502Z"
        fill={iconColor}
      />
    </g>
  </svg>
);

// Main DatePicker Component
const DatePicker = ({
  // Base props
  onChange,
  value,
  label,
  disabled,
  disabledStyle,
  // Customization props
  className,
  inputClassName,
  calendarClassName,
  labelClassName,
  primaryColor,
  secondaryColor,
  borderColor,
  borderRadius,
  iconColor,
  selectedDayColor,

  // Behavior props
  showNeighboringMonths = false,
  minDate,
  maxDate,
  disabledDates,
  yearRange = { start: 1900, end: new Date().getFullYear() },

  // Format props
  dateFormat = { year: 'numeric', month: 'short', day: 'numeric' },
  locale = 'en-US',

  // Placeholder & Label Text
  placeholder = 'Select Date',
  applyButtonText = 'Apply',
  cancelButtonText = 'Cancel',

  // Width options
  inputWidth = '100%',
  calendarWidth = '100%',

  // Calendar display settings
  initialView = 'month',
  closeOnSelect = false,

  // Custom handlers
  onCancel,

  // Parent controlled state
  isOpen: isOpenFromProps,
  onOpenChange,
}) => {
  // Compute custom colors for the component
  const customColors = {
    primaryColor: primaryColor || styles.primaryColor,
    secondaryColor: secondaryColor || styles.secondaryColor,
    borderColor: borderColor || styles.borderColor,
    borderRadius: borderRadius || styles.borderRadius,
    selectedDayColor: selectedDayColor || primaryColor || styles.primaryColor,
  };

  // State Management
  const [activeStartDate, setActiveStartDate] = useState(new Date());
  const [view, setView] = useState(initialView);
  const [showYearsList, setShowYearsList] = useState(false);
  // if you want a specific date as the default value, provide the value prop.
  // if you want a place holder like  "dd/mm/yyyy" don't provide a value prop and provide a placeholder
  // if you don't provide anything, the default value automatically set to today's date.
  const [selectedDate, setSelectedDate] = useState(value ? value : placeholder ? '' : new Date());
  const [isCalendarOpen, setIsCalendarOpen] = useState(isOpenFromProps || false);
  const currentDateYear = new Date().getFullYear();

  // Update internal state when controlled from parent
  useEffect(() => {
    if (isOpenFromProps !== undefined) {
      setIsCalendarOpen(isOpenFromProps);
    }
  }, [isOpenFromProps]);

  // Update selected date when value changes
  useEffect(() => {
    if (value) {
      setSelectedDate(value);
    }
  }, [value]);

  // Refs for click-outside detection
  const calendarRef = useRef(null);
  const inputRef = useRef(null);

  // Generate years based on range
  const years = Array.from(
    { length: yearRange.end - yearRange.start + 1 },
    (_, i) => yearRange.start + i
  ).reverse();

  // Format month names
  const formatMonth = (locale, date) => {
    const monthName = date.toLocaleString(locale, { month: 'long' });
    return monthName.substring(0, 3);
  };

  // Handle drill up from month to year view
  const handleDrillUp = () => {
    setView('year');
  };

  // Navigate to a specific year
  const navigateToYear = year => {
    const newDate = new Date(activeStartDate);
    newDate.setFullYear(year);
    setActiveStartDate(newDate);
    setShowYearsList(false);
    setView('year');
  };

  // Handle date change from calendar
  const handleDateChange = date => {
    setSelectedDate(date);

    // If closeOnSelect is true, close the calendar and fire onChange
    if (closeOnSelect && view === 'month') {
      if (onChange) {
        onChange(date);
      }
      setIsCalendarOpen(false);
      if (onOpenChange) {
        onOpenChange(false);
      }
    }
  };

  // Check if year is in the future
  const isYearInFuture = year => {
    return year > currentDateYear;
  };

  // Toggle years list
  const toggleYearsList = () => {
    setShowYearsList(!showYearsList);
  };

  // Toggle calendar visibility
  const toggleCalendar = () => {
    const newIsOpen = !isCalendarOpen;
    setIsCalendarOpen(newIsOpen);

    if (onOpenChange) {
      onOpenChange(newIsOpen);
    }

    if (newIsOpen) {
      setView(initialView);
      setActiveStartDate(
        selectedDate ? new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1) : new Date()
      );
    }
  };

  // Format date for input display
  const formatDateForInput = date => {
    if (!date) return '';
    if (locale === 'en-GB') {
      return date.toLocaleDateString(locale, dateFormat).replace(/ (\d{4})$/, ', $1');
    }
    return date.toLocaleDateString(locale, dateFormat);
  };

  // Handle Apply button
  const handleApply = () => {
    if (showYearsList) {
      setShowYearsList(false);
      return;
    }

    if (view === 'year') {
      setView('month');
      return;
    }

    if (view === 'month') {
      const finalDate = selectedDate || activeStartDate;
      // console.log('Selected date:', finalDate);
      if (onChange) {
        onChange(finalDate);
      }
      setIsCalendarOpen(false); // Close calendar after final selection
      if (onOpenChange) {
        onOpenChange(false);
      }
    }
  };

  // Handle Cancel button
  const handleCancel = () => {
    if (showYearsList) {
      setShowYearsList(false);
      return;
    }

    // For other views, close the calendar without updating the value
    setIsCalendarOpen(false);
    if (onOpenChange) {
      onOpenChange(false);
    }

    // Call custom onCancel if provided
    if (onCancel) {
      onCancel();
    }
    // Reset to initial value
    setSelectedDate(value);
  };

  // Close calendar when clicking outside
  useEffect(() => {
    const handleClickOutside = event => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        inputRef.current &&
        !inputRef.current.contains(event.target)
      ) {
        setSelectedDate(value);
        setIsCalendarOpen(false);
        if (onOpenChange) {
          onOpenChange(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onOpenChange, value]);

  const actionButtonsComponent = (
    <ActionButtons
      onApply={handleApply}
      onCancel={handleCancel}
      applyButtonText={applyButtonText}
      cancelButtonText={cancelButtonText}
      primaryColor={customColors.primaryColor}
      secondaryColor={customColors.secondaryColor}
      borderColor={customColors.borderColor}
    />
  );

  // Calendar styling with custom colors
  const calendarCssStyles = [
    {
      '.react-calendar__navigation': styles.calendarStyles.navigation,
      '.react-calendar__navigation button:hover': {
        backgroundColor: 'transparent',
      },
      '.react-calendar__navigation-label': {
        fontWeight: 'bold',
        color: '#333',
      },
      '.react-calendar__navigation-button': {
        color: '#007bff',
      },
      '.react-calendar__navigation__label__labelText': {
        backgroundColor: 'none',
      },
      '.react-calendar__navigation:hover': {
        backgroundColor: 'none',
      },
      '.react-calendar__navigation-label:hover': {
        backgroundColor: 'none',
        cursor: 'pointer',
      },
      '.react-calendar__navigation-button:hover': {
        backgroundColor: 'none',
      },
      'abbr:where([title])': {
        textDecoration: 'none',
        color: styles.textLight,
        fontSize: '12px',
        opacity: '0.6',
        textTransform: 'capitalize',
      },
      '.react-calendar__navigation__label__labelText:hover': {
        backgroundColor: 'none',
      },
      '.react-calendar__month-view__weekdays': {
        paddingLeft: '20px',
        paddingTop: '10px',
      },
      '.react-calendar__month-view__weekdays__weekday': {
        textTransform: 'capitalize',
        cursor: 'default',
        '& abbr': {
          textTransform: 'capitalize',
        },
      },
      '.react-calendar__month-view': {
        backgroundColor: '#fff !important',
      },
      '.react-calendar__tile': {
        backgroundColor: '#fff !important',
        padding: '0 !important',
        '&:hover': {
          backgroundColor: '#f0f0f0 !important',
        },
      },
      '.react-calendar__month-view__days': {
        padding: '0 10px 10px 10px',
      },
      '.react-calendar__month-view__days__day': {
        color: '#1C1C1C !important',
        height: '45px !important',
        padding: '0 !important',
        transform: 'scale(0.85)',
        fontSize: '15px !important',
      },
      '.react-calendar__month-view__days__day:hover': {
        backgroundColor: customColors.primaryColor + ' !important',
        borderRadius: '50% !important',
        color: '#fff !important',
      },
      '.react-calendar__year-view__months__month': {
        borderBottom: '1px solid ' + customColors.borderColor,
        paddingInline: '10px !important',
        paddingBlock: '14px !important',
      },
      //remove the borderBottom for the last row (3 months per row)
      '.react-calendar__year-view__months__month:nth-last-child(-n+3)': {
        borderBottom: 'none !important',
      },
      '.react-calendar__year-view__months__month:hover': {
        backgroundColor: 'transparent !important',
      },
      '.react-calendar__year-view__months__month abbr:hover': {
        background: customColors.primaryColor,
        color: '#fff',
        paddingInline: '20px',
        paddingBlock: '8px',
        borderRadius: '10px',
        fontWeight: 'bold',
      },
      // Style for the selected day
      '.react-calendar__tile--active, .react-calendar__tile--active:enabled:hover, .react-calendar__tile--active:enabled:focus':
        {
          backgroundColor: customColors.selectedDayColor + ' !important',
          borderRadius: '50% !important',
          color: '#fff !important',
          fontWeight: 'bold !important',
        },
      // Style for today
      '.react-calendar__tile--now abbr': {
        // Custom styles for today can be added here
      },
      // Style for the selected day when it is also today
      '.react-calendar__tile--now.react-calendar__tile--active': {
        backgroundColor: customColors.selectedDayColor + ' !important',
        color: '#fff !important',
        border: '2px solid ' + customColors.selectedDayColor + ' !important',
      },
      '.react-calendar__tile:disabled': {
        color: `${text_secondary} !important`,
        cursor: 'default !important',
        opacity: 0.5,
      },
      '.react-calendar__tile:disabled:hover': {
        backgroundColor: 'transparent !important',
      },
      // style for disabled months in year view
      '.react-calendar__tile:disabled abbr': {
        backgroundColor: 'transparent !important',
        color: `${text_secondary} !important`,
        opacity: 0.5,
        fontWeight: 'normal !important',
      },
    },
  ];

  return (
    <div tw="relative" className={className}>
      {label && (
        <label tw="block text-[14px] font-medium text-gray-700 mb-1" className={labelClassName}>
          {label}
        </label>
      )}

      <div tw="relative">
        <input
          ref={inputRef}
          type="text"
          tw="w-full py-[10px] px-[12px] border rounded-[6px] cursor-pointer disabled:bg-disable disabled:cursor-not-allowed "
          css={{
            borderColor: customColors.borderColor,
            borderRadius: customColors.borderRadius,
            width: inputWidth,
          }}
          className={inputClassName}
          placeholder={placeholder}
          value={formatDateForInput(selectedDate)}
          readOnly
          onClick={toggleCalendar}
          disabled={disabled}
        />
        <CalendarIcon iconColor={iconColor} />
      </div>

      {isCalendarOpen && (
        <div
          ref={calendarRef}
          tw="absolute z-[100] mt-1"
          style={{ width: calendarWidth }}
          className={calendarClassName}
        >
          <div
            tw="w-full [box-shadow: 0 4px 6.8px 0 #0000000d] border rounded-[12px] overflow-hidden border-text_secondary bg-white relative"
            css={{ borderColor: customColors.borderColor }}
          >
            {showYearsList && (
              <YearsList
                years={years}
                activeStartDate={activeStartDate}
                navigateToYear={navigateToYear}
                setShowYearsList={setShowYearsList}
                selectedDate={selectedDate}
                actionButtons={actionButtonsComponent}
                primaryColor={customColors.primaryColor}
                borderColor={customColors.borderColor}
              />
            )}

            {!showYearsList && (
              <Calendar
                onChange={handleDateChange}
                value={selectedDate}
                prevLabel={null}
                nextLabel={null}
                prev2Label={null}
                next2Label={null}
                showNeighboringMonth={showNeighboringMonths}
                view={view}
                activeStartDate={activeStartDate}
                minDate={minDate}
                maxDate={maxDate}
                tileDisabled={({ date }) => {
                  // Disable if in disabledDates array
                  disabledDates
                    ? disabledDates.some(
                        disabledDate =>
                          date.getFullYear() === disabledDate.getFullYear() &&
                          date.getMonth() === disabledDate.getMonth() &&
                          date.getDate() === disabledDate.getDate()
                      )
                    : false;
                }}
                onActiveStartDateChange={({ activeStartDate, view }) => {
                  setActiveStartDate(activeStartDate);
                  setView(view);

                  if (view === 'month') {
                    const newDate = new Date(selectedDate);
                    newDate.setFullYear(activeStartDate.getFullYear());
                    newDate.setMonth(activeStartDate.getMonth());
                    setSelectedDate(newDate);
                  } else if (view === 'year') {
                    const newDate = new Date(selectedDate);
                    newDate.setFullYear(activeStartDate.getFullYear());
                    setSelectedDate(newDate);
                  }
                }}
                onDrillUp={handleDrillUp}
                onDrillDown={({ activeStartDate, view }) => {
                  setActiveStartDate(activeStartDate);
                  setView(view);

                  if (view === 'month') {
                    const newDate = new Date(selectedDate);
                    newDate.setFullYear(activeStartDate.getFullYear());
                    newDate.setMonth(activeStartDate.getMonth());
                    setSelectedDate(newDate);
                  }
                }}
                formatMonth={formatMonth}
                navigationLabel={({ date, locale, view }) => (
                  <NavigationLabel
                    years={years}
                    date={date}
                    locale={locale}
                    view={view}
                    setView={setView}
                    navigateToYear={navigateToYear}
                    toggleYearsList={toggleYearsList}
                    isYearInFuture={isYearInFuture}
                    primaryColor={customColors.primaryColor}
                    textDark={styles.textDark}
                  />
                )}
                css={calendarCssStyles}
              />
            )}
            {!showYearsList && actionButtonsComponent}
          </div>
        </div>
      )}
    </div>
  );
};

export default DatePicker;
