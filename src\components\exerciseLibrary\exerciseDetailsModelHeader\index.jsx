import Close from '@assets/svgs/close-icon.svg';
import Image from '@assets/svgs/auth/arrow-down.svg';
import 'twin.macro';

// ! header
const ExerciseDetailsModelHeader = ({
  title,
  handelCloseClick,
  closeIconSrc,
  handelCLiconTitle,
  hideArrow,
  hideCloseIcon,
  customTitle,
  containerStyle,
  titleStyle,
}) => {
  return (
    <div
      tw="p-[20px] relative border-b flex justify-between items-center border-[#D9D9D9]"
      css={containerStyle}
    >
      <div tw="flex gap-1 items-center" onClick={handelCLiconTitle}>
        {!hideArrow && <img src={Image} alt="arrow" tw="rotate-90 cursor-pointer h-[20px]" />}
        {customTitle ? (
          customTitle
        ) : (
          <span tw="text-[1.2rem] cursor-pointer" css={titleStyle}>
            {title}
          </span>
        )}
      </div>
      {!hideCloseIcon && (
        <img
          src={closeIconSrc || Close}
          alt="close"
          tw="absolute top-8 right-8 cursor-pointer"
          onClick={handelCloseClick}
        />
      )}
    </div>
  );
};

export default ExerciseDetailsModelHeader;
