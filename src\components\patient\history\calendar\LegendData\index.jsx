import { legend_data } from '../../../constant';
import tw, { css } from 'twin.macro';
const LegendData = () => {
  return (
    <div tw="rounded-md border border-border_stroke">
      <h3 tw="px-4 py-3 text-base font-bold text-black border-b border-border_stroke">Legend</h3>
      <div tw="grid grid-cols-2 gap-4 p-4">
        {legend_data.map(item => (
          <div tw="flex items-center gap-1.5">
            <div
              css={[
                tw`flex justify-center items-center`,
                item.color && tw`w-5 h-5 rounded-full border `,
                css`
                  border-color: ${item.color};
                `,
              ]}
            >
              <img src={item.icon} />
            </div>
            <p tw="text-[.625rem] font-medium text-text_primary">{item.text}</p>
          </div>
        ))}
      </div>
    </div>
  );
};
export default LegendData;
