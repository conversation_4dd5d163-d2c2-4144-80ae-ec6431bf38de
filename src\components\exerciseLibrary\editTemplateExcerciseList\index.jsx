import ExerciseCard from '@/components/exerciseCard';
import useScrollSwipe from '@/hooks/exercises-library/useScrollBySwipe';
import { isExistInsideArray, removeIfExists, toggleItemInArray } from '@/utils/helpers';
import EditTemplateContainerSections from '../editTemplateContainerSections';
import Delete from '@/components/exerciseCard/buttons/delete';
import tw from 'twin.macro';

const EditTemplateExerciseList = ({
  exerciseListTemplate,
  setSelectedExercise,
  setExerciseListTemplate,
  selectedExercise,
}) => {
  const { scrollRef } = useScrollSwipe();
  const handelAddToArray = item => {
    const items = toggleItemInArray(selectedExercise, item);
    setSelectedExercise(items);
  };

  const handleDelete = item => {
    const restItems = removeIfExists(exerciseListTemplate, item);
    setExerciseListTemplate(restItems);
  };
  return (
    <EditTemplateContainerSections customStyle={tw`pt-0`}>
      <p tw="text-[1.1rem] my-[10px] font-medium">Exercise List</p>
      <ul
        className="custom-scroll"
        ref={scrollRef}
        tw="flex overflow-x-auto gap-5 items-center pb-4"
      >
        {exerciseListTemplate.length === 0 && (
          <div tw="h-[170px] flex text-center items-center justify-center w-full">
            No Exercises in this template to show
          </div>
        )}
        {exerciseListTemplate.map((item, index) => (
          <div tw="w-[155px] flex-shrink-0" key={item.id}>
            <ExerciseCard
              hasShadowOnHover
              imageUrl={item.imgUrl}
              videoUrl={item.videoUrl}
              tagText={`Exercise  ${index + 1}`}
              showActions={true}
              title={item.title}
              handelCardClick={() => handelAddToArray(item)}
              containerStyle={[
                isExistInsideArray(selectedExercise, item) ? tw`border-2 border-Primary_600` : tw``,
                tw`transition-all duration-200 ease-in-out`,
              ]}
              actionsList={
                [
                  // <Delete
                  //   key={1}
                  //   handleClick={e => {
                  //     handleDelete(item);
                  //   }}
                  // />,
                ]
              }
              params={{ sets: 4, reps: 4, readOnly: true }}
            />
          </div>
        ))}
      </ul>
    </EditTemplateContainerSections>
  );
};

export default EditTemplateExerciseList;
