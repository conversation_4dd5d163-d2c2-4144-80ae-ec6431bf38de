import TabsButton from '@/components/shared/tab-button';
import 'twin.macro';

/**
 *
 * @param {Array} prop.tabsData - List of tabs containing id, title, and icon properties
 * tabsData has form of [{id:number , title: string, component: ReactNode}]
 * @param {Function} prop.handelSelected - Function for handling tab selection
 * @param {Number} prop.selectedTab - Index of the currently selected tab
 */

const Tabs = ({ tabsData, handelSelected, selectedTab }) => {
  return (
    <div>
      <ul tw="flex gap-4 items-center">
        {tabsData.map(item => (
          <TabsButton
            key={item.id}
            title={item.title}
            onClick={() => handelSelected(item)}
            isSelected={selectedTab === item.id}
          />
        ))}
      </ul>
    </div>
  );
};

export default Tabs;
