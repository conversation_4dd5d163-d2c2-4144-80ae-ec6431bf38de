import LabelWithTooltip from '@/components/labelWithTooltip';
import Menu from '@/components/menu';
import Input from '@/components/shared/input';
import InputNumber from '@/components/shared/inputNumber';
import PrimaryButton from '@/components/shared/primaryButton';
import SecondaryButton from '@/components/shared/secondaryButton';
import { showSuccess } from '@/libs/react.toastify';
import { exerciseLibraryActions } from '@/reducers/exercise-library';
import { useExerciseLibrary } from '@/zustand/exercise-library';
import { useState, useRef } from 'react';
import tw from 'twin.macro';

const PrescriptionForm = ({
  register,
  selectedCard,
  options,
  state,
  touchedFields,
  watch,
  dispatch,
  errors,
  handleOpenPrescriptionModal,
  resetPrescriptionForm,
}) => {
  //console.log('PrescriptionForm rendered', errors);
  const [openMenu, setOpenMenu] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const buttonRef = useRef(null);
  const timeoutRef = useRef();
  const { setEditTemplateData, EditTemplateData } = useExerciseLibrary();
  const prescriptionName = watch('prescription_name');

  const handleMouseEnter = () => {
    clearTimeout(timeoutRef.current);
    setShowMenu(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setShowMenu(false);
    }, 150);
  };

  const handelSave = () => {
    setOpenMenu(false);
    dispatch({ type: exerciseLibraryActions.openSaveTemplateModal, payload: true });
  };
  const handelSaveAsCopy = () => {
    setOpenMenu(false);
    showSuccess('save as copy');
    dispatch({ type: exerciseLibraryActions.openSaveTemplateModal, payload: true });
  };

  const handelDiscardChanges = () => {
    dispatch({ type: exerciseLibraryActions.activePrescriptionTab, payload: 1 });
    dispatch({ type: exerciseLibraryActions.resetPrescription });
    dispatch({ type: exerciseLibraryActions.resetTemplatesIds });
    setEditTemplateData([]);
    resetPrescriptionForm();
  };

  return (
    <form tw="flex flex-col gap-3">
      <div tw="bg-neutral_50 h-[95%] flex flex-col gap-2 p-[14px] border rounded-card border-border_stroke">
        <div tw="w-full">
          <LabelWithTooltip
            labelStyle={tw`text-[0.8rem]`}
            label="Frequency"
            tooltipText="sadas"
            hasTooltip
          />
          <div tw="flex gap-2 items-start mt-2 w-full">
            <div tw="w-[50%]">
              <InputNumber
                register={register}
                name={'frequency_day'}
                type="number"
                showArrow={false}
                inputStye={tw`py-[6px]`}
                placeholder={'a day'}
                max={12}
                containerSTyle={tw`px-2`}
                labelStyle={tw`text-[0.8rem]`}
                maxWidth={15}
                hideErrorMessage
                errorMessage={errors?.frequency_day?.message}
              />
              {errors?.frequency_day && (
                <div tw="mt-1 text-xs text-red-500">{errors.frequency_day.message}</div>
              )}
            </div>
            <div tw="w-[50%]">
              <InputNumber
                register={register}
                name={'frequency_week'}
                type="number"
                showArrow={false}
                inputStye={tw`py-[6px]`}
                placeholder={'a week'}
                containerSTyle={tw`px-2`}
                max={12}
                hideErrorMessage
                labelStyle={tw`text-[0.8rem]`}
                maxWidth={15}
                errorMessage={errors?.frequency_week?.message}
              />
              {errors?.frequency_week && (
                <div tw="mt-1 text-xs text-red-500">{errors.frequency_week.message}</div>
              )}
            </div>
          </div>
        </div>
        {state.prescriptionTabArray.length === 0 ? (
          <div>
            <InputNumber
              register={register}
              name={'duration'}
              type="number"
              showArrow={false}
              placeholder={'weeks'}
              label={'Duration'}
              inputStye={tw`py-[6px]`}
              hasTooltip
              max={999}
              maxWidth={22}
              errorMessage={errors?.duration?.message}
              labelStyle={tw`text-[0.8rem]`}
              containerSTyle={tw`text-[0.8rem] basis-[50%] placeholder:text-[0.8rem]`}
            />
          </div>
        ) : (
          <div>
            <InputNumber
              register={register}
              name={'duration'}
              type="number"
              showArrow={false}
              placeholder={'sad'}
              label={'Duration'}
              inputStye={tw`py-[6px]`}
              hasTooltip
              max={999}
              maxWidth={22}
              labelStyle={tw`text-[0.8rem]`}
              containerSTyle={tw`text-[0.4rem] h-full basis-[50%] placeholder:text-[0.4rem]`}
            />
            {errors?.duration && (
              <div tw="mt-1 text-xs text-red-500">{errors.duration.message}</div>
            )}
          </div>
        )}

        <div>
          <Input
            register={register}
            name="prescription_name"
            placeholder={'Prescription name'}
            label={'Prescription name'}
            inputStye={tw`py-[6px] placeholder:text-[0.7rem]`}
            containerSTyle={tw`bg-white`}
            labelStyle={tw`text-[0.7rem]`}
          />
          {errors?.prescription_name && (
            <div tw="mt-1 text-xs text-red-500">{errors.prescription_name.message}</div>
          )}
        </div>
      </div>
      <div tw="flex relative h-[15%] gap-4 items-center">
        <>
          <PrimaryButton
            customStyle={tw`rounded-[5px] text-[0.7rem] p-[5px] h-full w-[80%]`}
            text={'Assign prescription'}
            disable={
              selectedCard.length === 0 || Object.keys(errors).length > 0 || !prescriptionName
            }
            type="button"
            onClick={handleOpenPrescriptionModal}
          />
          <Menu
            disable={selectedCard.length === 0}
            handelClose={() => setOpenMenu(false)}
            handleOpenMenu={() => setOpenMenu(true)}
            openMenu={openMenu}
            options={options}
          />
        </>

        {/* {state.prescriptionTabArray.length === 0 ? (
         
        ) : (
          <>
            <div
              tw="relative basis-[50%]"
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <PrimaryButton
                ref={buttonRef}
                customStyle={tw`w-full py-[10px]`}
                text={'Save'}
                type="button"
                disable={EditTemplateData.length === 0}
                handleClick={() => console.log('save')}
              />
              {showMenu && (
                <div tw="absolute cursor-pointer text-center font-semibold text-[0.8rem] rounded-[6px] top-[110%] left-0 bg-neutral_100 border border-border_stroke [box-shadow: 0px 5px 15px 0px #00000014, 0px 15px 35px -5px #11182626,0px 0px 0px 1px #98A1B21A] w-[120%]">
                  <div
                    tw="py-[14px] px-[14px] hover:bg-neutral_300 duration-500 transition-all"
                    onClick={handelSave}
                    onMouseDown={e => e.preventDefault()}
                  >
                    Save
                  </div>
                  <div
                    tw="py-[14px] px-[14px] border-t border-border_stroke hover:bg-neutral_300 duration-500 transition-all"
                    onClick={handelSaveAsCopy}
                    onMouseDown={e => e.preventDefault()}
                  >
                    Save as copy
                  </div>
                </div>
              )}
            </div>
            <SecondaryButton
              text="Discard Edits"
              otherStyle={tw`basis-[50%] py-0 text-[0.8rem] h-full`}
              handelClick={handelDiscardChanges}
            />
          </>
        )} */}
      </div>
    </form>
  );
};

export default PrescriptionForm;
