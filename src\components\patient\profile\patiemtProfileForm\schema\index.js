import * as Yup from 'yup';

export const patientValidationSchema = Yup.object().shape({
  image_file: Yup.mixed().notRequired().nullable(),

  patient_name: Yup.string()
    .required('Required field')
    .min(2, 'Invalid Name')
    .max(100, 'Invalid Name')
    .test(
      'is-valid-first-name',
      'Enter a valid name (letters only).',
      value => !value || /^[\u0600-\u06FFa-zA-Z]+(?: [\u0600-\u06FFa-zA-Z]+){0,2}$/.test(value)
    ),
  assigned_pt: Yup.string().required('Required field.'),

  email: Yup.string()
    .email('Invalid email.')
    .matches(/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i, 'Invalid email.')
    .max(255, 'Maximum 255 characters are allowed.')
    .nullable()
    .notRequired(),

  country_code: Yup.string()
    .matches(/^\+\d{1,4}$/, 'Invalid country code')
    .required('Required field.'),

  phoneNumber: Yup.string()
    .matches(/^\d{6,15}$/, 'Invalid phone number')
    .required('Required field.'),

  birthdate: Yup.date()
    .max(new Date(), 'Birthdate cannot be in the future')
    .required('Required field.'),

  gender: Yup.object()
    .shape({
      label: Yup.string().required('Required field.'),
      value: Yup.string().required('Required field.'),
    })
    .required('Required field.'),

  heightUnit: Yup.object()
    .shape({
      label: Yup.string().required(),
      value: Yup.string().required(),
    })
    .required('Required field.'),
  height: Yup.number()
    .typeError('Invalid height.')
    .required('Required field.')
    .positive('Invalid height.')
    .when('heightUnit.value', {
      is: unit => unit === 'ft',
      then: schema => schema.max(9, 'Invalid height.'),
      otherwise: schema =>
        schema.when('heightUnit.value', {
          is: unit => unit === 'in',
          then: schema => schema.max(100, 'Invalid height.'),
          otherwise: schema =>
            schema.when('heightUnit.value', {
              is: unit => unit === 'm',
              then: schema => schema.max(2.5, 'Invalid height.'),
              otherwise: schema =>
                schema.when('heightUnit.value', {
                  is: unit => unit === 'cm',
                  then: schema => schema.max(250, 'Invalid height.'),
                  otherwise: schema => schema, // fallback, no max
                }),
            }),
        }),
    }),
  weightUnit: Yup.object()
    .shape({
      label: Yup.string().required(),
      value: Yup.string().required(),
    })
    .required('Required field.'),
  weight: Yup.number()
    .typeError('Invalid weight.')
    .required('Required field.')
    .positive('Invalid weight.')
    .when('weightUnit.value', {
      is: unit => unit === 'kg',
      then: schema => schema.max(500, 'Invalid weight.'),
      otherwise: schema =>
        schema.when('weightUnit.value', {
          is: unit => unit === 'g',
          then: schema => schema.max(500000, 'Invalid weight.'),
          otherwise: schema =>
            schema.when('weightUnit.value', {
              is: unit => unit === 'lb',
              then: schema => schema.max(1100, 'Invalid weight.'),
              otherwise: schema => schema, // fallback, no max
            }),
        }),
    }),

  medical_conditions: Yup.string().notRequired(),

  pain_areas: Yup.string().nullable().notRequired(),
});
