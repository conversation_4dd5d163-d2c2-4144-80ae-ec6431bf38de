import "twin.macro";
import { styles } from "./datePicker";

// Button components
const SecondaryButton = ({
  children,
  onClick,
  borderColor = styles.borderColor,
  bgLight = styles.bgLight,
}) => (
  <button
    tw="px-[10px] py-[8px] rounded-[6px] border flex-1"
    css={{
      borderColor: borderColor,
      backgroundColor: bgLight,
    }}
    onClick={onClick}
  >
    {children}
  </button>
);

export default SecondaryButton;
