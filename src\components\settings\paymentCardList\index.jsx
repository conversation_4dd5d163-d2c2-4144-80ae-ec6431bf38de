import PaymentCard from '../paymentCard';
import 'twin.macro';

const PaymentCardList = ({ cards, title, clickOnDeleteIcon, handelSetAsDefault }) => {
  return (
    <div tw="my-[24px]">
      <p tw="text-[1.125rem] font-semibold">{title}</p>
      <div tw="py-[24px] border-b border-border_stroke grid gap-[16px]">
        {cards.map(item => (
          <PaymentCard
            date={item.expire}
            imageUrl={item.imageUrl}
            title={item.title}
            isExpire={item.isExpires}
            isDefault={item.isDefault}
            key={item.id}
            id={item.id}
            handelSetAsDefault={handelSetAsDefault}
            clickOnDeleteIcon={clickOnDeleteIcon}
          />
        ))}
      </div>
    </div>
  );
};

export default PaymentCardList;
