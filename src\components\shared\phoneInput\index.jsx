import { Controller } from 'react-hook-form';
import PhoneInput<PERSON><PERSON>, { getCountries, getCountryCallingCode } from 'react-phone-number-input';
import tw, { css } from 'twin.macro';
import Arrow from '@assets/svgs/auth/arrow-down.svg';
import { useState, useRef, useEffect } from 'react';
import ReactCountryFlag from 'react-country-flag';
import i18n from 'i18n-iso-countries';
import en from 'i18n-iso-countries/langs/en.json';
import { FixedSizeList as List } from 'react-window';
import 'react-phone-number-input/style.css';
// Initialize i18n
i18n.registerLocale(en);

/**
 * Custom dropdown arrow icon component
 */
const CustomArrowIcon = ({ isOpen }) => (
  <div>
    <img
      src={Arrow}
      alt="arrow"
      tw="max-w-[40px]"
      css={isOpen ? tw`rotate-180 transition-all duration-200` : tw``}
    />
  </div>
);

/**
 * PhoneInput Component
 *
 * A controlled phone number input component that integrates with react-hook-form.
 * The component allows opening the country select dropdown by clicking anywhere on the component.
 * The dropdown automatically closes when:
 * - User clicks outside the component
 * - User selects a country
 * - Component loses focus (blur event)
 * - User presses the Escape key
 *
 * @param {Object} props Component props
 * @param {Object} props.control React Hook Form control object
 * @param {string} props.name Field name to register with react-hook-form
 * @param {string} props.defaultCountry Default country code (ISO 3166-1 alpha-2)
 * @param {Object} props.customStyle Custom CSS styles to apply to the input
 * @param {boolean} props.showCodeBeforeFlag Whether to show the country code before the flag (default: true)
 * @param {string} props.defaultCountryCodeNumber Default country code number (default: "+963")
 * @param {string} props.phoneNumberFontSize Font size for the displayed phone number (default: "14px")
 * @param {errorMessage} props.errorMessage error message
 *
 * @returns {React.Element} Phone input component
 *
 * @example
 * // Basic usage with react-hook-form
 * const { control } = useForm();
 *
 * <PhoneInput
 *   control={control}
 *   name="phoneNumber"
 *   defaultCountry="PH"
 *   showCodeBeforeFlag={true}
 *   phoneNumberFontSize="20px"
 * />
 */
const PhoneInput = ({
  control,
  name,
  defaultCountry = 'EG',
  customStyle,
  showCodeBeforeFlag = true,
  phoneNumberFontSize = '12px',
  disabled,
  disabledStyle,
  errorMessage,
  containerStyle,
  setIsOpenMenu,
  isOpenMenu,
}) => {
  const [phoneNumber, setPhoneNumber] = useState(defaultCountry);
  const phoneInputRef = useRef(null);

  const countries = getCountries();

  // Add handlers to close the dropdown
  useEffect(() => {
    // Handle clicks outside the component
    const handleClickOutside = event => {
      if (phoneInputRef.current && !phoneInputRef.current.contains(event.target)) {
        setIsOpenMenu(false);
      }
    };

    // Handle keyboard events (Escape key)
    const handleKeyDown = event => {
      if (event.key === 'Escape') {
        setIsOpenMenu(false);
      }
    };

    // Add event listeners
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    // Clean up the event listeners on component unmount
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // Custom styles for the phone input to match the design
  const phoneInputStyles = css`
    &.phoneNumber p {
      font-size: 14px;
    }
    .PhoneInput--disabled {
      background: #ff0000;
      cursor: not-allowed;
    }
    .PhoneInputCountry {
      background: transparent;
      cursor: pointer;
      position: relative;
      z-index: 1;
    }

    .PhoneInputCountryIcon {
      width: 15px;
      height: auto;
    }

    /* Make the select more accessible for our custom click handler */
    .PhoneInputCountrySelect {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      z-index: 2;
      opacity: 0;
      cursor: pointer;
    }

    /* Hide default arrow */
    .PhoneInputCountrySelectArrow {
      display: none;
    }

    input {
      display: none;
    }
  `;
  const [searchValue, setSearchValue] = useState('');

  const filteredCountries = countries.filter(country => {
    const name = i18n.getName(country, 'en');
    return name?.toLowerCase().includes(searchValue.toLowerCase());
  });

  return (
    <div
      tw="flex items-center border-e border-border_stroke w-full h-full px-[12px] cursor-pointer relative"
      css={[containerStyle, disabled && disabledStyle]}
      onClick={() => {
        if (!disabled) setIsOpenMenu(true);
      }}
      ref={phoneInputRef}
    >
      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, onBlur, ref } }) => (
          <PhoneInputLib
            ref={ref}
            onBlur={() => {
              onBlur();
              setTimeout(() => {
                setIsOpenMenu(false);
              }, 200);
            }}
            defaultCountry={phoneNumber}
            className="phoneNumber"
            international
            disabled
            css={[phoneInputStyles, customStyle]}
            countryCallingCodeEditable={false}
            onCountryChange={e => {
              onChange(getCountryCallingCode(e));
              setPhoneNumber(e);
            }}
            countryCallingCodePosition={showCodeBeforeFlag ? 'before' : 'after'}
          />
        )}
      />

      {/* 🧾 Virtualized Country List */}
      <div
        tw="[box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px] rounded-[6px] z-20 bg-white overflow-hidden w-[220px] absolute top-10 left-0"
        css={[
          isOpenMenu ? tw`block` : tw`hidden`,
          css`
            ul li {
              padding: 12px;
              display: flex;
              gap: 12px;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s;
              border-bottom: 1px solid #d9dfe4;
            }
            ul li:hover {
              background-color: #f00;
            }
          `,
        ]}
      >
        {/* 🔍 Search Input */}
        <div tw="p-2">
          <input
            type="text"
            placeholder="Search country..."
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
            tw="w-full px-3 py-2 border border-border_stroke rounded-[6px] text-[13px] focus:outline-none focus:ring-2 focus:ring-Primary"
          />
        </div>

        {/* Virtualized List */}
        <List height={235} itemCount={filteredCountries.length} itemSize={40} width={220}>
          {({ index, style }) => {
            const country = filteredCountries[index];
            return (
              <div
                key={country}
                style={style}
                className="country-item"
                tw="px-3 py-2 cursor-pointer flex items-center gap-2 text-[13px] border-b border-stroke"
                onClick={e => {
                  e.stopPropagation();
                  setIsOpenMenu(false);
                  setPhoneNumber(country);
                  setSearchValue('');
                }}
              >
                <ReactCountryFlag countryCode={country} svg />
                <span>{i18n.getName(country, 'en')}</span>
              </div>
            );
          }}
        </List>
      </div>

      <div tw="flex gap-2 items-center">
        <p tw="text-text_primary" style={{ fontSize: phoneNumberFontSize, marginInlineStart: 2 }}>
          +{getCountryCallingCode(phoneNumber)}
        </p>
        <CustomArrowIcon isOpen={isOpenMenu} />
      </div>
    </div>
  );
};

export default PhoneInput;
