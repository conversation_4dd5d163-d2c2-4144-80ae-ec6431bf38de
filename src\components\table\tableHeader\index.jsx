import React, { useState } from 'react';
import tw from 'twin.macro';
import DropDown from '@/components/shared/dropDown';
import Input from '@/components/shared/input';
import NormalModal from '@/components/shared/normalModal';
import GenericSelect from '@/components/shared/select';
import TableSettings from './tableSettings';
import FilterIcon from '@assets/svgs/filter.svg';
import SearchIcon from '@assets/svgs/search.svg';
import TableSettingIcon from '@assets/svgs/table-settings-icon.svg';

import { components } from 'react-select';
import ToolTip from '@/components/shared/toolTip';

/**
 * TableHeader component
 *
 * Renders the table header section with filter, search, actions, and settings controls.
 * Allows filtering, searching, performing actions on selected rows, and opening the table settings modal.
 *
 * @param {Object} props - The component props.
 * @param {string} props.filterValue - The current filter value.
 * @param {Function} props.setFilterValue - Function to update the filter value.
 * @param {Array<number>} props.selectedRows - Array of currently selected row IDs.
 * @param {Object} props.register - React Hook Form register function for the search input.
 * @param {Object} props.settings - Table settings (e.g., reorderable columns, date format).
 * @param {Function} props.onChangeSettings - Function to update the table settings.
 */

const TableHeader = ({
  selectedRows,
  control,
  register,
  settings,
  onChangeSettings,
  filterOptions,
  actionOptions,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const CustomSingleValue = ({ children, ...props }) => {
    return (
      <components.SingleValue {...props}>
        <div css={tw`flex gap-[6px] text-[14px] font-medium`}>
          <img src={FilterIcon} alt="filter-icon" key="filter" css={tw`w-5 h-5`} />
          {children}
        </div>
      </components.SingleValue>
    );
  };

  const CustomOption = props => {
    return (
      <div css={[tw`relative`]} className="group">
        <components.Option {...props}>{props.children}</components.Option>
        {<ToolTip text={props.data.value} />}
      </div>
    );
  };

  const actionItems = ['Actions'];

  return (
    <div tw="flex justify-between pt-0 pr-4 pb-4 pl-4">
      <div tw="flex">
        {selectedRows.length > 0 && (
          <div tw="flex gap-3 items-center mr-4">
            <span tw="text-[14px] font-medium">{selectedRows.length} selected</span>
            <DropDown
              items={actionItems}
              dropCss={tw`border-opacity-100 rounded-[6px] shadow-none py-[10px] px-[14px] gap-[12px]`}
              options={actionOptions}
              hasArrow
            />
          </div>
        )}
        <div tw="flex">
          <GenericSelect
            control={control}
            name="filter"
            options={filterOptions}
            customStyles={{
              control: () => ({
                padding: '4px 6px',
                cursor: 'pointer',
                borderRadius: '6px 0 0 6px',
                borderRight: 'none !important',
                '&:hover': {
                  borderRight: 'none !important',
                },
              }),
              singleValue: () => ({
                margin: '0px',
              }),
              menu: () => ({
                width: 'fit-content',
                zIndex: '40',
              }),
              menuList: () => ({
                maxHeight: 'fit-content',
                width: '250px',
                overflow: 'visible',
              }),
              option: () => ({
                fontWeight: '500',
              }),
            }}
            inputLength={10}
            customComponents={{ SingleValue: CustomSingleValue, Option: CustomOption }}
            filterOption={(option, inputValue) => {
              return option.label.toLowerCase().includes(inputValue.toLowerCase());
            }}
          />
          <div tw="flex relative">
            <Input
              placeholder="Search"
              defaultValue=""
              register={register}
              name="search"
              type="string"
              firstIcon={<img src={SearchIcon} alt="search" css={tw`ml-4`} />}
              containerSTyle={tw`h-full rounded-s-none border border-border_stroke`}
              inputStye={tw`h-full
              py-[6px] px-[12px] text-[14px]
              rounded-e-[6px] pr-[36px] w-[320px] 
              focus:outline-none border-l-0
              hover:cursor-pointer focus:cursor-text
            `}
            />
          </div>
        </div>
      </div>
      <button onClick={() => setModalOpen(true)}>
        <img src={TableSettingIcon} alt="table-settings" tw="w-[34px] h-[34px]" />
      </button>
      {modalOpen && (
        <NormalModal
          open={modalOpen}
          handleClose={() => setModalOpen(false)}
          title="Table settings"
          titleStyle={tw`!pb-4 px-6`}
          content={
            <TableSettings
              initialItems={settings.reorderableColumns}
              initialDateFormat={settings.dateFormat}
              onApply={newSettings => {
                onChangeSettings(newSettings);
                setModalOpen(false);
              }}
            />
          }
          containerModalStyle={tw`max-w-fit  !min-w-0 md:!min-w-0 h-auto !rounded-[12px] pt-[16px] pb-[24px] shadow-[0px_5px_15px_0px_rgba(0,0,0,0.08),0px_15px_35px_-5px_rgba(17,24,38,0.2),0px_0px_0px_1px_rgba(152,161,178,0.1)] overflow-hidden`}
        />
      )}
    </div>
  );
};

export default TableHeader;
