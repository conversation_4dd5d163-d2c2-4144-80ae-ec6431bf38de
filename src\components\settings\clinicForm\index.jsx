import Input from '@/components/shared/input';
import GenericSelect from '@/components/shared/select';
import PhoneNumber from '@/components/phoneNumber';
import tw from 'twin.macro';

const CLinicForm = ({ errors, control, register }) => {
  const inputData = [
    { id: 0, title: 'Name', placeholder: 'Name', name: 'name', error: errors?.name?.message },
    // { id: 1, title: 'Email', placeholder: 'email', name: 'email', error: errors?.email?.message },
  ];

  const selectData = [
    {
      id: 0,
      label: 'Country',
      options: [{}],
      name: 'country',
      placeholder: 'Country',
      error: errors?.country?.message,
    },
    {
      id: 1,
      label: 'City',
      options: [{}],
      name: 'city',
      placeholder: 'City',
      error: errors?.city?.message,
    },
  ];

  const ContainerSectionStyle = tw`flex gap-[24px]`;

  return (
    <div tw="flex flex-col gap-[24px] h-full">
      <div css={ContainerSectionStyle}>
        {inputData.map(item => (
          <div key={item.id} tw="basis-[50%]">
            <Input
              register={register}
              name={item.name}
              label={item.title}
              errorMessage={item.error}
              placeholder={item.placeholder}
            />
          </div>
        ))}
        <div tw="basis-[50%]">
          <PhoneNumber
            control={control}
            defaultCountry={'EG'}
            label={'Phone number'}
            name={'phoneNumber'}
            phoneName={'phoneCh'}
            placeholder={'Phone number'}
            register={register}
            errors={errors?.phoneNumber?.message}
          />
        </div>
      </div>
      <div css={ContainerSectionStyle}>
        <div tw="basis-[50%]">
          <PhoneNumber
            control={control}
            defaultCountry={'EG'}
            label={
              <>
                Landline
                <span css={tw`text-text_tertiary font-light text-[13px]`}>(optional)</span>
              </>
            }
            name={'landline'}
            phoneName={'phoneCh'}
            placeholder={'Landline'}
            register={register}
            errors={errors?.phoneNumber?.message}
          />
        </div>
        <div tw="basis-[50%]">
          <Input register={register} name={'Address'} label={'Address'} placeholder={'address'} />
        </div>
      </div>

      <div css={ContainerSectionStyle}>
        {selectData.map(item => (
          <div key={item.id} tw="basis-[50%]">
            <GenericSelect
              control={control}
              name={item.name}
              placeholder={item.placeholder}
              errorMessage={item.error}
              label={item.label}
              options={item.options}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default CLinicForm;
