import { toast } from "react-toastify";
import "twin.macro";

export const showSuccess = (message) =>
  toast(
    <div>
      <div className="toast-title" tw="text-[0.9em] font-[600]">
        {message}
      </div>
    </div>,
    {
      className: "Toastify__toast--custom",
      closeButton: <button tw="absolute top-4 right-4 text-white"></button>,
      closeOnClick: true,
      hideProgressBar: true,
      autoClose: true,
      position: "bottom-right",
    }
  );

export const showError = (message) =>
  toast(
    <div>
      <div className="toast-title" tw="text-[0.9em] font-[600]">
        {message}
      </div>
    </div>,
    {
      className: "Toastify_error__toast--custom",
      closeButton: <button tw="absolute top-4 right-4 text-white"></button>,
      closeOnClick: true,
      hideProgressBar: true,
      autoClose: true,
      position: "bottom-right",
    }
  );

export const showInfo = (message, _ToastOptions) =>
  toast.info(
    <div>
      <span>{message}</span>
    </div>,

    _ToastOptions
  );
