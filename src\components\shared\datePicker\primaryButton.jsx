import "twin.macro";
import { styles } from "./datePicker";

// Button components
const PrimaryButton = ({
  children,
  onClick,
  primaryColor = styles.primaryColor,
  secondaryColor = styles.secondaryColor,
}) => (
  <button
    tw="px-[12px] py-[8px] rounded-[6px] border flex-1"
    css={{
      borderColor: primaryColor,
      backgroundColor: secondaryColor,
    }}
    onClick={onClick}
  >
    {children}
  </button>
);

export default PrimaryButton;
