import Download from '@assets/svgs/settings/downloadsvg.svg';
import 'twin.macro';

const InvoiceHistoryCard = ({ date, pdfUrl }) => {
  const downloadPdf = () => {
    fetch(pdfUrl)
      .then(response => response.blob())
      .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.download = 'sample.pdf';
        document.body.appendChild(link);

        link.click();

        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      })
      .catch(error => {
        console.error('Error fetching the file:', error);
      });
  };

  return (
    <div tw="flex items-center gap-[24px] mb-[7px]">
      <p tw="font-medium text-text_secondary text-[.875rem]">{date}</p>
      <p tw="text-[#0B834F] border border-[#05D84D] bg-[#E6FFEF] font-medium text-[.875rem] px-[16px] py-[6px] rounded-[20px]">
        Paid
      </p>
      <button
        onClick={downloadPdf}
        tw="text-[.875rem] border border-border_stroke flex items-center gap-[8px] font-medium py-[6px] px-[24px] rounded-[20px]"
      >
        <img src={Download} alt="Download" />
        Download
      </button>
    </div>
  );
};

export default InvoiceHistoryCard;
