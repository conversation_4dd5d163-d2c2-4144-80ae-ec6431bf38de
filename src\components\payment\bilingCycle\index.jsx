import tw from 'twin.macro';
import PaymentCard from '../paymentCard';

const BlingCycle = ({ selectedPlan, setSelectedPlan }) => {
  const billing = [
    {
      id: 0,
      title: '$20/mo ',
      badge: 'Monthly',
      subtitle: 'Cancel anytime, no fee',
      text: '+ $17/mo per member',
    },
    {
      id: 1,
      title: '$170/yr',
      badge: 'Yearly',
      text: '+ $17/mo per member',
      subtitle: 'No refund if you cancel after 14 days',
    },
  ];

  return (
    <PaymentCard
      title="Billing Cycle"
      content={
        <div tw="grid grid-cols-2 justify-between items-center">
          {billing.map((item, index) => (
            <div
              key={item.id}
              tw="grid gap-1 h-full border cursor-pointer px-[20px] py-[20px]"
              css={[
                selectedPlan.id === item.id
                  ? tw`border-primary bg-Primary_50`
                  : tw`bg-neutral-100 border-stroke`,
                index === 0 && tw`rounded-l-lg`,
                index === billing.length - 1 && tw`rounded-r-lg`,
              ]}
              onClick={() => setSelectedPlan(item)}
            >
              <span tw="font-[400] text-[0.87rem] text-black">{item.badge}</span>
              <span tw="font-[700] text-[1rem] text-black">{item.title}</span>
              <span tw="font-[500] text-[1rem] text-black">{item.text}</span>
              <small tw="font-[300] text-black italic text-[0.75rem]">{item.subtitle}</small>
            </div>
          ))}
        </div>
      }
    />
  );
};

export default BlingCycle;
