import PrescriptionCard from '@/components/prescriptionCard';
import LabelSections from '@/components/sectionLabels';
import { prescriptions } from '@/mock/prescriptions-data';
import { useMemo } from 'react';
import 'twin.macro';
import tw from 'twin.macro';
export default function PrescriptionList({ pov = 'patient' }) {
  const { activePrescriptions, pastPrescriptions } = useMemo(() => {
    return prescriptions.data.reduce(
      (acc, pres) => {
        acc[pres.isActive ? 'activePrescriptions' : 'pastPrescriptions'].push(pres);
        return acc;
      },
      { activePrescriptions: [], pastPrescriptions: [] }
    );
  }, []);

  return (
    <div tw="space-y-6">
      <div>
        <LabelSections text={'Active Prescription'} customStyle={tw`text-base text-black  mb-3`} />

        {pov === 'patient' ? (
          <PrescriptionCard prescription={activePrescriptions[0]} pov={pov} />
        ) : (
          <div tw="space-y-6">
            {activePrescriptions.map(prescription => (
              <PrescriptionCard key={prescription.id} prescription={prescription} pov={pov} />
            ))}
          </div>
        )}
      </div>
      <div tw="w-full h-[.0625rem] bg-border_stroke" />
      <div>
        <LabelSections text={'Past Perscriptions'} customStyle={tw`text-base text-black  mb-3`} />
        <div tw="space-y-6">
          {pastPrescriptions.map(prescription => (
            <PrescriptionCard key={prescription.id} prescription={prescription} pov={pov} />
          ))}
        </div>
      </div>
    </div>
  );
}
