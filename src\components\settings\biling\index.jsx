import BlingCycle from '@/components/payment/bilingCycle';
import { useState } from 'react';
import PaymentMethods from '../paymentMethods';
import PaymentDetails from '@/components/payment/paymentDetails';
import SectionCards from '@/components/sectionsCard';
import SeeFutureCharges from '@/components/payment/seeFutureCharges';
import ActionModal from '@/components/actionModal';
import cancelSubscriptionModal from '@assets/svgs/settings/solar_danger-triangle-outline.svg';
import InfoBilling from '../infoBilling';
import tw from 'twin.macro';

const Billing = () => {
  const [selectedPlan, setSelectedPlan] = useState([
    {
      id: 0,
      title: '$10/mo per member',
      badge: 'Monthly',
      subtitle: 'Cancel anytime, no fee',
    },
  ]);
  const [open, setOPen] = useState(false);
  const [ppenSubscriptionModal, setOpenSubscriptionModal] = useState(false);

  const handelCloseModel = () => {
    setOPen(false);
    setOpenSubscriptionModal(false);
  };
  const handelOpenCancelSubscriptionModal = () => {
    setOpenSubscriptionModal(true);
  };
  return (
    <div tw="h-full flex flex-col gap-[24px]">
      <InfoBilling />
      <div tw="flex gap-[24px] h-full">
        <div tw="basis-[50%] h-[113vh] overflow-y-auto" className="element">
          <PaymentMethods />
        </div>
        <div tw="basis-[50%] flex flex-col min-h-screen h-fit overflow-y-auto gap-[24px]">
          <BlingCycle selectedPlan={selectedPlan} setSelectedPlan={setSelectedPlan} />
          <SectionCards customStyle={tw`overflow-hidden`}>
            <PaymentDetails
              IncreaseValue={20}
              selectedPlan={selectedPlan}
              subtotal={20}
              totalValue={40}
              setOPen={setOPen}
              isInSettingPage
              memberCount={40}
              containerCardCustomStyle={tw`border-0`}
              couponExpire={'Active until Monday, June 30 2025'}
              couponValue={'BBSF+ (20% OFF)'}
              customTitle={
                <div tw="flex justify-between items-center mb-[24px]">
                  <p tw="font-semibold text-[1.125rem]">Your subscription</p>
                  <button
                    onClick={handelOpenCancelSubscriptionModal}
                    tw="bg-error_50 px-[16px] py-[10px] rounded-[8px] border text-error border-error font-medium"
                  >
                    Cancel Subscription
                  </button>
                </div>
              }
              containerStyle={tw`w-full bg-white min-h-fit border-0 p-[20px]`}
            />
          </SectionCards>
        </div>
        <SeeFutureCharges
          IncreaseValue={10}
          open={open}
          selectedPlan={selectedPlan}
          totalValue={10}
          handelCloseModel={handelCloseModel}
        />
        {ppenSubscriptionModal && (
          <ActionModal
            open={ppenSubscriptionModal}
            handleClose={handelCloseModel}
            primaryActionHandler={() => console.log('asds')}
            title="Cancel Subscription"
            cancelButtonText="Keep Subscription"
            customIcon={cancelSubscriptionModal}
            actionButtonText="Cancel Subscription"
            description="You can cancel your subscription at any time. You’ll continue to have access to your account until your current billing period ends."
          />
        )}
      </div>
    </div>
  );
};

export default Billing;
