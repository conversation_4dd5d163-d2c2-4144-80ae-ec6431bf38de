import { Controller } from 'react-hook-form';
import Select, { components } from 'react-select';
import AsyncSelect from 'react-select/async';
import Arrow from '@assets/svgs/collapse-arrow.svg';
import 'twin.macro';
import { useRef } from 'react';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '@tailwind';
import LabelWithTooltip from '@/components/labelWithTooltip';

const fullConfig = resolveConfig(tailwindConfig);
const {
  error: errorColor,
  border_stroke,
  error_50,
  neutral_50,
  neutral_100,
  stroke,
  Primary_100,
  Primary_600,
  text_primary,
  neutral_300,
} = fullConfig.theme.colors;

/**
 * GenericSelect Component
 *
 * A controlled select component that integrates with react-hook-form.
 * Supports error validation styling and messages.
 *
 * @param {Object} props Component props
 * @param {Object} props.control React Hook Form control object
 * @param {string} props.name Field name to register with react-hook-form
 * @param {Array} props.options Array of options for the select dropdown
 * @param {string} props.label Label text for the select
 * @param {boolean} props.disable Whether the select is disabled
 * @param {any} props.defaultValue Default value for the select
 * @param {boolean} props.error Whether the select has an error
 * @param {placeholder} props.placeholderWhether placeholder of input
 * @param {string} props.errorMessage Custom error message to display
 */

const getDefaultStyles = (error, disable) => ({
  control: props => ({
    ...props,
    paddingBlock: 3,
    outline: 'none !important',
    border: error
      ? `1px solid ${errorColor} !important`
      : props.isOpen
        ? 'none'
        : `1px solid ${border_stroke} !important`,
    borderRadius: '6px !important',
    paddingInline: '8px',
    backgroundColor: error ? error_50 : disable ? '#E4E7EB' : 'white',
    boxShadow: 'none !important',
    transition: 'all 0.3s ease-in-out',
    '&:hover': {
      border: error
        ? `1px solid ${errorColor} !important`
        : `1px solid ${border_stroke} !important`,
      background: neutral_50,
    },
  }),
  input: props => ({
    ...props,
    fontSize: '14px',
    border: 'none',
    outline: 'none',
  }),
  menuList: props => ({
    ...props,
    background: neutral_100,
    border: `1px solid ${border_stroke}`,
    borderRadius: '6px',
    padding: '0px',
  }),
  menu: props => ({
    ...props,
    borderRadius: '6px',
    boxShadow: '0px 4px 10px 0px rgba(0, 0, 0, 0.25)',
    marginTop: '4px',
  }),
  option: (provided, state) => {
    const options = state.options || [];
    const index = options.findIndex(option => option.value === state.data.value);
    const isFirst = index === 0;
    const isLast = index === options.length - 1;

    return {
      ...provided,
      borderBottom: isLast ? 'none' : `1px solid ${stroke}`,
      backgroundColor: state.isSelected ? Primary_100 : neutral_100,
      color: state.isSelected ? Primary_600 : text_primary,
      display: 'flex',
      fontWeight: state.isSelected ? '500' : '400',
      paddingBlock: '13px',
      paddingInline: '16px',
      alignItems: 'center',
      justifyContent: 'space-between',
      cursor: 'pointer',
      fontSize: '14px',
      borderTopLeftRadius: isFirst ? '6px' : 0,
      borderTopRightRadius: isFirst ? '6px' : 0,
      borderBottomLeftRadius: isLast ? '6px' : 0,
      borderBottomRightRadius: isLast ? '6px' : 0,
      '&:hover': {
        backgroundColor: state.isSelected ? Primary_100 : neutral_300,
      },
    };
  },

  indicatorSeparator: props => ({
    ...props,
    display: 'none',
  }),
  placeholder: props => ({
    ...props,
    color: text_primary,
    opacity: '50%',
    fontSize: '14px',
  }),
  singleValue: provided => ({
    ...provided,
    fontSize: '14px',
    color: text_primary,
  }),
  multiValue: provided => ({
    ...provided,
    backgroundColor: Primary_100,
    borderRadius: '6px',
  }),
  multiValueLabel: provided => ({
    ...provided,
    color: Primary_600,
    fontWeight: '500',
  }),
  multiValueRemove: provided => ({
    ...provided,
    color: errorColor,
    opacity: '0.8',
    ':hover': {
      backgroundColor: error_50,
      opacity: '1',
    },
  }),
});

// Custom Dropdown Indicator Component
const CustomDropdownIndicator = props => {
  const { selectProps } = props;
  return (
    <div>
      <img
        src={Arrow}
        alt="arrow"
        tw="w-[10px] h-[10px] mx-2 transition-transform duration-200"
        style={{
          transform: selectProps.menuIsOpen ? 'rotate(0deg)' : 'rotate(180deg)',
        }}
      />
    </div>
  );
};

const GenericSelect = ({
  control,
  name,
  options,
  label,
  labelStyle,
  labelContainerStyle,
  disable,
  defaultValue,
  error,
  errorMessage,
  placeholder,
  customStyles,
  inputLength,
  isMulti,
  customComponents,
  isAsync,
  loadOptions,
  tooltipText,
  hasTooltip,
  toolTipActiveOnHover,
  rootElementStyle,
  isFixed,
  ...res
}) => {
  const CustomInput = props => <components.Input {...props} maxLength={inputLength} />;
  const defaultStyles = getDefaultStyles(errorMessage, disable);
  const mergedStyles = {};

  for (const key of new Set([
    ...Object.keys(defaultStyles),
    ...(customStyles ? Object.keys(customStyles) : []),
  ])) {
    const defaultFn = defaultStyles[key];
    const customFn = customStyles?.[key];

    // If both exist, merge their results
    if (typeof defaultFn === 'function' && typeof customFn === 'function') {
      mergedStyles[key] = (provided, state) => ({
        ...defaultFn(provided, state),
        ...customFn(provided, state),
      });
    } else {
      // Use whichever exists (custom overrides default)
      mergedStyles[key] = customFn || defaultFn;
    }
  }

  const selectRef = useRef();

  // Choose Select or AsyncSelect based on isAsync prop
  const SelectComponent = isAsync ? AsyncSelect : Select;

  return (
    <>
      <div tw="grid gap-1" css={rootElementStyle}>
        {label && (
          <LabelWithTooltip
            label={label}
            tooltipText={tooltipText}
            hasTooltip={hasTooltip}
            toolTipActiveOnHover={toolTipActiveOnHover}
            labelStyle={labelStyle}
            containerStyle={labelContainerStyle}
          />
        )}
        <Controller
          name={name}
          control={control}
          defaultValue={defaultValue}
          render={({ field: { onChange, value } }) => (
            <SelectComponent
              ref={selectRef}
              value={value}
              placeholder={placeholder}
              menuPosition={isFixed ? 'fixed' : ''}
              components={{
                DropdownIndicator: CustomDropdownIndicator,
                Input: CustomInput,
                ...customComponents,
              }}
              // onChange check for custom click handlers
              // If the option has a customClickHandler, call it instead of updating the form state
              onChange={option => {
                if (isMulti) {
                  // option is an array of selected options
                  const lastSelected = Array.isArray(option) ? option[option.length - 1] : option;

                  if (lastSelected && lastSelected.customClickHandler) {
                    lastSelected.customClickHandler();
                    return; // Don't update form state
                  }
                  onChange(option);
                  setTimeout(() => {
                    if (selectRef.current && selectRef.current.focusInput) {
                      selectRef.current.focusInput();
                    }
                  }, 0);
                } else {
                  // Single select
                  if (option && option.customClickHandler) {
                    option.customClickHandler();
                    return; // Don't update form state
                  }
                  onChange(option);
                }
              }}
              // Pass options or loadOptions depending on Select type
              {...(isAsync ? { loadOptions } : { options })}
              styles={mergedStyles}
              classNamePrefix="custom-select"
              // hideSelectedOptions
              tabSelectsValue
              isDisabled={disable}
              isMulti={isMulti}
              closeMenuOnSelect={!isMulti}
              className={errorMessage ? 'error-select' : ''}
              formatOptionLabel={(data, { context, selectValue }) => {
                if (context === 'menu') {
                  const isSelected = selectValue.some(val => val.value === data.value);
                  return (
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        width: '100%',
                        height: '100%',
                      }}
                    >
                      <span>{data.label}</span>
                      {isSelected && (
                        <svg width="16" height="16" fill={Primary_600} viewBox="0 0 24 24">
                          <path d="M20.285 6.709a1 1 0 0 0-1.414-1.418L9 15.172l-3.871-3.87a1 1 0 1 0-1.414 1.414l4.578 4.577a1 1 0 0 0 1.414 0l10.578-10.584z" />
                        </svg>
                      )}
                    </div>
                  );
                }

                return data.label;
              }}
              {...res}
            />
          )}
        />
      </div>
      {errorMessage && (
        <p tw="mt-1 text-sm text-error">{errorMessage || 'Please select a valid option'}</p>
      )}
    </>
  );
};
export default GenericSelect;
