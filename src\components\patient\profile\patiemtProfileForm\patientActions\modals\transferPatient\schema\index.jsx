import * as Yup from 'yup';

export const transferPatientSchema = Yup.object().shape({
  patient_name: Yup.string()
    .required('Patient name is required')
    .min(2, 'Name is too short')
    .max(100, 'Name is too long')
    .test(
      'is-valid-first-name',
      'Enter a valid name (letters only).',
      value => !value || /^[\u0600-\u06FFa-zA-Z]+(?: [\u0600-\u06FFa-zA-Z]+){0,2}$/.test(value)
    ),

  therapist: Yup.object()
    .shape({
      label: Yup.string().required(),
      value: Yup.string().required(),
    })
    .required('Required field.'),
});
