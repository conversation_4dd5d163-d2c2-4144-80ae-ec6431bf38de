import CollapseArrow from '@assets/svgs/collapse-arrow.svg';
import tw from 'twin.macro';

/**
 * Collapse component that renders a collapsible container with a header and body
 * @param {Object} props
 * @param {Boolean} open
 * @param {Function} handelToggleCollapse
 * @param {Function} arrowIconCustomStyle
 * @param {ReactNode} props.header - React node for the header of the collapsible container
 * @param {ReactNode} props.body - React node for the body of the collapsible container
 * @param {Object} props.containerStyle - Custom styles for the container
 * @param {Object} props.headerCustomStyle - Custom styles for the header
 * @param {Object} props.bodyCustomStyle - Custom styles for the body
 */

const Collapse = ({
  header,
  body,
  containerStyle,
  headerCustomStyle,
  bodyCustomStyle,
  arrowIconCustomStyle,
  open,
  handelToggleCollapse,
}) => {
  return (
    <div
      css={[
        tw`transition-all duration-500 ease-in-out w-full`,
        open ? tw`max-h-[1000px] opacity-100` : tw`max-h-[40px] opacity-90 overflow-hidden`,
        containerStyle,
      ]}
    >
      <div
        onClick={handelToggleCollapse}
        css={[
          tw`flex gap-2 items-center cursor-pointer transition-all duration-300 rounded-[10px] px-3`,
          headerCustomStyle,
        ]}
      >
        <img
          css={[
            tw`transition-transform duration-300 ease-in-out`,
            arrowIconCustomStyle,
            !open && tw`rotate-180`,
          ]}
          src={CollapseArrow}
          alt="collapse"
          tw="w-[12px] h-[15px]"
        />
        {header}
      </div>
      <div
        css={[
          tw`transition-all duration-500 ease-in-out transform`,
          bodyCustomStyle,
          open
            ? tw`opacity-100 translate-y-0 max-h-[1000px]`
            : tw`opacity-0 -translate-y-2 max-h-0 overflow-hidden`,
        ]}
      >
        {body}
      </div>
    </div>
  );
};

export default Collapse;
