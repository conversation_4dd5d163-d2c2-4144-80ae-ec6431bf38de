import LeftArrow from '@assets/svgs/auth/left-arrow.svg';
import 'twin.macro';

const ClinicCard = ({ name, imageUrl, handelCLick }) => {
  return (
    <div
      tw="flex justify-between items-center w-full border-b border-stroke py-[20px] px-[24px] hover:bg-Primary_50 transition-all duration-300 cursor-pointer"
      onClick={handelCLick}
    >
      <div tw="flex gap-3 items-center">
        <div tw="flex justify-center items-center rounded-full bg-Primary">
          {imageUrl ? (
            <img src={imageUrl} alt={name} tw="w-[40px] h-[40px] rounded-full object-cover" />
          ) : (
            <p tw="font-['Inter'] text-[0.9rem] py-[6px] px-[8px] font-[700]">
              {String(name).slice(0, 2).toLocaleUpperCase()}
            </p>
          )}
        </div>
        <p tw="text-[0.9rem] font-[700] font-['Inter']">{name}</p>
      </div>
      <div>
        <img src={LeftArrow} alt={name} />
      </div>
    </div>
  );
};

export default ClinicCard;
