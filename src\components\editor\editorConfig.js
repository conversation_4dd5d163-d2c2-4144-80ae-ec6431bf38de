import { Color } from '@tiptap/extension-color';
import ListItem from '@tiptap/extension-list-item';
import TextStyle from '@tiptap/extension-text-style';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import Underline from '@tiptap/extension-underline';
import Placeholder from '@tiptap/extension-placeholder';
import Image from '@tiptap/extension-image';
import { Node, Extension } from '@tiptap/core';

export const extensions = [
  Color.configure({ types: [TextStyle.name, ListItem.name] }),
  TextStyle.configure({ types: [ListItem.name] }),
  Underline,
  Link.extend({
    atom: true,
    inclusive: false,
  }).configure({
    openOnClick: true,
    editable: false,
    HTMLAttributes: {
      class: 'text-blue-500 underline hover:no-underline cursor-context-menu cursor-pointer',
    },

    inclusive: false, // avoids link overrun
  }),
  StarterKit.configure({
    document: true,
    bulletList: {
      keepMarks: true,
      keepAttributes: false,
    },
    orderedList: {
      keepMarks: true,
      keepAttributes: false,
    },
  }),
  Placeholder.configure({
    placeholder: 'Your content starts here…',
  }),
];
