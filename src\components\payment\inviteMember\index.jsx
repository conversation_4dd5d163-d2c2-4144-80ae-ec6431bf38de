import { useRef, useState, useEffect } from 'react';
import Delete from '@/assets/svgs/auth/delete-member.svg';
import PaymentCard from '../paymentCard';
import AuthInput from '@/components/auth/authInput';
import GenericSelect from '@/components/shared/select';
import 'twin.macro';

const InviteMember = ({
  getValues,
  append,
  setValue,
  register,
  control,
  fields,
  remove,
  errors,
  setError,
  clearErrors,
}) => {
  const [value, setx] = useState(0);
  const emailInputRef = useRef(null);
  const selecteData = [
    { label: 'Therapist', value: 1 },
    { label: 'Admin', value: 2 },
  ];

  // Add effect to handle focus after value changes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (emailInputRef.current) {
        const inputElement = emailInputRef.current.querySelector('input');
        if (inputElement) {
          inputElement.focus();
        }
      }
    }, 0);
    return () => clearTimeout(timer);
  }, [value]);

  const validateEmail = email => {
    if (!email) return false;
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email.trim());
  };

  const validateMemberInput = () => {
    const email = getValues('currentEmail');
    const role = getValues('currentRole');
    let isValid = true;

    // Validate email
    if (!email) {
      setError('currentEmail', {
        type: 'manual',
        message: 'Email is required',
      });
      isValid = false;
    } else if (!validateEmail(email)) {
      setError('currentEmail', {
        type: 'manual',
        message: 'Please enter a valid email address',
      });
      isValid = false;
    } else {
      clearErrors('currentEmail');
    }

    // Validate role
    if (!role) {
      setError('currentRole', {
        type: 'manual',
        message: 'Please select a role',
      });
      isValid = false;
    } else {
      clearErrors('currentRole');
    }

    return isValid;
  };

  const handleAddMember = () => {
    if (!validateMemberInput()) {
      return;
    }

    const email = getValues('currentEmail');
    const role = getValues('currentRole');

    append({ email, role });
    setx(prev => prev + 1);
    setValue('currentEmail', '');
    setValue('currentRole', null);
  };

  const handleMemberEmailChange = (index, email) => {
    if (!validateEmail(email)) {
      setError(`members.${index}.email`, {
        type: 'manual',
        message: 'Please enter a valid email address',
      });
    }
  };

  return (
    <PaymentCard
      title="Invite member"
      content={
        <>
          <div tw="flex gap-6 items-start mt-4">
            <div tw="w-[60%]">
              <AuthInput
                register={register}
                name="currentEmail"
                label="Email"
                key={value}
                errorMessage={errors.currentEmail?.message}
                placeholder="Email Address"
                inputRef={emailInputRef}
              />
            </div>
            <div tw="w-[40%]">
              <GenericSelect
                control={control}
                label="Role"
                key={value}
                placeholder="Role"
                name="currentRole"
                options={selecteData}
                errorMessage={errors.currentRole?.message}
              />
            </div>
          </div>
          <div tw="grid gap-1 mt-4">
            {fields.map((field, index) => (
              <div key={field.id} tw="flex gap-6 items-center mb-2">
                <div tw="w-[60%] flex gap-1">
                  <img
                    src={Delete}
                    alt="Delete"
                    tw="cursor-pointer"
                    onClick={() => remove(index)}
                  />

                  <AuthInput
                    register={register}
                    name={`members.${index}.email`}
                    label=""
                    placeholder="Email Address"
                    errorMessage={errors.members?.[index]?.email?.message}
                    onChange={e => handleMemberEmailChange(index, e.target.value)}
                  />
                </div>
                <div tw="w-[40%]">
                  <GenericSelect
                    control={control}
                    label=""
                    name={`members.${index}.role`}
                    options={selecteData}
                  />
                </div>
              </div>
            ))}
          </div>
          <div tw="mt-4 w-fit">
            <p tw="text-info text-[0.875rem] cursor-pointer" onClick={handleAddMember}>
              +Add member
            </p>
          </div>
        </>
      }
    />
  );
};

export default InviteMember;
