import React from 'react';
import tw from 'twin.macro';
import SectionContainer from '@/components/patient/SectionContainer';
import Input from '@/components/shared/input';
import DatePicker from '@/components/shared/datePicker/datePicker';
import GenericSelect from '@/components/shared/select';
import PhoneNumber from '@/components/phoneNumber';
import { Controller } from 'react-hook-form';
import SelectorInputCompund from '@/components/selectorInputCompund';
import { gender_opetions, height_options, weight_options } from '@/constants/constants';
import 'twin.macro';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '@tailwind';
import UnitCompound, { styles } from '@/components/addPatientForm/unitCompound';

// Make sure to pass these props from the parent component

const fullConfig = resolveConfig(tailwindConfig);
const { text_secondary } = fullConfig.theme.colors;
function PatientClinicInformation({
  control,
  register,
  errors,
  touchedFields,
  formData,
  fieldsThPermistions,
}) {
  return (
    <SectionContainer customeClass={tw`grid grid-cols-2 gap-x-8 gap-y-3`}>
      {/* Email */}
      <Input
        disabled={!fieldsThPermistions.email}
        disabledStyle={tw`bg-disable cursor-not-allowed hover:bg-disable`}
        type="text"
        name="email"
        defaultValue={formData.email}
        placeholder={'Email Address'}
        labelStyle={tw`text-[0.9rem]`}
        inputStye={tw`text-[0.8rem]`}
        register={register}
        label={'Email'}
        errorMessage={errors?.email?.message}
        containerSTyle={tw`bg-white border-border_stroke disabled:bg-disable disabled:cursor-not-allowed`}
      />

      {/* PHONE NUMBER */}
      <div tw="flex-1">
        <PhoneNumber
          control={control}
          defaultCountry={'EG'}
          label={'Phone number'}
          name={'phoneNumber'}
          phoneName={'phoneCh'}
          placeholder={'Phone number'}
          register={register}
        />
      </div>

      {/* birth date */}
      <div>
        <Controller
          control={control}
          name="birthdate"
          render={({ field }) => (
            <DatePicker
              {...field}
              disabled={!fieldsThPermistions.birthdata}
              disabledStyle={tw`bg-disable cursor-not-allowed hover:bg-disable`}
              value={field.birthdate}
              onChange={field.onChange}
              placeholder="mm/dd/yyyy"
              label="Date of Birth"
              labelClassName="!font-medium !text-[0.9rem] !mb-1"
              dateFormat={{
                day: 'numeric',
                month: 'numeric',
                year: 'numeric',
              }}
              inputClassName="!border-border_stroke !px-4 !py-3 font-medium text-[0.8rem] hover:bg-neutral_100 focus:bg-neutral_100 focus:outline-none focus:caret-transparent"
              calendarClassName="bottom-[0%] left-[calc(100%+8px)] !w-[160%]"
              maxDate={new Date()}
            />
          )}
        />
        {errors.dateOfBirth && touchedFields.dateOfBirth && (
          <span css={tw`text-error text-sm`}>{errors.dateOfBirth.message}</span>
        )}
      </div>

      {/* gender */}
      <div>
        <GenericSelect
          control={control}
          disable={!fieldsThPermistions.gender}
          disabledStyle={tw`bg-disable cursor-not-allowed hover:bg-disable`}
          label="Gender"
          name="gender"
          placeholder="Select gender"
          options={gender_opetions}
          errorMessage={errors?.gender?.message || ''}
          labelContainerStyle={tw`!mb-0`}
          labelStyle={tw`font-medium text-[0.9rem] `}
          customStyles={{
            control: () => ({
              padding: '.25rem .5px',
            }),
          }}
          inputLength={7}
        />
      </div>
      {/* height */}
      <UnitCompound
        labelText="Height"
        labelTooltipText="height toolTip"
        control={control}
        selectorName="heightUnit"
        selectorPlaceHolder="cm"
        selectorOptions={height_options}
        inputName="height"
        inputPlaceholder="0 cm"
        register={register}
        errors={errors}
        touchedFields={touchedFields}
        customStyles={{
          ...styles,

          control: () => ({
            ...styles.control(),
            padding: '.25rem',
          }),
        }}
      />
      {/* weight */}
      <UnitCompound
        labelText="Weight"
        labelTooltipText="weight toolTip"
        control={control}
        selectorName="weightUnit"
        selectorPlaceHolder="kg"
        selectorOptions={weight_options}
        inputName="weight"
        inputPlaceholder="0 kg"
        register={register}
        errors={errors}
        touchedFields={touchedFields}
        customStyles={{
          ...styles,

          control: () => ({
            ...styles.control(),
            padding: '.25rem',
          }),
        }}
      />
    </SectionContainer>
  );
}

export default PatientClinicInformation;
