import LabelSections from '@/components/sectionLabels';
import Input from '@/components/shared/input';
import tw from 'twin.macro';

const MedicalInformation = ({ register }) => {
  const inputsData = [
    { id: 0, title: 'Professional Degree', name: 'degree', placeholder: 'Professional Degree' },
    {
      id: 1,
      title: 'Professional License number',
      name: 'licenseNumber',
      placeholder: '00-**********',
    },
  ];

  return (
    <div tw="space-y-4 p-[24px] ">
      <LabelSections text={'Medical information'} />
      <div tw="flex gap-[16px] items-start">
        {inputsData.map(item => (
          <div tw="flex-1" key={item.id}>
            <Input
              containerSTyle={tw`flex-shrink-0`}
              label={item.title}
              register={register}
              name={item.name}
              placeholder={item.placeholder}
              key={item.id}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default MedicalInformation;
