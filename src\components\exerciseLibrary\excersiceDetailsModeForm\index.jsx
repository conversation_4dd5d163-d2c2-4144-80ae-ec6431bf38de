/* eslint-disable indent */

import GenericSelect from '@/components/shared/select';
import ResetToDefaultSection from '../resetToDefaultButton';
import Input from '@/components/shared/input';
import tw from 'twin.macro';
import { InitialState } from '../prescriptionForm/module';
import { useForm } from 'react-hook-form';

/* eslint-disable no-case-declarations */
const ExerciseDetailsModeForm = ({
  handelResetDefault,
  text,
  exerciseConfig,
  shouldDisplayField,
  movementName,
  getFieldValidationRules,
}) => {
  const {
    register,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: InitialState,
    mode: 'onBlur',
  });
  return (
    <div tw="col-span-12 lg:col-span-8 h-fit py-4 border-b border-[#D9D9D9]">
      <ResetToDefaultSection handelClick={handelResetDefault} text={text} />
      <div tw="grid grid-cols-4 gap-y-2 gap-x-4 items-start">
        {exerciseConfig?.map(field => {
          // First check if the field should be displayed based on its displayDependsOn property
          if (!shouldDisplayField(field)) {
            return null; // Don't render if displayDependsOn condition is not met
          }

          // Get the field name with movementName prefix
          const fieldName = `${movementName}_${field.name}`;

          // Get the validation rules for this field
          const validationRules = getFieldValidationRules(field);

          // Get any existing error for this field
          const fieldError = errors && errors[fieldName];

          switch (field.type) {
            case 'dropdown':
              const transformedOptions = field.options.map(option => ({
                label: option,
                value: option,
              }));

              // Transform the default value to the correct format
              const defaultValueObject = field.default
                ? {
                    label: field.default,
                    value: field.default,
                  }
                : null;

              return (
                <GenericSelect
                  key={field.name}
                  control={control}
                  name={fieldName}
                  label={field.label}
                  options={transformedOptions} // Use transformed options
                  defaultValue={defaultValueObject} // Set default value as an object
                  rules={validationRules}
                  labelStyle={tw`font-normal mb-0 text-[0.9rem] text-text_secondary`}
                  errorMessage={fieldError?.message}
                />
              );
            case 'input':
              // Validate and convert max_value and min_value to numbers with fallbacks
              const maxValue =
                field.max_value && !isNaN(Number(field.max_value))
                  ? Number(field.max_value)
                  : undefined;
              const minValue =
                field.min_value && !isNaN(Number(field.min_value))
                  ? Number(field.min_value)
                  : undefined;
              const defaultValue =
                field.default && !isNaN(Number(field.default)) ? Number(field.default) : undefined;

              return (
                <Input
                  key={field.name}
                  register={register}
                  name={fieldName}
                  label={field.label}
                  placeholder={field.placeholder}
                  type="number"
                  labelStyle={tw`font-normal text-[1rem] text-text_secondary`}
                  max={maxValue}
                  min={minValue}
                  defaultValue={defaultValue}
                  validation={validationRules}
                  errorMessage={fieldError?.message}
                />
              );
            default:
              return null;
          }
        })}
      </div>
    </div>
  );
};

export default ExerciseDetailsModeForm;
