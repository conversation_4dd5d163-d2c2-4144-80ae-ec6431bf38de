import Checkbox from '@/components/shared/checkbox';
import { Controller } from 'react-hook-form';

// All Checkbox Component
const AllCheckbox = ({ control, setValue, filterItems }) => (
  <Controller
    name="all"
    control={control}
    render={({ field }) => (
      <Checkbox
        label="All"
        hasHovering
        checked={field.value}
        onChange={e => {
          const checked = e.target.checked;
          field.onChange(checked);

          // Reset all other checkboxes when "All" is checked
          filterItems.forEach(i => {
            if (i.id !== 'all') {
              setValue(`marketingConsent_${i.id}`, false);
              if (i.items) {
                i.items.forEach(_val => {
                  setValue(`marketingConsent_custom_${_val.id}`, false);
                });
              }
            }
          });
        }}
      />
    )}
  />
);

export default AllCheckbox;
