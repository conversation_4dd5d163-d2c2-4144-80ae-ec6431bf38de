import React from 'react';
import tw from 'twin.macro';
import AddStaff from './addStafff';
import GenericModal from '@/components/genericModal';
import ModalTitle from '@/components/modalTitle';

const AddStaffView = ({
  open,
  handleClose,
  handelAction,
  register,
  errors,
  touchedFields,
  control,
  isValid,
}) => {
  return (
    <GenericModal
      element="div"
      openModel={open}
      handelCloseMode={handleClose}
      containerModalStyle={[
        tw`!max-w-[34rem] md:!min-w-0 !min-w-0 !rounded-xl h-fit shadow-[0rem_0.3125rem_0.9375rem_0rem_rgba(0,0,0,0.08),0rem_0.9375rem_2.1875rem_-0.3125rem_rgba(17,24,38,0.2),0rem_0rem_0rem_0.0625rem_rgba(152,161,178,0.1)] !overflow-hidden`,
      ]}
      title={<ModalTitle title={'Add staff'} handleClose={handleClose} />}
      titleStyle={tw`!p-0 border-b border-stroke !rounded-t-xl`}
      content={<AddStaff {...{ errors, register, touchedFields, control }} />}
      contetnContainerStyle={tw` min-h-[0px] `}
      PrimaryButtonDisable={!isValid}
      PrimaryButtonStyle={tw`flex-1 !py-[10px] !rounded-[6px] !font-medium`}
      PrimaryButtonText={'Continue'}
      SecondaryButtonStyle={tw`flex-1 !py-[10px] !rounded-[6px] !font-medium bg-white !border-border_stroke text-text_primary text-sm`}
      secondaryButtonText="Cancel"
      clickOnSecondaryButton={handleClose}
      clickOnPrimaryButton={handelAction}
    />
  );
};

export default AddStaffView;
