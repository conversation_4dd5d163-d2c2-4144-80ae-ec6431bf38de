import React from 'react';
import DuplicateIcon from '@assets/svgs/duplicate.svg';
import DuplicateIconHover from '@assets/svgs/duplicate-hover.svg';
import tw from 'twin.macro';

const Duplicate = ({ handleClick }) => (
  <button
    type="button"
    css={tw`relative w-[18px] h-[18px] p-0 border-none bg-none`}
    className="group"
    onClick={e => {
      handleClick();
      e.stopPropagation();
    }}
    onPointerDown={e => e.stopPropagation()}
  >
    <img
      src={DuplicateIcon}
      alt="duplicate Icon"
      css={tw`absolute top-0 left-0 w-[22px] h-[22px] transition-opacity duration-300 opacity-100 group-hover:opacity-0`}
      draggable={false}
    />
    <img
      src={DuplicateIconHover}
      alt="duplicate Icon Hover"
      css={tw`absolute top-0 left-0 w-[22px] h-[22px] transition-opacity duration-300 opacity-0 group-hover:opacity-100`}
      draggable={false}
    />
  </button>
);

export default Duplicate;
