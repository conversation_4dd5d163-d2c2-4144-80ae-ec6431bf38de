import { useMemo, useState } from 'react';
import FrequencyAndDate from './frequencyAndDate';
import PrescriptionTitle from './prescriptionTitle';
import PrescriptionsCardsList from './prescriptionsCardsList';
import CollapseInsightPersecriptionCards from './collapseInsightPersecriptionCards';
import Modals from './modals';
import 'twin.macro';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useExerciseLibrary } from '@/zustand/exercise-library';
import { prescriptionMode } from '@/constants/constants';
import { mappedExerciseToPrescriptionView } from '@/mock/prescriptions-data';

/**
 * A component that displays active prescriptions information including title, frequency details, and prescription cards
 *
 * @param {Object} props
 * @param {Array} props.prescriptionsList - Array of prescriptions to be displayed in the cards list
 * @param {Array} [props.insightsList] - Optional array of insights to be displayed in collapsible section
 * @param {string} [props.insightsTitle="Insights"] - Optional custom title for the insights section
 *
 *
 * @example
 * <ActivePrescriptions
 *   exercisesList={[
 *     {
 *       // exercisesList data structure
 *     }
 *   ]}
 *   insightsList={[
 *     {
 *       title: "Progress",
 *       content: "70%",
 *       containerStyle: tw`custom-style`
 *     }
 *   ]}
 *   insightsTitle="Weekly Stats"
 * />
 *
 * @returns {JSX.Element} A card containing prescription information with title, menu, frequency details, and prescription cards
 *
 * @styling
 * - Background: White
 * - Text size: 1.05em
 * - Border: Default border with rounded corners
 * - Padding: 24px (both x and y)
 * - Layout: Grid with 8px gap between sections
 * - Sections:
 *   1. Header with title and menu
 *   2. Frequency and date information
 *   3. List of prescription cards
 *   4. Collapsible insights section (if insights are provided)
 *
 * @components
 * - Uses PrescriptionActiveTitle for header information
 * - Uses Menu for action options (Edit/Delete)
 * - Uses FrequencyAndDate for timing details
 * - Uses PrescriptionsCardsList for displaying prescription items
 * - Uses InsightsCard for displaying insights metrics
 */

const PrescriptionCard = ({ prescription, pov }) => {
  const [isInsightsExpanded, setIsInsightsExpanded] = useState(false);
  const [insightsText, setInsightsText] = useState('Show Insights');
  const { setMode, setEditTemplateData } = useExerciseLibrary();

  const toggleInsights = () => {
    setIsInsightsExpanded(!isInsightsExpanded);
    setInsightsText(prevState =>
      prevState === 'Show Insights' ? 'Hide Insights' : 'Show Insights'
    );
  };

  const { prescriptionData, id, prescriptionsTitleProps, frequencyData, isActive } = useMemo(() => {
    const { prescription: prescriptionData, therapist, id, isActive, patient } = prescription;

    const prescriptionsTitleProps = {
      byPerson: pov === 'patient' ? `${therapist.first_name} ${therapist.last_name}` : patient.Name,
      prescriptionName: prescriptionData.name,
      therapist_id: therapist.id,
      patient_id: patient.id,
      prescription_id: id,
      notes: prescriptionData.notes,
      isActive,
    };

    const frequencyData = {
      start_date: prescriptionData.start_date,
      end_date: prescriptionData.end_date,
      frequency: prescriptionData.frequency,
      duration: prescriptionData.duration,
      eta: prescriptionData.eta,
    };

    return {
      prescriptionData,
      therapist,
      id,
      prescriptionsTitleProps,
      frequencyData,
      isActive,
    };
  }, [prescription.id]);

  const editPerscriptionBase = () => {
    setMode(prescriptionMode.EDIT);
    setEditTemplateData(mappedExerciseToPrescriptionView(prescription));
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div tw="px-6 py-5 bg-white border rounded-card border-border_stroke">
        <PrescriptionTitle
          {...prescriptionsTitleProps}
          editPerscriptionBase={editPerscriptionBase}
          pov={pov}
        >
          <Modals
            id={id}
            prescription={prescription}
            isActive={isActive}
            editPerscriptionBase={editPerscriptionBase}
          />
        </PrescriptionTitle>
        <FrequencyAndDate {...frequencyData} />
        <PrescriptionsCardsList exercisesList={prescriptionData.exercises} />
        <CollapseInsightPersecriptionCards
          insightsList={prescriptionData.insights}
          isInsightsExpanded={isInsightsExpanded}
          toggleInsights={toggleInsights}
          insightsTitle={insightsText}
        />
      </div>
    </DndProvider>
  );
};

export default PrescriptionCard;
