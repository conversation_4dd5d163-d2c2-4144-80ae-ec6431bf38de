import RulesSection from '../rulesSection';
import MemberSubscription from '../memberSubscription';
import SubTotal from '../subtotalItem';
import Input from '@/components/shared/input';
import TotalItem from '../totalItem';
import Checkbox from '@/components/shared/checkbox';
import PrimaryButton from '@/components/shared/primaryButton';
import Notification from '@assets/svgs/payment/notfication.svg';
import tw from 'twin.macro';

const PaymentDetails = ({
  listOfRules,
  IncreaseValue,
  selectedPlan,
  register,
  totalValue,
  watch,
  subtotal,
  handelApply,
  isApplying,
  setOPen,
  memberCount,
  isInSettingPage,
  customTitle,
  containerStyle,
  containerCardCustomStyle,
  couponValue,
  couponExpire,
}) => {
  const validateDiscount = value => {
    const discount = Number(value);
    if (discount) {
      if (discount > 100) {
        return 'Discount cannot be greater than 100%';
      }
      if (discount < 0) {
        return 'Discount cannot be negative';
      }
      return false;
    }
  };

  return (
    <div
      tw="w-[50%] min-h-screen border border-Primary bg-Primary_50 px-[7%] py-[4%]"
      css={containerStyle}
    >
      {customTitle ? (
        customTitle
      ) : (
        <div tw="p-[20px] border-2 border-Primary_600 bg-Primary rounded-t-[8px] ">
          <p tw="text-[1.25rem] font-[600]">Subscription Summary</p>
        </div>
      )}
      <div
        tw="bg-white px-[20px] border-Primary_600 border border-t-0 rounded-b-[8px]"
        css={containerCardCustomStyle}
      >
        {listOfRules?.length > 0 && <RulesSection listOfRules={listOfRules} />}
        <div tw="py-[20px] border-b border-[#CFCFCF] grid gap-2">
          <div tw="flex justify-between items-start font-medium text-[0.875rem]">
            <p tw="text-[0.875rem]">Clinic subscription</p>
            <p tw="text-[0.875rem]">$20</p>
          </div>
          <MemberSubscription
            IncreaseValue={IncreaseValue}
            title="Member subscription"
            hasSubtitle
          />
          <MemberSubscription IncreaseValue={selectedPlan.badge} title="Billing cycle" />

          <SubTotal subtotal={subtotal} title={`Subtotal (${memberCount + 1} member)`} />
        </div>
        {isInSettingPage && (
          <div tw="py-[20px] space-y-2">
            <p tw="font-medium text-black">Coupon code</p>
            <div tw="bg-Primary_100 text-center rounded-[6px] p-[16px]">
              <p tw="font-bold text-[1.125rem] text-Primary_600">{couponValue}</p>
            </div>
            <span tw="text-[#5E738A] text-[.8125rem]">{couponExpire}</span>
          </div>
        )}
        {!isInSettingPage && watch && (
          <div tw="py-[20px] grid gap-2">
            <Input
              register={register}
              name="code"
              label="Enter discount percentage"
              placeholder="Enter percentage (e.g. 20)"
              errorMessage={validateDiscount(watch('code'))}
              lsatIcon={
                <button
                  onClick={handelApply}
                  disabled={isApplying}
                  tw="text-Primary_600 underline font-bold cursor-pointer text-[0.9rem] px-3"
                >
                  {isApplying ? 'Applying...' : 'Apply'}
                </button>
              }
            />
          </div>
        )}
        <div tw="">
          <div tw="py-[16px] flex justify-between items-start font-medium text-[0.8rem] border-y border-[#CFCFCF]">
            <p tw="font-bold">VAT</p>
            <p tw="font-bold">$0</p>
          </div>
          <TotalItem contentStyle={tw`py-[16px]`} title="Total" totalValue={totalValue} />
        </div>
        {!isInSettingPage && (
          <div tw="bg-[#EEF3FF] p-[16px] flex gap-1 items-center justify-center font-medium text-[0.875rem] rounded-[6px] w-full">
            <img src={Notification} alt="Notification" />
            <span>You will be charged after the 14-day trial</span>
          </div>
        )}
        <div tw="flex justify-between items-start font-medium text-[0.875rem] py-[10px]">
          <div>
            <p tw="text-black [line-height: 80%]">Next charge on June 1, 2025</p>
            <p
              onClick={() => setOPen(true)}
              tw="mt-1 font-bold underline cursor-pointer text-Primary_600"
            >
              See future charges
            </p>
          </div>
          <p>$10</p>
        </div>
        {!isInSettingPage && watch && (
          <Checkbox
            label={
              <span tw="text-text_primary text-[0.875rem] [line-height: 130%] font-medium mb-2">
                By subscribing you agree to Rehabitaire processing your data in line with our
                Privacy Policy and our Terms of Service. Once your 14 day free trial has concluded
                you will be charged the above amount automatically, however you can cancel your free
                trial at any time.
              </span>
            }
            register={register}
            name="marketingConsent"
            checked={watch('marketingConsent')}
          />
        )}

        {!isInSettingPage && (
          <PrimaryButton
            text="Start your free trial"
            type="submit"
            customStyle={tw`w-full font-bold mt-4 rounded-[6px] py-[14px] text-[1.125rem] mb-[20px]`}
          />
        )}
      </div>
    </div>
  );
};

export default PaymentDetails;
