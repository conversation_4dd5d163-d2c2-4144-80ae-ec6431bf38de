import { useMemo } from 'react';
// import patientsData from '@/mock/patients-data';

export function useFilteredTableData(filterValue, searchValue, tableData) {
  return useMemo(() => {
    // Filter by filterValue if not "All"
    let data = tableData;
    if (filterValue !== 'All') {
      data = data.filter(row =>
        // Check if any value in the row matches the filterValue
        Object.values(row).some(
          value =>
            // Check if the value is defined and matches the filterValue
            value && value.toString().toLowerCase() === filterValue.toLowerCase()
        )
      );
    }

    // Further filter by searchValue if not empty
    if (searchValue.trim() !== '') {
      data = data.filter(row =>
        Object.values(row).some(
          value => value && value.toString().toLowerCase().includes(searchValue.toLowerCase())
        )
      );
    }
    return data;
  }, [filterValue, searchValue]);
}
