import { exerciseLibraryActions } from '@/reducers/exercise-library';
import { useState, useEffect } from 'react';
import tw from 'twin.macro';

const PrescriptionTab = ({ items, state, dispatch }) => {
  const [selectedTabText, setSelectedTabText] = useState(state?.activePrescriptionTab);

  // Load selected tab from localStorage on component mount
  useEffect(() => {
    const savedTab = localStorage.getItem('selectedTabText');
    if (savedTab && items?.some(item => item.text === savedTab)) {
      setSelectedTabText(savedTab);
    } else if (items?.length > 0) {
      // If no saved tab or saved tab doesn't exist in items, use first item
      setSelectedTabText(items[0].text);
      localStorage.setItem('selectedTabText', items[0].text);
      dispatch({ type: exerciseLibraryActions.activePrescriptionTab, payload: items[0].id });
    }
  }, [items]);

  const handleClickOnTab = item => {
    setSelectedTabText(item.text);
    localStorage.setItem('selectedTabText', item.text);
    dispatch({ type: exerciseLibraryActions.activePrescriptionTab, payload: item.id });
  };

  return items?.map(item => (
    <div
      key={item.id}
      onClick={e => {
        handleClickOnTab(item);
        e.stopPropagation();
      }}
      tw="py-[4px] grid place-items-center border-border_stroke border-x bg-neutral-300 px-[10px]"
      css={
        selectedTabText === item.text
          ? tw`border-b-4 border border-b-[#2e2e2e] cursor-pointer font-semibold`
          : tw`opacity-50 font-semibold`
      }
    >
      <p>{item.text}</p>
    </div>
  ));
};

export default PrescriptionTab;
