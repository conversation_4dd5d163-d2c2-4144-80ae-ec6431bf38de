import { Fragment } from 'react';
import { useNavigate } from 'react-router-dom';
import tw from 'twin.macro';
/**
 * SidebarMenuItem Component
 *
 * A single menu item in the sidebar with hover effects and tooltips
 *
 * @param {Object} props Component props
 * @param {Object} props.item The menu item object containing id, title, icon, and route
 * @param {boolean} props.isCollapsed Whether the sidebar is collapsed
 * @param {boolean} props.isActive Whether this item is the active/selected route
 * @param {boolean} props.isSidebarHovered Whether the sidebar is currently being hovered
 * @param {boolean} props.isItemHovered Whether this specific item is being hovered
 * @param {Function} props.onHover Function to call when this item is hovered
 * @param {Boolean} props.isDirty for prevent navigation
 * @param {Function} props.onAttemptNavigation Function to call when confirm to leave page
 */
const SidebarMenuItem = ({
  item,
  isCollapsed,
  isActive,
  isSidebarHovered,
  isItemHovered,
  onHover,
  isDirty,
  onAttemptNavigation,
}) => {
  const navigate = useNavigate();
  const handleClick = e => {
    e.preventDefault();
    if (isDirty) {
      onAttemptNavigation(item.route);
    } else {
      navigate(item.route);
    }
  };

  return (
    <a
      href={item.route}
      onClick={handleClick}
      tw="py-[13px] relative cursor-pointer flex items-center gap-[12px] rounded-card"
      css={[
        !isCollapsed && (isActive ? tw`bg-[#4F4F4F] px-[12px] text-white` : tw`text-[#D7D7D7]`),
        !isCollapsed && !isActive && tw`px-[12px] hover:bg-[#1E1E1E] hover:text-white`,
      ]}
      onMouseEnter={() => onHover(item.id)}
    >
      <img src={item.icon} tw="w-[20px] h-[20px]" alt={item.title} />
      {!isCollapsed && <p tw="text-[0.9rem]">{item.title}</p>}
      {isCollapsed && isSidebarHovered && isItemHovered && (
        <Fragment>
          <div
            css={[
              tw`absolute top-1/2 transform z-50`,
              {
                left: '25px',
                animation: 'slideIn 0.3s ease forwards',
                '@keyframes slideIn': {
                  '0%': { opacity: 0, transform: 'translate(5px, -50%)' },
                  '100%': { opacity: 1, transform: 'translate(0, -50%)' },
                },
              },
            ]}
          >
            <svg
              width="14"
              height="28"
              viewBox="0 0 14 28"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M0 14C0 10 4 6 8 4L14 0V28L8 24C4 22 0 18 0 14Z" fill="#1E1E1E" rx="4" />
            </svg>
          </div>
          <div
            css={[
              tw`bg-[#1E1E1E] z-[100] text-[#FFFFFF] text-[0.7rem] font-[500] cursor-pointer rounded-[8px] w-fit p-2 absolute left-[30px]`,
              {
                whiteSpace: 'nowrap',
                animation: 'fadeIn 0.3s ease forwards',
                '@keyframes fadeIn': {
                  '0%': { opacity: 0, transform: 'translateX(10px)' },
                  '100%': { opacity: 1, transform: 'translateX(0)' },
                },
              },
            ]}
            onMouseEnter={() => onHover(item.id)}
          >
            {item.title}
          </div>
        </Fragment>
      )}
    </a>
  );
};

export default SidebarMenuItem;
