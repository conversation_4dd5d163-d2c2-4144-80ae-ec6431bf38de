import LoginImage from '@assets/images/login/Feature 1.webp';
import LoginImage2 from '@assets/images/login/Feature 2.webp';
import LoginImage3 from '@assets/images/login/Feature 3.webp';
import Logo from '@assets/svgs/auth/logo.svg';
import { useState } from 'react';
import { useContactFormModal } from '@/zustand/contact-form-modla';
import { useForm } from 'react-hook-form';
// import useFetchCountries from "@/hooks/use-fetch-countries";
import ThanksModal from '@/components/contactForm/thanksModal';
import AuthFooter from '@/components/auth/authFooter';
import AuthRedirectFooter from '@/components/auth/authSwitchPrompet';
import ContactForm from '@/components/contactForm/contactFormModal';
import LeftSideAuthorization from '@/components/auth/leftSideAuthorization';
import tw from 'twin.macro';

const AuthLayout = ({
  authSection,
  title,
  subtitle,
  hasAuthRedirectFooter,
  hasAuthFooter,
  redirectLink,
  redirectLinkText,
  redirectNormalText,
  footerLinkText,
  footerNormalText,
  aboveTitle,
  handelSubmit,
  parentRef,
  isLogIn = false,
  hideBorder,
  hasOpacity = true,
  customContainerStyle,
  authRedirectSection,
}) => {
  const [openThankstModal, setOpenThanksModal] = useState(false);
  const openContactForm = useContactFormModal(state => state.openContactForm);
  const changeOpenContactForm = useContactFormModal(state => state.changeOpenContactForm);
  // useFetchCountries();

  const handleCloseModal = () => {
    changeOpenContactForm(false);
  };

  const { register, reset } = useForm({
    defaultValues: { descriptions: '' },
  });
  const handleCLickSubmitButton = () => {
    setOpenThanksModal(true);
    changeOpenContactForm(false);
  };
  const handleCloseModals = () => {
    setOpenThanksModal(false);
    changeOpenContactForm(false);
    reset();
  };

  const circles = [{ id: 0 }, { id: 1 }, { id: 2 }];

  const authData = [
    {
      title: 'Precise Adherence Tracking',
      subtitle:
        'Track patient adherence in real time, capturing exercise completion and execution quality, with detailed video analysis and timestamps to pinpoint errors and provide corrective feedback.',
      backgroundImage: LoginImage,
    },
    {
      title: 'Outcome-Driven Progress Measurement',
      subtitle:
        'Track rehabilitation progress through data-rich insights that measure improvements in mobility, posture, and fatigue over time—enabling more informed decisions, better outcomes, and personalized adjustments to keep your recovery on course.',
      backgroundImage: LoginImage2,
    },
    {
      title: 'Seamless Patient Onboarding',
      subtitle:
        'Join the platform effortlessly through health record integration, QR codes, or manual entry—allowing instant access to your prescribed program and removing barriers to getting started on your rehab journey.',
      backgroundImage: LoginImage3,
    },
  ];

  return (
    <div tw="grid overflow-hidden grid-cols-1 h-screen md:grid-cols-2">
      {/* Left Side: Video feed / Image */}
      <LeftSideAuthorization circles={circles} data={authData} logoImageUrl={Logo} />
      <div tw="flex overflow-hidden flex-col h-screen bg-white">
        <form
          onSubmit={handelSubmit}
          ref={parentRef}
          tw="flex overflow-y-auto flex-1 justify-center items-center px-16"
        >
          {/* Add any content you want here */}
          <div tw="w-[75%]" css={[isLogIn ? tw`space-y-4` : tw`space-y-4`, customContainerStyle]}>
            {aboveTitle && aboveTitle}
            <h1 tw="font-bold text-[1.5rem] font-['Inter'] pb-[0.75rem]">{title}</h1>
            <span
              tw="text-[1.125rem] [line-height: 100%] font-[500] font-['Inter']"
              css={hasOpacity ? tw`opacity-50` : tw``}
            >
              {subtitle}
            </span>
            {authSection && authSection}
            {hasAuthRedirectFooter && (
              <div tw="space-y-3" css={[isLogIn ? tw`` : tw`pt-[0.75rem]`, authRedirectSection]}>
                <AuthRedirectFooter
                  link={redirectLink}
                  linkedText={redirectLinkText}
                  normalText={redirectNormalText}
                  hideBorder={hideBorder}
                />
              </div>
            )}
          </div>
        </form>
        {hasAuthFooter && (
          <AuthFooter
            onClick={() => changeOpenContactForm(true)}
            linkText={footerLinkText}
            normalText={footerNormalText}
          />
        )}
      </div>
      {openContactForm && (
        <ContactForm
          inLandingPage
          handleCloseModal={handleCloseModal}
          openContactModal={openContactForm}
          register={register}
          isAuth={false}
          handleSubmit={handleCLickSubmitButton}
        />
      )}
      {openThankstModal && (
        <ThanksModal
          openModal={openThankstModal}
          handleCLoseModal={handleCloseModals}
          handleBackButton={handleCloseModals}
        />
      )}
    </div>
  );
};

export default AuthLayout;
