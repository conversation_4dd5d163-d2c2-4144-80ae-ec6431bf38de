import tw from 'twin.macro';

const Model = ({ open, overLayStyle, children }) => {
  return (
    <div
      tw="transition-all duration-500 ease-in-out bg-[#00000040] fixed top-0 left-0 z-[10] w-screen h-screen overflow-hidden flex flex-col items-center justify-center"
      css={[!open && tw`opacity-0 pointer-events-none`, open && tw`bg-[#00000040]`, overLayStyle]}
    >
      {children}
    </div>
  );
};
export default Model;
