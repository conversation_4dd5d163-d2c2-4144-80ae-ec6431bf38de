import React, { useRef, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { truncateText } from '@/utils/helpers';
import FileUploaderIcon from '@assets/svgs/settings/file-uploader-icon.svg';
import 'twin.macro';

const acceptedTypes = [
  'application/pdf',
  'image/png',
  'image/jpeg',
  'image/jpg',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
];

const getFileIcon = type => {
  if (type.includes('pdf')) return '📄';
  if (type.includes('word')) return '📝';
  if (type.includes('excel')) return '📊';
  if (type.includes('text')) return '📊';
  return '📁';
};

const FileUploader = ({ maxFiles = 5 }) => {
  const [files, setFiles] = useState([]);
  const [error, setError] = useState('');
  const [previewImage, setPreviewImage] = useState(null);
  const inputRef = useRef();

  const handleFiles = fileList => {
    const newFiles = Array.from(fileList);
    const validFiles = [];
    let errorShown = false;

    newFiles.forEach(file => {
      if (acceptedTypes.includes(file.type)) {
        validFiles.push(file);
      } else if (!errorShown) {
        setError('Only PDF, Word, Excel, PNG, JPG, and JPEG files are allowed.');
        errorShown = true;
      }
    });

    if (validFiles.length) {
      setError('');
      if (maxFiles === 1) {
        setFiles([validFiles[0]]);
      } else {
        setFiles(prev => [...prev, ...validFiles]);
      }
    }
  };

  const onDrop = e => {
    e.preventDefault();
    handleFiles(e.dataTransfer.files);
  };

  const onFileChange = e => {
    handleFiles(e.target.files);
    e.target.value = null;
  };

  const removeFile = index => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const isImage = type => type.startsWith('image');

  const isSingleMode = maxFiles === 1 && files.length === 1;

  return (
    <div tw="">
      {!isSingleMode && (
        <div
          tw="p-8 text-center rounded-lg border transition cursor-pointer border-border_stroke hover:border-border_stroke"
          onClick={() => inputRef.current.click()}
          onDragOver={e => e.preventDefault()}
          onDrop={onDrop}
        >
          <div tw="flex flex-col justify-center items-center">
            <div tw="flex justify-center items-center mb-2 w-10 h-10 bg-gray-100 rounded-full">
              {/* <span tw="text-xl">⬆️</span> */}
              <img src={FileUploaderIcon} alt="file-uploader" />
            </div>
            <p tw="text-[#5E738A] font-normal text-[0.875rem]">
              <span tw="font-semibold text-blue-600">Click to upload</span> or drag and drop
            </p>
            <p tw="mt-1 text-[#5E738A] font-normal text-[0.875rem]">
              PDF, PNG, JPG, JPEG, Word, Excel (max. 800×400px)
            </p>
          </div>
          <input
            type="file"
            ref={inputRef}
            tw="hidden"
            multiple={maxFiles > 1}
            accept={acceptedTypes.join(',')}
            onChange={onFileChange}
          />
        </div>
      )}

      {error && <p tw="mt-2 text-sm text-red-500">{error}</p>}

      {files.length > 0 && (
        <div tw="grid grid-cols-1 gap-4 mt-4 sm:grid-cols-2">
          <AnimatePresence>
            {files.map((file, idx) => (
              <motion.div
                key={file.name + idx}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20, scale: 0.8 }}
                transition={{ duration: 0.3 }}
                whileHover={{ scale: 1.03, boxShadow: '0 4px 16px rgba(0,0,0,0.08)' }}
                tw="flex relative gap-3 items-center p-3 bg-gray-50 rounded-md border shadow-sm"
              >
                {isImage(file.type) ? (
                  <img
                    src={URL.createObjectURL(file)}
                    alt={file.name}
                    tw="object-cover w-16 h-16 rounded cursor-pointer"
                    onClick={() => setPreviewImage(URL.createObjectURL(file))}
                  />
                ) : (
                  <div tw="text-3xl">{getFileIcon(file.type)}</div>
                )}

                <div tw="flex-1">
                  <p tw="text-sm font-medium truncate">
                    {truncateText({ text: file.name, maxLength: 20 })}
                  </p>
                  <p tw="text-xs text-gray-500">{file.type}</p>
                </div>

                {maxFiles > 1 && (
                  <button
                    onClick={() => removeFile(idx)}
                    tw="absolute top-1 right-1 text-xs text-red-400 hover:text-red-600"
                  >
                    ✖
                  </button>
                )}
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Full screen image preview */}
      <AnimatePresence>
        {previewImage && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.25 }}
            tw="flex fixed inset-0 z-[999] justify-center items-center bg-black bg-opacity-80"
            onClick={() => setPreviewImage(null)}
          >
            <img src={previewImage} alt="Preview" tw="object-cover max-w-full max-h-full" />
            <button
              tw="absolute top-5 right-5 text-3xl font-bold text-white"
              onClick={e => {
                e.stopPropagation();
                setPreviewImage(null);
              }}
            >
              ✖
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
export default FileUploader;
