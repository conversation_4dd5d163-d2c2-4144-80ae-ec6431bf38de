import React, { useState, useRef } from 'react';
import tw, { css } from 'twin.macro';
import Notification from './notification';
import { useNavigate } from 'react-router-dom';
import notifications from '@/mock/notifications-data';
import useClickOutside from '@/hooks/useClickOutside';
import usePortalPosition from '@/hooks/usePortalPosition';
import { createPortal } from 'react-dom';
import NotificationInboxIcon from './notificationInboxIcon';

const NotificationInbox = () => {
  const [open, setOpen] = useState(false);
  const inboxRef = useRef(null);
  const iconRef = useRef(null);
  const navigate = useNavigate();

  // Use the ClickOutside custom hook
  useClickOutside([inboxRef, iconRef], () => setOpen(false));

  // Use the custom hook to calculate portal position
  const { top, right } = usePortalPosition(iconRef);

  const handleIconClick = () => {
    setOpen(prev => !prev);
  };

  return (
    <div css={tw`relative`}>
      {/* Notification Icon */}
      <NotificationInboxIcon ref={iconRef} onClick={handleIconClick} open={open} />
      {open &&
        createPortal(
          <div
            ref={inboxRef}
            style={{
              position: 'fixed',
              top: top,
              right: right,
              zIndex: 9999,
              width: '22.4vw',
              minWidth: 256,
            }}
            css={[
              tw`flex flex-col border border-stroke rounded-[0.6rem] bg-white`,
              css`
                box-shadow: 0px 14px 54px 0px rgba(0, 0, 0, 0.35);
              `,
            ]}
          >
            {/* header */}
            <div
              css={tw`px-[0.8rem] py-[1.6rem] font-semibold text-[0.7rem] border-b border-b-stroke`}
            >
              NOTIFICATIONS
            </div>
            {/* notifications list */}
            <div css={tw`max-h-[37.104vh] overflow-y-auto`} className="element">
              {notifications.map((notification, index) => {
                const isLast = index === notifications.length - 1; // Check if the current notification is the last one
                return (
                  <Notification
                    key={notification.id}
                    notification={notification}
                    isLast={isLast}
                    setOpen={setOpen}
                  />
                );
              })}
            </div>
            {/* see all button */}
            <div
              css={tw`rounded-b-[0.6rem] border-t border-t-stroke flex justify-center items-center p-[1.2rem] bg-neutral_50 text-[0.7rem] text-info font-semibold cursor-pointer hover:bg-neutral_100`}
              onClick={() => {
                setOpen(false);
                navigate('/notifications'); // Navigate to the notifications page
              }}
            >
              <p>See all notifications</p>
            </div>
          </div>,
          document.body
        )}
    </div>
  );
};

export default NotificationInbox;
