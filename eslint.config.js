import js from '@eslint/js';
import globals from 'globals';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import react from 'eslint-plugin-react';
import importPlugin from 'eslint-plugin-import';
import prettier from 'eslint-plugin-prettier';

export default [
  { ignores: ['dist'] },
  {
    files: ['**/*.{js,jsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
      parserOptions: {
        ecmaVersion: 'latest',
        ecmaFeatures: { jsx: true },
        sourceType: 'module',
      },
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      react: react,
      import: importPlugin,
      prettier: prettier,
    },
    rules: {
      ...js.configs.recommended.rules,
      ...reactHooks.configs.recommended.rules,
      'no-unused-vars': ['error', { varsIgnorePattern: '^[A-Z_]' }],
      'react-refresh/only-export-components': ['warn', { allowConstantExport: true }],
      // ⚛️ React
      'react/react-in-jsx-scope': 'off',
      'react/jsx-uses-react': 'off',
      'react/jsx-key': 'warn',
      'react/no-unescaped-entities': 'warn',
      'react/self-closing-comp': 'warn',

      // 🧠 React Hooks
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'warn',

      'no-multiple-empty-lines': ['warn', { max: 1 }],
      'eol-last': ['warn', 'always'],
      quotes: ['warn', 'single'],
      semi: ['warn', 'always'],
      indent: ['warn', 2],

      'no-undef': 'off',

      // 🧪 Debugging
      'no-console': ['warn', { allow: ['warn', 'error'] }],
      'no-debugger': 'warn',

      // 🧱 Import
      'import/order': 'off',

      eqeqeq: ['warn', 'always'],
      curly: 'warn',

      'react/jsx-curly-spacing': ['warn', { when: 'never', children: true }],
      'react/jsx-equals-spacing': ['warn', 'never'],
      'react/jsx-indent': ['warn', 2],
      'react/jsx-tag-spacing': ['warn', { beforeSelfClosing: 'always' }],

      // Prettier
      'prettier/prettier': 'warn',
    },
  },
];
