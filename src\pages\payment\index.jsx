import { useEffect, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import SeeFutureCharges from '@/components/payment/seeFutureCharges';
import PaymentNavbar from '@/components/payment/paymentNavbar';

import PaymentForm from '@/components/payment/paymentForm';
import { yupResolver } from '@hookform/resolvers/yup';
import { schemaPayment } from '@/components/payment/paymentForm/schema';
import { InitialPayment } from '@/components/payment/paymentForm/modules';
import 'twin.macro';

const Payment = () => {
  // ! payment section
  const [open, setOPen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState({
    id: 0,
    title: '$10/mo per member',
    badge: 'Monthly',
    subtitle: 'Cancel anytime, no fee',
  });
  const {
    register,
    control,
    getValues,
    setValue,
    watch,
    formState: { errors },
    handleSubmit,
    setError,
    clearErrors,
  } = useForm({
    defaultValues: InitialPayment,
    resolver: yupResolver(schemaPayment),
    mode: 'onChange',
  });
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'members',
  });

  const IncreaseValue = selectedPlan.id === 0 ? 10 : 120;
  const memberCount = fields?.length;
  const subtotal = memberCount * IncreaseValue + IncreaseValue;
  const discountPercentage = Number(watch('code')) || 0;

  const totalValue = subtotal - subtotal * (discountPercentage / 100);

  const handelCloseModel = () => {
    setOPen(false);
  };

  return (
    <div tw="overflow-hidden overflow-y-scroll w-screen h-[98%]" className="element">
      <PaymentNavbar />
      {/* {clientSecret && stripePromise && ( */}
      <PaymentForm
        IncreaseValue={IncreaseValue}
        append={append}
        control={control}
        errors={errors}
        fields={fields}
        getValues={getValues}
        handleSubmit={handleSubmit}
        memberCount={memberCount}
        register={register}
        remove={remove}
        setOPen={setOPen}
        setValue={setValue}
        subtotal={subtotal}
        totalValue={totalValue}
        watch={watch}
        setSelectedPlan={setSelectedPlan}
        selectedPlan={selectedPlan}
        setError={setError}
        clearErrors={clearErrors}
      />
      {/* )} */}
      <SeeFutureCharges
        IncreaseValue={IncreaseValue}
        open={open}
        selectedPlan={selectedPlan}
        totalValue={totalValue}
        handelCloseModel={handelCloseModel}
      />
    </div>
  );
};

export default Payment;
