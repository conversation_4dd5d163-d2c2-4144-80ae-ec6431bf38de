import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import SectionCards from '@/components/sectionsCard';
import viewMore from '@assets/svgs/settings/down-line.svg';
import PdfFile from '/sample-local-pdf.pdf';
import InvoiceHistoryCard from '../invoiceHistoryCard';
import tw from 'twin.macro';

const InvoiceHistory = () => {
  const [showAll, setShowAll] = useState(false);
  const invoices = new Array(6).fill('').map((_, index) => ({ id: index, date: 'Nov 25, 2023' }));

  return (
    <SectionCards customStyle={tw`p-[24px]`}>
      <h5 tw="text-[1.25rem] font-medium mb-[36px]">Invoice History</h5>

      {invoices.slice(0, 4).map(invoice => (
        <InvoiceHistoryCard key={invoice.id} date={invoice.date} pdfUrl={PdfFile} />
      ))}

      <AnimatePresence>
        {showAll &&
          invoices.slice(4).map(invoice => (
            <motion.div
              key={invoice.id}
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <InvoiceHistoryCard pdfUrl={PdfFile} date={invoice.date} />
            </motion.div>
          ))}
      </AnimatePresence>

      {invoices.length > 4 && (
        <div
          tw="flex items-center gap-[11px] cursor-pointer"
          onClick={() => setShowAll(prev => !prev)}
        >
          <img
            src={viewMore}
            alt={showAll ? 'Hide' : 'View more'}
            style={{ transform: showAll ? 'rotate(180deg)' : 'none', transition: 'transform 0.3s' }}
          />
          <p tw="font-medium text-[#285FF5]">{showAll ? 'Hide' : 'View More'}</p>
        </div>
      )}
    </SectionCards>
  );
};

export default InvoiceHistory;
