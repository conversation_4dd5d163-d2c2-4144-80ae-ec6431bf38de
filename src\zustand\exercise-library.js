import { prescriptionMode } from '@/constants/constants';
import { create } from 'zustand';

export const useExerciseLibrary = create((set, get) => ({
  mode: prescriptionMode.PRESCRIPTION, // prescription | template | edit
  openNotesView: false,
  EditTemplateData: [],
  setMode: value => set({ mode: value }), //setIsEditMode, isEditMode
  setEditTemplateData: value => {
    set({ EditTemplateData: value });
  },
  addEditTemplateData: value => {
    //add object
    const current = get().EditTemplateData;
    set({ EditTemplateData: [...current, ...value] });
  },
  setOpenNotesView: value => set({ openNotesView: value }),
}));
