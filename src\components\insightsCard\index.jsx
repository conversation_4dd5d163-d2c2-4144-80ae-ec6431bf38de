import InsightsIcon from "@assets/svgs/insights tooltip.svg";
import "twin.macro";

/**
 * A reusable card component for displaying insights with numeric data and optional tooltip
 *
 * @param {Object} props
 * @param {string} [props.title] - Optional title text for the insights card
 * @param {string} [props.text] - Optional descriptive text to display next to the number
 * @param {string|number} props.number - The primary numeric data to display
 * @param {boolean} [props.hasTooltip=false] - Whether to show an information tooltip icon
 * @param {Object} [props.containerStyle] - Optional custom styles for the container
 * @param {JSX.Element} [props.customTitle] - Optional custom title section
 * @param {JSX.Element} [props.customContent] - Optional custom content section
 *
 * @example
 * <InsightsCard
 *   title="Weekly Progress"
 *   number="78%"
 *   text="increase"
 *   hasTooltip={true}
 *   containerStyle={tw`custom-style`}
 *   customTitle={<div>custom title</div>}
 *   customContent={<div>custom content</div>}
 * />
 *
 * @returns {JSX.Element} A styled insights card component
 *
 * @styling
 * - Container: Full width with 24px padding
 * - Border: Default border with rounded corners
 * - Title: 1.05em size
 * - Number: 1.96em size, semi-bold (600), condensed line height (90%)
 * - Text: 1.18em size, default text color
 * - Layout: Grid layout with 10px gap between title and content
 * - Tooltip: Optional icon with cursor pointer
 * - Custom title section
 * - Custom content section
 *
 * @features
 * - Large numeric display
 * - Optional title section
 * - Optional descriptive text
 * - Information tooltip
 * - Custom container styling support
 * - Custom title section
 * - Custom content section
 */

const InsightsCard = ({
  title,
  text,
  number,
  hasTooltip,
  containerStyle,
  customTitle,
  customContent,
}) => {
  return (
    <div
      tw="w-full p-[24px] rounded-card border border-border_stroke grid"
      css={containerStyle}
    >
      {title && (
        <div tw="flex gap-2 items-center">
          <div tw="flex gap-2 items-center">
            <p tw="text-[1.05em]">{title}</p>
            {hasTooltip && (
              <img src={InsightsIcon} alt="insights-icon" tw="cursor-pointer" />
            )}
          </div>
        </div>
      )}
      {customTitle}
      {text ||
        (number && (
          <div tw="flex gap-2 items-center mt-[10px]">
            {number && (
              <p tw="text-[1.96em] font-[600] [line-height: 90%]">{number}</p>
            )}
            {text && <p tw="text-text_secondary text-[1.18em]">{text}</p>}
          </div>
        ))}
      {customContent}
    </div>
  );
};

export default InsightsCard;
