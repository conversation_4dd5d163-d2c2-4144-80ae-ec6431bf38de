import React, { useState, useRef } from 'react';
import { useCurrentEditor } from '@tiptap/react';
import PrimaryButton from '../../primaryButton';
import SecondaryButton from '../../secondaryButton';
import tw from 'twin.macro';
import boldIcon from '@assets/svgs/tiptap-editor/bold-icon.svg';
import italicIcon from '@assets/svgs/tiptap-editor/Italic-icon.svg';
import underlineIcon from '@assets/svgs/tiptap-editor/under-line-icon.svg';
import orderedListIcon from '@assets/svgs/tiptap-editor/polit-points.svg';
import linkIcon from '@assets/svgs/tiptap-editor/link-icon.svg';
import imageIcon from '@assets/svgs/tiptap-editor/image-icon.svg';

const MenuBar = ({ linkText, linkUrl, setLinkUrl, setLinkText }) => {
  const { editor } = useCurrentEditor();
  const [showLinkModal, setShowLinkModal] = useState(false);
  const fileInputRef = useRef(null);

  if (!editor) {
    return null;
  }

  const handleInsertLink = () => {
    if (linkUrl) {
      if (linkText) {
        editor
          .chain()
          .focus()
          .extendMarkRange('link')
          .setLink({ href: linkUrl })
          .insertContent(linkText)
          .run();
      } else {
        editor.chain().focus().extendMarkRange('link').setLink({ href: linkUrl }).run();
      }
      setLinkUrl('');
      setLinkText('');
      setShowLinkModal(false);
    }
  };

  const handleImageUpload = e => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = event => {
        const imageUrl = event.target.result;
        editor.chain().focus().setImage({ src: imageUrl }).run();
      };
      reader.readAsDataURL(file);
    }
  };

  const leftToolbarIconsList = [
    {
      id: 0,
      imageUrl: boldIcon,
      alt: 'bold',
      onClick: () => editor.chain().focus().toggleBold().run(),
      disabled: !editor.can().chain().focus().toggleBold().run(),
      style: tw`w-[10px]`,
    },
    {
      id: 1,
      imageUrl: italicIcon,
      alt: 'italic',
      onClick: () => editor.chain().focus().toggleItalic().run(),
      disabled: !editor.can().chain().focus().toggleItalic().run(),
      style: tw`w-[6px]`,
    },
    {
      id: 2,
      imageUrl: underlineIcon,
      alt: 'underline',
      onClick: () => editor.chain().focus().toggleUnderline().run(),
      disabled: !editor.can().chain().focus().toggleUnderline().run(),
      style: tw`w-[10px]`,
    },
    {
      id: 3,
      imageUrl: orderedListIcon,
      alt: 'orderedList',
      onClick: () => editor.chain().focus().toggleOrderedList().run(),
      disabled: !editor.can().chain().focus().toggleOrderedList().run(),
      style: tw`w-[14px]`,
    },
  ];

  const rightToolbarIconsList = [
    {
      id: 4,
      imageUrl: linkIcon,
      alt: 'link',
      onClick: () => setShowLinkModal(true),
      disabled: false,
    },
    {
      id: 5,
      imageUrl: imageIcon,
      alt: 'image',
      onClick: () => fileInputRef.current.click(),
      disabled: false,
    },
  ];

  return (
    <>
      <div tw="flex flex-wrap gap-2 justify-between mb-2 p-[16px] border border-gray-200 rounded-[10px]">
        <div tw="flex gap-6 items-center">
          {leftToolbarIconsList.map(icon => (
            <button key={icon.id} onClick={icon.onClick} disabled={icon.disabled}>
              <img src={icon.imageUrl} alt={icon.alt} css={icon.style} />
            </button>
          ))}
        </div>
        <div tw="flex gap-4">
          {rightToolbarIconsList.map(icon => (
            <button key={icon.id} onClick={icon.onClick} disabled={icon.disabled}>
              <img tw="w-[14px]" src={icon.imageUrl} alt={icon.alt} />
            </button>
          ))}
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleImageUpload}
            accept="image/*"
            tw="hidden"
          />
        </div>
      </div>

      {showLinkModal && (
        <div tw="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50">
          <div tw="p-6 w-96 bg-white rounded-lg shadow-lg">
            <h3 tw="mb-4 text-lg font-semibold">Insert Link</h3>
            <div tw="mb-4">
              <label tw="block mb-1 text-sm font-medium">Link Text</label>
              <input
                type="text"
                value={linkText}
                onChange={e => setLinkText(e.target.value)}
                tw="p-2 w-full rounded border border-gray-300"
                placeholder="Text to display"
              />
            </div>
            <div tw="mb-4">
              <label tw="block mb-1 text-sm font-medium">URL</label>
              <input
                type="text"
                value={linkUrl}
                onChange={e => setLinkUrl(e.target.value)}
                tw="p-2 w-full rounded border border-gray-300"
                placeholder="https://example.com"
              />
            </div>
            <div tw="grid grid-cols-2 gap-4 justify-end">
              <PrimaryButton otherStyle={tw`px-4 py-2`} text="Insert" onClick={handleInsertLink} />
              <SecondaryButton
                otherStyle={tw`px-4 py-2`}
                text="Cancel"
                onClick={() => {
                  setShowLinkModal(false);
                  setLinkUrl('');
                  setLinkText('');
                }}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default MenuBar;
