const defaultVideoUrl =
  'https://videocdn.cdnpk.net/videos/af92c376-7164-4c19-be8d-8ab222fa9beb/horizontal/previews/watermarked/small.mp4';

export const prescriptions = {
  patient_id: 1,
  data: [
    {
      id: 1,
      isActive: true,
      therapist: {
        id: 1,
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2023-05-10',
        'Last visit': '2024-04-05',
        phone_number: '0101234567891',
        'License Number': '00-**********',
        Role: 'Clinic Owner',
      },
      patient: {
        id: 1,
        Name: '<PERSON>',
        Status: 'High adherence',
        Prescription: 'Lower Back Pain',
        'Overall adherence': '74.3',
        Adherence: '76.3',
        'Pain level': '4 of 10',
        'Assigned PT': '<PERSON><PERSON> <PERSON>',
        'Email address': '<EMAIL>',
        'Date joined': '2023-05-10',
        'Last visit': '2024-04-05',
        'Overall period': '8 weeks',
        'Time left': '3 weeks left',
        'Expiration date': '2024-04-29',
      },
      prescription: {
        patient: { label: 'Patient Name', value: 1 },
        name: 'Prescription Name',
        start_date: '03/24/2022',
        end_date: '07/24/2022',
        frequency: {
          per_day: '1',
          per_week: '3',
        },
        duration: '8',
        eta: {
          number: '12',
          unit: 'min',
        },

        exercises: [
          {
            id: 'target-id-2',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-0',
            title: 'Arm Circles',
            image: '/exercise3.png',

            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-3',
            title: 'Arm Circles',
            image: '/exercise3.png',

            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-5',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-6',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-7',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-3',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-10',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
        ],
        notes: {
          general_note: {
            note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
            voice: {},
          },
          exercises_notes: {
            'target-id-0': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
            'target-id-1': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
            'target-id-2': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
          },
        },

        insights: [
          { title: 'Adherence', rate: '52.3%', bg_color: '#FFF1F2' },
          { title: 'Exercise hours', rate: '30.5', unit: 'Hours', bg_color: '#F8F9FA' },
          { title: 'Completed Sessions', rate: '19', unit: 'Sessions', bg_color: '#F8F9FA' },
          { title: 'Missed Sessions', rate: '5', unit: 'Sessions', bg_color: '#F8F9FA' },
          { title: 'Average Session Duration', rate: '10', unit: 'min', bg_color: '#F8F9FA' },
          { title: 'Consistency', rate: '3', unit: 'Consecutive Weeks', bg_color: '#F8F9FA' },
        ],
      },
    },
    {
      id: 5,
      isActive: true,
      therapist: {
        id: 1,
        first_name: 'Alice',
        last_name: 'Robert',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2023-05-10',
        'Last visit': '2024-04-05',
        phone_number: '0101234567891',
        'License Number': '00-**********',
        Role: 'Clinic Owner',
      },
      patient: {
        id: 2,
        Name: 'Robert Smith',
        Status: 'High adherence',
        Prescription: 'Lower Back Pain',
        'Overall adherence': '74.3',
        Adherence: '76.3',
        'Pain level': '4 of 10',
        'Assigned PT': 'E. Miller',
        'Email address': '<EMAIL>',
        'Date joined': '2023-05-10',
        'Last visit': '2024-04-05',
        'Overall period': '8 weeks',
        'Time left': '3 weeks left',
        'Expiration date': '2024-04-29',
      },
      prescription: {
        patient: { label: 'Patient Name', value: 1 },
        name: 'Prescription Name',
        start_date: '03/24/2022',
        end_date: '07/24/2022',
        frequency: {
          per_day: '1',
          per_week: '3',
        },
        duration: '8',
        eta: {
          number: '12',
          unit: 'min',
        },

        exercises: [
          {
            id: 'target-id-2',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-0',
            title: 'Arm Circles',
            image: '/exercise3.png',

            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-3',
            title: 'Arm Circles',
            image: '/exercise3.png',

            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-5',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-6',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-7',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-3',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-10',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
        ],
        notes: {
          general_note: {
            note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
            voice: {},
          },
          exercises_notes: {
            'target-id-0': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
            'target-id-1': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
            'target-id-2': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
          },
        },

        insights: [
          { title: 'Adherence', rate: '52.3%', bg_color: '#FFF1F2' },
          { title: 'Exercise hours', rate: '30.5', unit: 'Hours', bg_color: '#F8F9FA' },
          { title: 'Completed Sessions', rate: '19', unit: 'Sessions', bg_color: '#F8F9FA' },
          { title: 'Missed Sessions', rate: '5', unit: 'Sessions', bg_color: '#F8F9FA' },
          { title: 'Average Session Duration', rate: '10', unit: 'min', bg_color: '#F8F9FA' },
          { title: 'Consistency', rate: '3', unit: 'Consecutive Weeks', bg_color: '#F8F9FA' },
        ],
      },
    },
    {
      id: 2,
      isActive: false,
      therapist: {
        id: 1,
        first_name: 'Alice',
        last_name: 'Robert',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2023-05-10',
        'Last visit': '2024-04-05',
        phone_number: '0101234567891',
        'License Number': '00-**********',
        Role: 'Clinic Owner',
      },
      patient: {
        id: 3,
        Name: 'Sarah Davis',
        Status: 'High adherence',
        Prescription: 'Shoulder Strengthening',
        'Overall adherence': '77.8',
        Adherence: '78.9',
        'Pain level': '4 of 10',
        'Assigned PT': 'J. Harris',
        'Email address': '<EMAIL>',
        'Date joined': '2023-11-02',
        'Last visit': '2024-01-18',
        'Overall period': '10 weeks',
        'Time left': '6 weeks left',
        'Expiration date': '2024-03-01',
      },
      prescription: {
        patient: { label: 'Patient Name', value: 1 },
        name: 'Prescription Name',
        start_date: '03/24/2022',
        end_date: '07/24/2022',
        frequency: {
          per_day: '1',
          per_week: '3',
        },
        duration: '8',
        eta: '12 min',

        exercises: [
          {
            id: 'target-id-2',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-0',
            title: 'Arm Circles',
            image: '/exercise3.png',

            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-3',
            title: 'Arm Circles',
            image: '/exercise3.png',

            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-5',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-6',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-7',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-3',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-10',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
        ],
        notes: {
          general_note: {
            note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
            voice: {},
          },
          exercises_notes: {
            'target-id-0': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
            'target-id-1': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
            'target-id-2': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
          },
        },
        insights: [
          { title: 'Adherence', rate: '52.3%', bg_color: '#FFF1F2' },
          { title: 'Exercise hours', rate: '30.5', unit: 'Hours', bg_color: '#F8F9FA' },
          { title: 'Completed Sessions', rate: '19', unit: 'Sessions', bg_color: '#F8F9FA' },
          { title: 'Missed Sessions', rate: '5', unit: 'Sessions', bg_color: '#F8F9FA' },
          { title: 'Average Session Duration', rate: '10', unit: 'min', bg_color: '#F8F9FA' },
          { title: 'Consistency', rate: '3', unit: 'Consecutive Weeks', bg_color: '#F8F9FA' },
        ],
      },
    },
    {
      id: 3,
      isActive: false,
      therapist: {
        id: 1,
        first_name: 'Alice',
        last_name: 'Robert',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2023-05-10',
        'Last visit': '2024-04-05',
        phone_number: '0101234567891',
        'License Number': '00-**********',
        Role: 'Clinic Owner',
      },
      patient: {
        id: 3,
        Name: 'Sarah Davis',
        Status: 'High adherence',
        Prescription: 'Shoulder Strengthening',
        'Overall adherence': '77.8',
        Adherence: '78.9',
        'Pain level': '4 of 10',
        'Assigned PT': 'J. Harris',
        'Email address': '<EMAIL>',
        'Date joined': '2023-11-02',
        'Last visit': '2024-01-18',
        'Overall period': '10 weeks',
        'Time left': '6 weeks left',
        'Expiration date': '2024-03-01',
      },
      prescription: {
        patient: { label: 'Patient Name', value: 1 },
        name: 'Prescription Name',
        start_date: '03/24/2022',
        end_date: '07/24/2022',
        frequency: {
          per_day: '1',
          per_week: '3',
        },
        duration: '8',
        eta: '12 min',

        exercises: [
          {
            id: 'target-id-2',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-0',
            title: 'Arm Circles',
            image: '/exercise3.png',

            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-3',
            title: 'Arm Circles',
            image: '/exercise3.png',

            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-5',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-6',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-7',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-3',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-10',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
        ],
        notes: {
          general_note: {
            note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
            voice: {},
          },
          exercises_notes: {
            'target-id-0': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
            'target-id-1': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
            'target-id-2': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
          },
        },
        insights: [
          { title: 'Adherence', rate: '52.3%', bg_color: '#FFF1F2' },
          { title: 'Exercise hours', rate: '30.5', unit: 'Hours', bg_color: '#F8F9FA' },
          { title: 'Completed Sessions', rate: '19', unit: 'Sessions', bg_color: '#F8F9FA' },
          { title: 'Missed Sessions', rate: '5', unit: 'Sessions', bg_color: '#F8F9FA' },
          { title: 'Average Session Duration', rate: '10', unit: 'min', bg_color: '#F8F9FA' },
          { title: 'Consistency', rate: '3', unit: 'Consecutive Weeks', bg_color: '#F8F9FA' },
        ],
      },
    },
    {
      id: 4,
      isActive: false,
      therapist: {
        id: 1,
        first_name: 'Alice',
        last_name: 'Robert',
        image_url: '',
        'Email address': '<EMAIL>',
        'Date joined': '2023-05-10',
        'Last visit': '2024-04-05',
        phone_number: '0101234567891',
        'License Number': '00-**********',
        Role: 'Clinic Owner',
      },
      patient: {
        id: 3,
        Name: 'Sarah Davis',
        Status: 'High adherence',
        Prescription: 'Shoulder Strengthening',
        'Overall adherence': '77.8',
        Adherence: '78.9',
        'Pain level': '4 of 10',
        'Assigned PT': 'J. Harris',
        'Email address': '<EMAIL>',
        'Date joined': '2023-11-02',
        'Last visit': '2024-01-18',
        'Overall period': '10 weeks',
        'Time left': '6 weeks left',
        'Expiration date': '2024-03-01',
      },
      prescription: {
        patient: { label: 'Patient Name', value: 1 },
        name: 'Prescription Name',
        start_date: '03/24/2022',
        end_date: '07/24/2022',
        frequency: {
          per_day: '1',
          per_week: '3',
        },
        duration: '8',
        eta: '12 min',

        exercises: [
          {
            id: 'target-id-2',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-0',
            title: 'Arm Circles',
            image: '/exercise3.png',

            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-3',
            title: 'Arm Circles',
            image: '/exercise3.png',

            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-5',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-6',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-7',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-3',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
          {
            id: 'target-id-10',
            title: 'Arm Circles',
            image: '/exercise3.png',
            parameters: {
              sets: {
                title: 'Sets',
                number: 1,
              },
              reps: {
                title: 'Reps',
                number: 1,
              },
            },
          },
        ],
        notes: {
          general_note: {
            note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
            voice: {},
          },
          exercises_notes: {
            'target-id-0': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
            'target-id-1': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
            'target-id-2': {
              note: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur ante elit, facilisis a.',
              voice: {},
            },
          },
        },
        insights: [
          { title: 'Adherence', rate: '52.3%', bg_color: '#FFF1F2' },
          { title: 'Exercise hours', rate: '30.5', unit: 'Hours', bg_color: '#F8F9FA' },
          { title: 'Completed Sessions', rate: '19', unit: 'Sessions', bg_color: '#F8F9FA' },
          { title: 'Missed Sessions', rate: '5', unit: 'Sessions', bg_color: '#F8F9FA' },
          { title: 'Average Session Duration', rate: '10', unit: 'min', bg_color: '#F8F9FA' },
          { title: 'Consistency', rate: '3', unit: 'Consecutive Weeks', bg_color: '#F8F9FA' },
        ],
      },
    },
  ],
};

export const mapPrescriptionToExerciseLibraryView = presc => {
  const { prescription } = presc;

  return {
    id: presc.id,
    prescription_name: prescription.name,
    frequency_day: Number(prescription.frequency.per_day),
    frequency_week: Number(prescription.frequency.per_week),
    duration: prescription.duration,
    patients: [prescription.patient],
    startDate: new Date(prescription.start_date),
    generalMessage: prescription.notes.general_note,
    exerciseMessages: prescription.notes.exercises_notes,
  };
};

export const mappedExerciseToPrescriptionView = presc => {
  const { prescription } = presc;
  return prescription.exercises.map(exercise => ({
    id: exercise.id,
    title: exercise.title,
    isDublicate: false,
    isFavorite: true,
    imgUrl: '/exercise3.png',
    videoUrl: defaultVideoUrl,
    sets: exercise.parameters?.sets?.number ?? 0,
    reps: exercise.parameters?.reps?.number ?? 0,
  }));
};

export const getPrescriptionById = id => {
  return prescriptions.data.find(prescription => prescription.id === id);
  // return mapPrescriptionToExerciseLibraryView(prescription);
};
