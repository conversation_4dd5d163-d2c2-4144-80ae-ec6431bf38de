import Menue from '@/components/shared/menue';

const PatientActions = ({ handleOpen }) => {
  return (
    <>
      <Menue
        options={[
          {
            id: 1,
            label: 'Transfer patient',
            value: 'Transfer patient',
            customClickHandler: () => handleOpen('transfer'),
          },
          {
            id: 2,
            label: 'Discharge patient',
            value: 'Discharge patient',
            customClickHandler: () => handleOpen('discharge'),
          },
          {
            id: 3,
            label: 'ٌRemove patient',
            value: 'ٌRemove patient',
            customClickHandler: () => handleOpen('delete'),
          },
        ]}
      />
    </>
  );
};

export default PatientActions;
