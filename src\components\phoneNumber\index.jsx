import PhoneInput from '../shared/phoneInput';
import AuthInput from '../auth/authInput';
import tw from 'twin.macro';
import { useState } from 'react';

const PhoneNumber = ({
  errors,
  control,
  register,
  name,
  placeholder,
  phoneName,
  defaultCountry,
  label,
  wrapperElementClasses,
  inputCalsses,
  lableClasses,
  disable,
}) => {
  const errorStyle = tw`border-[rgba(209, 46, 60, 1)]! bg-[#FFF1F2] border transition-all duration-1000 font-['Inter'] w-full rounded-[6px]`;
  const [isOpenMenu, setIsOpenMenu] = useState(false);
  const [focus, setFocus] = useState(false);

  return (
    <div tw="h-full">
      <div tw="flex flex-col gap-1 h-full" css={wrapperElementClasses}>
        <label tw="font-[500] text-[0.95rem] font-['Inter']" css={lableClasses}>
          {label}
        </label>
        <div
          tw="flex  w-full h-full border hover:bg-neutral_50  transition-all duration-300  rounded-[6px]"
          onFocus={() => setFocus(true)}
          onBlur={() => setFocus(false)}
          css={[
            focus || isOpenMenu ? tw`border-Primary_600` : tw`border-border_stroke`,
            errors && errorStyle,
          ]}
        >
          <div tw="w-[6em] flex items-end ">
            <PhoneInput
              control={control}
              defaultCountry={defaultCountry || 'EG'}
              name={phoneName}
              errorMessage={errors}
              isOpenMenu={isOpenMenu}
              setIsOpenMenu={setIsOpenMenu}
              inputCalsses={inputCalsses}
              disabled={disable}
              disabledStyle={tw`bg-disable cursor-not-allowed hover:bg-disable`}
            />
          </div>
          <div tw="w-[100%] h-full">
            <AuthInput
              name={name}
              placeholder={placeholder}
              register={register}
              type={'number'}
              hideErrorMessage
              showArrow={false}
              containerSTyle={tw`rounded-s-[0px] border-s-0 border-none`}
              inputCalsses={inputCalsses}
              disabled={disable}
              disabledStyle={tw`bg-disable cursor-not-allowed hover:bg-disable`}
            />
          </div>
        </div>
      </div>
      {errors && (
        <span tw="pt-2 text-sm font-['Inter']" style={{ color: '#D12E3C' }}>
          {errors}
        </span>
      )}
    </div>
  );
};

export default PhoneNumber;
