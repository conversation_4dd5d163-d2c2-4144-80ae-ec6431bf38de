import 'twin.macro';
import React from 'react';
import ToggleSwitch from '@/components/shared/toggleSwitch';

const AlertsCard = ({ title, subtitle, enabled, onToggle }) => {
  return (
    <div tw="flex justify-between items-center rounded mt-[20px]">
      <div>
        <p tw="font-semibold">{title}</p>
        <p tw="font-normal text-text_secondary">{subtitle}</p>
      </div>
      <ToggleSwitch isActive={enabled} onToggle={onToggle} />
    </div>
  );
};

export default AlertsCard;
