import tw from 'twin.macro';
import GenericSelect from '@components/shared/select';
import Input from '@components/shared/input';
import PhoneNumber from '@/components/phoneNumber';

function AddStaff({ register, errors, touchedFields, control }) {
  return (
    <div css={tw`p-8 pt-4 flex flex-col gap-6 `}>
      {/*firstName & lastName*/}
      <div css={tw`flex gap-5`}>
        <div css={tw`flex-1`}>
          <Input
            type="text"
            label="First name"
            name="firstName"
            placeholder="First Name"
            register={register}
            errorMessage={
              errors.firstName && touchedFields.firstName ? errors.firstName.message : ''
            }
            inputStye={tw`rounded-[6px] focus:outline-none focus:ring-2 focus:ring-Primary`}
          />
        </div>
        <div css={tw`flex-1`}>
          <Input
            type="text"
            label="Last name"
            name="lastName"
            placeholder="Last Name"
            register={register}
            errorMessage={errors.lastName && touchedFields.lastName ? errors.lastName.message : ''}
            inputStye={tw`rounded-[6px] focus:outline-none focus:ring-2 focus:ring-Primary`}
          />
        </div>
      </div>
      {/*phoneNumber & emailAddress*/}
      <div css={tw`w-full flex gap-5 items-start`}>
        {/*emailAddress*/}
        <div css={tw`flex-1`}>
          <Input
            type="text"
            label={<>Email address</>}
            name="email"
            placeholder="<EMAIL>"
            register={register}
            errorMessage={errors.email?.message}
            inputStye={tw`h-[2.933rem] rounded-[6px] focus:outline-none focus:ring-2 focus:ring-Primary`}
          />
        </div>
        <div css={tw`flex-1 flex flex-col gap-1`}>
          <PhoneNumber
            control={control}
            register={register}
            name="localNumber"
            placeholder="000-000-000"
            phoneName="countryCode"
            defaultCountry="EG"
            label="Phone number"
            errors={
              errors.localNumber && touchedFields.localNumber ? errors.localNumber.message : ''
            }
            inputType="text"
            hideErrorMessage
            inputCustomStyle={tw`h-[2.98rem]`}
          />
          {/* {errors.localNumber && touchedFields.localNumber && (
            <span css={tw`text-error text-sm`}>{errors.localNumber.message}</span>
          )} */}
        </div>
      </div>
      {/*Role */}
      <div css={tw`w-full flex gap-5`}>
        <div css={tw`flex-1`}>
          <GenericSelect
            control={control}
            label="Role"
            name="role"
            placeholder="Select Role"
            options={[
              { label: 'Therapist', value: 'Therapist' },
              { label: 'Admin', value: 'Admin' },
            ]}
            errorMessage={errors.role && touchedFields.role ? errors.role.message : ''}
            labelStyle={tw`font-medium text-[0.95rem]`}
            labelContainerStyle={tw`mb-0`}
            customStyles={{
              control: () => ({
                padding: '8px',
              }),
            }}
            inputLength={7}
          />
        </div>
      </div>
      {/* License number  */}
      <div css={tw`flex-1`}>
        <Input
          type="text"
          label={
            <>
              License number{' '}
              <span css={tw`text-text_tertiary font-light text-[13px]`}>(optional)</span>
            </>
          }
          name="licenseNumber"
          placeholder="00-**********"
          register={register}
          errorMessage={
            errors.licenseNumber && touchedFields.licenseNumber ? errors.licenseNumber.message : ''
          }
          inputStye={tw`rounded-md focus:outline-none focus:ring-2 focus:ring-Primary`}
        />
      </div>
    </div>
  );
}

export default AddStaff;
