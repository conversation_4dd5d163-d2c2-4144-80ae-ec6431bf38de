import React, { useEffect, useState } from 'react';
import MainPageHeader from '@/components/mainPageHeader';
import SectionCards from '@/components/sectionsCard';
import Timeline from '@/components/timeLine';
import TimeLineLoading from '@/components/timeLine/timeLineLoading';
import TimeLineEmpty from '@/components/timeLine/timeLineEmpty';
import tw from 'twin.macro';
import useLatestNotificationsStore from '@/zustand/notifications/latest-notifications-store';
import useOlderNotificationsStore from '@/zustand/notifications/older-notifications-store';

const Notifications = () => {
  const latestHead = useLatestNotificationsStore(state => state.head);
  const olderHead = useOlderNotificationsStore(state => state.head);
  const [emptyNotifications, setEmptyNotifications] = useState(false);
  const [loading, setLoading] = useState(latestHead === 0 && olderHead === 0);

  // Get fetch functions from stores
  const fetchLatestNotifications = useLatestNotificationsStore(state => state.fetchNotifications);
  const fetchOlderNotifications = useOlderNotificationsStore(state => state.fetchNotifications);

  // Fetch all Notifications on mount and set loading to false when done
  useEffect(() => {
    if (latestHead === 0 && olderHead === 0) {
      setLoading(true);
      Promise.all([fetchLatestNotifications(), fetchOlderNotifications()]).then(() => {
        setLoading(false);
        if (
          (useLatestNotificationsStore.getState().items?.length ?? 0) === 0 &&
          (useOlderNotificationsStore.getState().items?.length ?? 0) === 0
        ) {
          setEmptyNotifications(true);
        } else {
          setEmptyNotifications(false);
        }
      });
    }
  }, [fetchLatestNotifications, fetchOlderNotifications, latestHead, olderHead]);

  return (
    <div css={tw`max-h-full overflow-y-auto flex flex-col px-[1.5rem]`} className="element">
      <div tw="sticky top-0 z-10 bg-neutral_50">
        <MainPageHeader title="Notifications" />
      </div>
      {loading ? (
        <div tw="flex flex-col gap-[3rem] mt-[1.5rem]">
          {Array(2)
            .fill()
            .map(index => (
              <TimeLineLoading key={index} />
            ))}
        </div>
      ) : emptyNotifications ? (
        <div tw="w-full h-[65vh]">
          <TimeLineEmpty
            label="No notifications available"
            subtitle="It’s a little quiet here. Your notifications will pop up as soon as there’s something new."
          />
        </div>
      ) : (
        <>
          <SectionCards customStyle={tw`p-[24px] mt-[1rem] mb-[1.5rem]`}>
            <Timeline
              label="Latest"
              useStore={useLatestNotificationsStore}
              emptyLabel="Nothing to see here"
              emptySubtitle="There've been no notifications recently. Check back later."
            />
          </SectionCards>
          {/* Divider */}
          <div css={tw`h-[0.0625rem] border-b border-b-stroke`} />
          <SectionCards customStyle={tw`p-[24px] my-[1.5rem]`}>
            <Timeline
              label={'Older'}
              useStore={useOlderNotificationsStore}
              emptyLabel="Nothing to see here"
              emptySubtitle="There've been no notifications lately."
            />
          </SectionCards>
        </>
      )}
    </div>
  );
};

export default Notifications;
