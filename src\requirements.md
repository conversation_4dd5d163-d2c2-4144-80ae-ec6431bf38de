# Most frontend library used : 
 
## media pipe (task vision, hand): 

@mediapipe/tasks-vision 

https://ai.google.dev/edge/mediapipe/solutions/vision/pose_landmarker/web_js 
 
## react query: 
@tanstack/react-query 
 
https://tanstack.com/query/latest 
 
## axios: 

https://www.npmjs.com/package/axios 

## vite: 

https://vite.dev/guide/ 
 
## chart js: 

https://www.chartjs.org/ 
 
https://react-chartjs-2.js.org/ 
 
## localforage (saving data in browser database): 
 
https://localforage.github.io/localForage/ 
 

## lottie-react (animation svgs): 
 
https://www.npmjs.com/package/lottie-react 
 
##  react-select: 
https://www.npmjs.com/package/react-select 
 
## react-toastify: 

https://www.npmjs.com/package/react-toastify 
 
## react toggle: 
https://www.npmjs.com/package/react-toggle 
 
## react-use-precision-timer (timer): 
 
https://www.npmjs.com/package/react-use-precision-timer 
 
##  react-use-websocket: 
https://www.npmjs.com/package/react-use-websocket 
 
##  react-webcam: 
https://www.npmjs.com/package/react-webcam 
 
##  tailwind: 

https://tailwindcss.com/ 
 
## twin.macro (tailwind freamwork): 
https://www.npmjs.com/package/twin.macro/v/1.0.0 
 
## usehooks-ts: 
https://usehooks-ts.com/ 
 
## yup: 
https://www.npmjs.com/package/yup 
 
## zustand: 
https://github.com/pmndrs/zustand 

 
## react-google-recaptcha: 
https://www.npmjs.com/package/react-google-recaptcha 
 
## react-hook-form: 
https://www.react-hook-form.com/ 

 
## framer-motion: 

https://www.npmjs.com/package/framer-motion 

 

## react-h5-audio-player: 

https://www.npmjs.com/package/react-h5-audio-player 
 
## react-intersection-observer: 
https://www.npmjs.com/package/react-intersection-observer 
 
## react-router-dom: 
https://www.npmjs.com/package/react-router-dom 
 
## react-spinners: 
https://www.npmjs.com/package/react-spinners 
 
## i18n-iso-countries: 
https://www.npmjs.com/package/i18n-iso-countries 
 
## moment.js: 
https://momentjs.com/ 
 
## react-big-calendar: 
https://jquense.github.io/react-big-calendar 
 
- react-country-flag: 
https://www.npmjs.com/package/react-country-flag 
 
## react-phone-number-input: 
https://www.npmjs.com/package/react-phone-number-input 
 
## styled-components: 
https://styled-components.com/ 
 
 
## tiptap (editor): 
https://tiptap.dev/ 
 
## tanstack/react-table: 
 
https://tanstack.com/table/latest 
 
 
## drag and drop (dndkit): 

https://dndkit.com/ 

 

## Canvas API  

https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API 

## Media Device 

https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices 

 