import reportedPain from '@assets/svgs/patient/reported-pain.svg';
import feedback from '@assets/svgs/patient/feedback.svg';
import DayPerformance from '../../DayPerformance';
import tw from 'twin.macro';
import 'twin.macro';

const DayStatus = ({ item }) => {
  return (
    <div
      tw="h-full flex flex-col  pb-2 justify-end min-w-[2.5rem]"
      css={[item.haveReportedPain || item.haveFeedback ? tw`` : tw`mb-5`]}
    >
      <DayPerformance status={item.status} performed={item.performed} total={item.total} />
      <div tw="flex gap-1">
        {item.haveReportedPain && <img src={reportedPain} />}
        {item.haveFeedback && <img src={feedback} />}
      </div>
    </div>
  );
};

export default DayStatus;
