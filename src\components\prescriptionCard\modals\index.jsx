import ActionModal from '@/components/actionModal';
import PrescriptionModal from '@/components/exerciseLibrary/prescriptionModal';
import SaveTemplateModal from '@/components/exerciseLibrary/saveTemplateModal';
import Menu from '@/components/menu';

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import PatientPrescriptionModal from './patientPrescriptionModal';

const Modals = ({ id, prescription, isActive, editPerscriptionBase }) => {
  const [openMenu, setOpenMenu] = useState(false);
  const [openPrescriptionModal, setOpenPrescriptionModal] = useState(false);
  const [openSaveModal, setOpenSaveModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const navigate = useNavigate();

  return (
    <>
      {openPrescriptionModal && (
        <PatientPrescriptionModal
          openPrescriptionModal={openPrescriptionModal}
          setOpenPrescriptionModal={setOpenPrescriptionModal}
          prescription={prescription}
        />
      )}

      {openSaveModal && (
        <SaveTemplateModal
          handelCloseMode={() => setOpenSaveModal(false)}
          open={openSaveModal}
          navigateTo={'/exercise-library?tab=templates'}
        />
      )}
      {openDeleteModal && (
        <ActionModal
          open={openDeleteModal}
          handleClose={() => setOpenDeleteModal(false)}
          title={'Delete prescription'}
          primaryActionHandler={() => setOpenDeleteModal(false)}
          description={
            'Are you sure you want to delete this prescription? This action cannot be undone.'
          }
        />
      )}
      <Menu
        width="w-full"
        options={[
          isActive && {
            id: 1,
            text: 'Edit in Exercise Library',
            onClick: () => {
              editPerscriptionBase();
              navigate('/exercise-library', { state: id });
            },
          },
          {
            id: 2,
            text: 'Reassign Prescription',
            onClick: () => {
              setOpenPrescriptionModal(true);
              setOpenMenu(false);
            },
          },
          {
            id: 3,
            text: 'Save as template',
            onClick: () => {
              setOpenSaveModal(true);
              setOpenMenu(false);
            },
          },
          {
            id: 4,
            text: 'Create Printable PDF',
            onClick: () => console.log('Create Printable PDF'),
          },
          {
            id: 5,
            text: 'Delete Prescription',
            onClick: () => {
              setOpenDeleteModal(true);
              setOpenMenu(false);
            },
          },
        ].filter(Boolean)}
        handelClose={() => setOpenMenu(false)}
        disable={false}
        openMenu={openMenu}
        handleOpenMenu={() => setOpenMenu(true)}
      />
    </>
  );
};

export default Modals;
