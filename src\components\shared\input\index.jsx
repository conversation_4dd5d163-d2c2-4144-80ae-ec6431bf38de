import { useState, useEffect } from 'react';
import ArrowUp from '@assets/svgs/auth/input-arrow.svg';
import tw, { css } from 'twin.macro';
import LabelWithTooltip from '@/components/labelWithTooltip';

const Input = ({
  name,
  register,
  placeholder,
  errorMessage,
  type = 'string',
  firstIcon,
  lsatIcon,
  inputStye,
  containerSTyle,
  onBlur,
  inputRef,
  max,
  defaultValue = '',
  validation,
  hideErrorMessage = false,
  showArrow = true,
  label,
  tooltipText,
  hasTooltip,
  labelStyle,

  rootElementStyle,
  disabledStyle,
  disabled,
  labelRootElementStyle,
  min,

  ...rest
}) => {
  const [focus, setFocus] = useState(false);
  const registerResult = register(name, validation);

  return (
    <div
      ref={inputRef}
      css={[
        css`
          ${rootElementStyle}
        `,
      ]}
    >
      {label && (
        <LabelWithTooltip
          label={label}
          tooltipText={tooltipText}
          hasTooltip={hasTooltip}
          labelStyle={labelStyle}
          containerStyle={labelRootElementStyle}
        />
      )}
      <div
        tw="flex items-center hover:bg-neutral_50 border-border_stroke overflow-hidden transition-all duration-300 border rounded-[6px]"
        style={{ background: errorMessage ? '#FFF1F2' : '' }}
        css={[
          css`
            ${containerSTyle} !important
          `,
          errorMessage
            ? tw`border-error`
            : focus
              ? tw`border-Primary_600`
              : tw`border-border_stroke`,
          disabled && disabledStyle,
        ]}
      >
        {firstIcon}
        <input
          name={name}
          ref={registerResult.ref}
          css={[
            tw`w-full focus:border-[none] focus:outline-none bg-inherit outline-0 text-sm px-[16px] py-[12px]`,
            inputStye,
            errorMessage && tw`bg-error_50`,
          ]}
          placeholder={placeholder}
          type={type}
          onFocus={() => setFocus(true)}
          onBlur={e => {
            registerResult.onBlur(e);
            if (onBlur) {
              onBlur(e);
            }
            setFocus(false);
          }}
          min={min}
          max={max}
          onChange={registerResult.onChange}
          disabled={disabled}
          {...rest}
        />
        {lsatIcon ? (
          lsatIcon
        ) : type === 'number' && showArrow ? (
          <div tw="flex flex-col h-full gap-2 px-3 py-3 border-l-[1px] border-stroke">
            <img
              src={ArrowUp}
              alt="arrow-up"
              onClick={() => {
                const input = document.querySelector(`input[name='${name}']`);
                if (input) input.value = Number(input.value || 0) + 1;
                input && input.dispatchEvent(new Event('input', { bubbles: true }));
              }}
            />
            <img
              tw="rotate-180"
              src={ArrowUp}
              alt="arrow-up"
              onClick={() => {
                const input = document.querySelector(`input[name='${name}']`);
                if (input) input.value = Number(input.value || 0) - 1;
                input && input.dispatchEvent(new Event('input', { bubbles: true }));
              }}
            />
          </div>
        ) : (
          <></>
        )}
      </div>
      {!hideErrorMessage && (
        <span tw="mt-1 text-sm font-['Inter']" style={{ color: '#D12E3C' }}>
          {errorMessage}
        </span>
      )}
    </div>
  );
};
export default Input;
