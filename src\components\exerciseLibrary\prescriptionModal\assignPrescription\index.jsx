import React, { useState } from 'react';
import { calculateEndDate } from '@/utils/helpers';
import InfoIcon from '@assets/svgs/info-icon.svg';
import { ModalViews } from '..';
import PatientSelector from './patientSelector';
import tw from 'twin.macro';
import ControlledCalendar from '@/components/controlledCalendar';
import MessagesList from './messagesList';

const AssignPrescription = ({
  errors,
  control,
  watch,
  patientsOptions,
  weeksCount = 1,
  setModalView,
  exercises,
}) => {
  const [hideMessages, setHideMessages] = useState(false);

  const startDate = watch('startDate');
  const year = new Date().getFullYear();
  const exerciseTitles = exercises.reduce((acc, exercise) => {
    acc[exercise.id] = exercise.title;
    return acc;
  }, {});
  // Access the form state
  const generalMessage = watch('generalMessage');
  const exerciseMessages = watch('exerciseMessages');
  // Helper function to check if we should render a message
  const isRenderable = message => {
    if (!message || typeof message !== 'object') {
      return false; // Null or not an object
    }
    if (Object.keys(message).length === 0) {
      return false; // Empty object
    }
    return Object.values(message).some(value => Boolean(value)); // At least one truthy value
  };

  // Combine general message and exercise messages into one array
  const messages = [
    ...(isRenderable(generalMessage) ? [{ type: 'general', data: generalMessage }] : []), // Include generalMessage only if we should render it
    ...Object.entries(exerciseMessages || {})
      .filter(([_, message]) => isRenderable(message)) // Filter out invalid exercise messages
      .map(([exerciseId, message]) => ({
        type: 'exercise',
        id: exerciseId,
        data: message,
        title: `Exercise ${exerciseTitles[exerciseId]}`, // Add a title for each exercise message
      })),
  ];

  return (
    <div
      css={[
        tw`p-6 flex flex-col gap-3`,
        messages.length > 0 && tw`max-h-[24.37rem] overflow-y-auto`,
        hideMessages && tw`overflow-visible`,
      ]}
    >
      {/* patients selection */}
      <PatientSelector
        control={control}
        errors={errors}
        patientsOptions={patientsOptions}
        setModalView={setModalView}
      />
      {/* prescription start & end dates */}
      <div css={tw`w-full flex gap-[24px]`}>
        {/* start Date */}
        <div css={tw`flex-1`}>
          <ControlledCalendar
            name="startDate"
            control={control}
            datePickerProps={{
              label: 'Start date',
              labelClassName: '!text-[16px] !text-black !mb-[10px]',
              dateFormat: {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              },
              locale: 'en-GB',
              calendarClassName: '-bottom-[60%] left-[calc(100%+8px)] !w-[110%]',
              minDate: new Date(),
              yearRange: {
                start: year,
                end: year + 1,
              },
              onOpenChange: value => {
                setHideMessages(value);
              },
            }}
            errors={errors}
          />
        </div>
        {/* end date */}
        <div css={tw`flex-1 flex flex-col gap-[10px]`}>
          <p css={tw`font-medium text-[16px] text-black`}>End date</p>
          <div
            css={tw`px-4 py-3 border border-border_stroke rounded-[6px] bg-neutral_300 text-text_secondary font-medium text-[14px] cursor-default hover:bg-neutral_100`}
          >
            {calculateEndDate(startDate, weeksCount)}
          </div>
        </div>
      </div>
      {/* pre-session messages */}
      <div css={tw`w-full flex flex-col gap-3 mt-3`}>
        <p css={tw`font-medium text-[16px] text-black`}>Pre-session messages</p>
        <div css={tw`flex gap-3 h-[54px]`}>
          <div
            css={tw`flex-1 flex flex gap-2 py-[10px] px-[12px] bg-info_light rounded-[6px] cursor-default`}
          >
            <img src={InfoIcon} alt="info icon" css={tw`w-4 h-4`} />
            <p css={tw`text-[13px] font-medium`}>
              Adding pre-session messages will provide your patients with guidance prior to the
              exercises you have prepared for them
            </p>
          </div>
          <button
            css={tw`border border-Primary_800 rounded-[6px] px-[12px] py-[10px] bg-Primary_100 text-Primary_800 font-medium text-[15px]`}
            onClick={() => {
              setModalView(ModalViews.PRE_SESSION_MESSAGES);
            }}
          >
            {messages.length > 0 ? 'Edit messages' : 'Add messages'}
          </button>
        </div>
      </div>
      {/* Messages list */}
      {!hideMessages && <MessagesList messages={messages} />}
    </div>
  );
};

export default AssignPrescription;
