import AddFilter from '@assets/svgs/exercise-library/add-filter.svg';
import { exerciseLibraryActions } from '@/reducers/exercise-library';
import FilterItem from '../filterItem';
import Input from '@/components/shared/input';
import tw from 'twin.macro';

const FilterItemsContainer = ({
  filterItems,
  control,
  watch,
  setValue,
  register,
  state,
  dispatch,
  setSearchParams,
  onDelete,
}) => {
  return (
    <div>
      {filterItems.map(item => (
        <FilterItem
          key={item.id}
          item={item}
          control={control}
          watch={watch}
          setValue={setValue}
          filterItems={filterItems}
          register={register}
          state={state}
          onDelete={onDelete}
          dispatch={dispatch}
        />
      ))}
      {state.editFilterId === null &&
        (state.isEditFilter ? (
          <Input
            name="filter_custom_name"
            defaultValue={''}
            inputStye={tw`px-2 py-1 text-sm`}
            autoFocus
            register={register}
          />
        ) : (
          <div
            onClick={() => {
              dispatch({
                type: exerciseLibraryActions.openCollapseModel,
                payload: false,
              });
              setSearchParams({ tab: 'exercises' });
              dispatch({ type: exerciseLibraryActions.isEditFilter, payload: true });
              localStorage.setItem('isEdit', true);
            }}
            tw="text-[0.75rem] gap-2 w-full rounded-[4px] cursor-pointer py-[6px] px-3 flex items-center hover:bg-Primary_100 transition-all duration-300"
          >
            <img src={AddFilter} alt="add" />
            <p tw="text-black">Create New List</p>
          </div>
        ))}
    </div>
  );
};

export default FilterItemsContainer;
