{"name": "rehabitair-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^5.0.1", "@stripe/react-stripe-js": "^3.7.0", "@tailwindcss/postcss": "^4.1.7", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-document": "^2.23.0", "@tiptap/extension-dropcursor": "^2.23.0", "@tiptap/extension-file-handler": "^2.23.0", "@tiptap/extension-image": "^2.23.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-list-item": "^2.14.0", "@tiptap/extension-paragraph": "^2.23.0", "@tiptap/extension-placeholder": "^2.26.1", "@tiptap/extension-underline": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.23.0", "@uidotdev/usehooks": "^2.4.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "i18n-iso-countries": "^7.14.0", "moment": "^2.30.1", "react": "^19.1.0", "react-big-calendar": "^1.19.4", "react-calendar": "^6.0.0", "react-country-flag": "^3.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.2", "react-phone-number-input": "^3.4.12", "react-router-dom": "^7.6.0", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "react-window": "^1.8.11", "recordrtc": "^5.6.2", "styled-components": "^6.1.18", "tailwindcss": "^3.4.1", "yup": "^1.6.1", "zustand": "^5.0.5"}, "babelMacros": {"twin": {"preset": "styled-components"}}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "babel-plugin-macros": "^3.1.0", "babel-plugin-styled-components": "^2.1.4", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "sass-embedded": "^1.89.2", "twin.macro": "^3.4.1", "vite": "^6.3.5"}}