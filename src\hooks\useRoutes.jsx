import ClinicInfo from '@/pages/clinic-infor';
import LinkExpire from '@/pages/link-expire';
import Login from '@/pages/login';
import EmailLogin from '@/pages/login-with-email';
import OtpPageLogin from '@/pages/otp-page';
import SignUp from '@/pages/sign-up';
import SignUpComplete from '@/pages/sign-up-complete';
import ChooseClinic from '@/pages/choose-clinic';
import Payment from '@/pages/payment';
import Patients from '@/pages/patients';
import Prescriptions from '@/pages/prescriptions';
import ExerciseLibrary from '@/pages/exercise-library';
import PaymentComplete from '@/pages/payment-complete';
import Settings from '@/pages/settings';
import Modals from '@/pages/modals';
import Patient from '@/pages/patients/patient';
import Staff from '@/pages/staff';
import Therapist from '@/pages/staff/therapist';
import Notifications from '@/pages/notifications';

const useRoutes = () => {
  const privateRoute = [
    { route: '/exercise-library', element: <ExerciseLibrary /> },
    { route: '/patients', element: <Patients /> },
    {
      route: '/patients/:id',
      element: <Patient />,
    },
    { route: '/staff', element: <Staff /> },
    {
      route: '/staff/:id',
      element: <Therapist />,
    },
    {
      route: '/staff/:staff_id/:id',
      element: <Patient />,
    },
    { route: '/prescriptions', element: <Prescriptions /> },
    { route: '/settings', element: <Settings /> },
    { route: '/notifications', element: <Notifications /> },
  ];

  const puplicRoute = [
    { route: '/login', element: <Login /> },
    { route: '/sign-up', element: <SignUp /> },
    { route: '/otp-page', element: <OtpPageLogin /> },
    { route: '/account-created', element: <SignUpComplete /> },
    { route: '/payment-complete', element: <PaymentComplete /> },
    { route: '/link-expire', element: <LinkExpire /> },
    { route: '/email-login', element: <EmailLogin /> },
    { route: '/clinic-info', element: <ClinicInfo /> },
    { route: '/choose-clinic', element: <ChooseClinic /> },
    { route: '/payment', element: <Payment /> },
    // { route: "/patients", element: <Patients /> },
    // { route: "/exercise-library", element: <ExerciseLibrary /> },
    { route: '/modals', element: <Modals /> },
  ];

  const sherdRoute = [
    // { route: "/privacy-policy", element: <PrivacyPolicy /> },
    // { route: "/terms-conditions", element: <TermsConditions /> },
    // { route: "*", element: <NotFOundPage /> },
  ];

  return { privateRoute, puplicRoute, sherdRoute };
};

export default useRoutes;
