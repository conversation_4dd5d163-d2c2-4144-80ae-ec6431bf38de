import React from 'react';
import tw from 'twin.macro';
import NormalModal from '@/components/shared/normalModal';
import CloseIcon from '@assets/svgs/close-icon.svg';
import Assign from './assign';
import AddPatient from './addPatient';

const PrescriptionModal = ({ weeksCount = 1 }) => {
  const [open, setOpen] = React.useState(true);
  const [modalView, setModalView] = React.useState('assign');
  const [startDate, setStartDate] = React.useState(new Date());
  const [selectedPatients, setSelectedPatients] = React.useState([]);

  // Pass this to your Menue or DropDown component: dfghsdh
  const handleSelectPatient = option => {
    setSelectedPatients(prev => (prev.includes(option.id) ? prev : [...prev, option.id]));
  };

  const handleDeSelectPatient = option => {
    setSelectedPatients(prev => prev.filter(id => id !== option.id));
  };

  return (
    <NormalModal
      handleClose={() => setOpen(false)}
      open={open}
      title={
        <div css={tw`w-full flex items-center justify-between`}>
          {modalView === 'assign' && <p>Assign to patient</p>}
          {modalView === 'addPatient' && <p>Add new patient</p>}
          <button onClick={() => setOpen(false)} aria-label="Close">
            {<img src={CloseIcon} alt="close icon" css={tw`w-[16px] h-[16px]`} />}
          </button>
        </div>
      }
      titleStyle={tw`!p-6 border-b border-stroke`}
      content={
        <div css={tw`w-full h-full flex flex-col`}>
          {modalView === 'assign' && (
            <Assign
              selectedPatients={selectedPatients}
              handleSelectPatient={handleSelectPatient}
              handleDeSelectPatient={handleDeSelectPatient}
              startDate={startDate}
              setStartDate={setStartDate}
              weeksCount={weeksCount}
              setOpen={setOpen}
              setModalView={setModalView}
            />
          )}
          {modalView === 'addPatient' && <AddPatient setModalView={setModalView} />}
        </div>
      }
      containerModalStyle={tw`w-[684px] h-fit !rounded-[12px] shadow-[0px_5px_15px_0px_rgba(0,0,0,0.08),0px_15px_35px_-5px_rgba(17,24,38,0.2),0px_0px_0px_1px_rgba(152,161,178,0.1)] overflow-visible`}
    />
  );
};

export default PrescriptionModal;
