import React, { useEffect, useState } from 'react';
import useRemToPx from '@/hooks/useRemToPx';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '@tailwind';

const fullConfig = resolveConfig(tailwindConfig);
const { Primary_800 } = fullConfig.theme.colors;

const ProfileMenuIcon = ({ onClick, ref, open }) => {
  const [isFilled, setIsFilled] = useState(false);
  const handleMouseEnter = () => setIsFilled(true);
  const handleMouseLeave = () => {
    if (!open) {
      // Only reset if the icon is not open
      setIsFilled(false);
    }
  };

  useEffect(() => {
    setIsFilled(open);
  }, [open]);

  const widthInPixels = useRemToPx(1.75);

  return (
    <svg
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      ref={ref}
      width={widthInPixels}
      height={widthInPixels} // Set height to match width
      viewBox="0 0 33 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
      style={{ cursor: 'pointer' }}
    >
      <path
        d="M16.8353 13.3307C19.7808 13.3307 22.1686 10.9429 22.1686 7.9974C22.1686 5.05188 19.7808 2.66406 16.8353 2.66406C13.8898 2.66406 11.502 5.05188 11.502 7.9974C11.502 10.9429 13.8898 13.3307 16.8353 13.3307Z"
        stroke={isFilled ? 'none' : Primary_800}
        strokeWidth="2.00537"
        fill={isFilled ? Primary_800 : 'none'}
      />
      <path
        d="M16.8353 28.0026C21.9899 28.0026 26.1686 25.6148 26.1686 22.6693C26.1686 19.7238 21.9899 17.3359 16.8353 17.3359C11.6806 17.3359 7.50195 19.7238 7.50195 22.6693C7.50195 25.6148 11.6806 28.0026 16.8353 28.0026Z"
        stroke={isFilled ? 'none' : Primary_800}
        strokeWidth="2.00537"
        fill={isFilled ? Primary_800 : 'none'}
      />
    </svg>
  );
};

export default ProfileMenuIcon;
