import InsightsCard from '../../insightsCard';
import tw, { css } from 'twin.macro';
import DropDown from '@assets/svgs/patient/drop_down.svg';
/**
 * A collapsible component for displaying insights cards with expandable/collapsible functionality
 *
 * @param {Object} props
 * @param {Array} [props.insightsList] - Array of insight items to be displayed in the grid
 * @param {boolean} props.isInsightsExpanded - Boolean state indicating if the insights section is expanded
 * @param {Function} props.toggleInsights - Callback function to toggle the expanded/collapsed state
 * @param {string} [props.insightsTitle="Insights"] - Title text displayed in the toggle header
 *
 * @example
 * <CollapseInsightPersecriptionCards
 *   insightsList={[
 *     {
 *       title: "Progress",
 *       content: "70%",
 *       containerStyle: tw`custom-style`
 *     }
 *   ]}
 *   isInsightsExpanded={isExpanded}
 *   toggleInsights={() => setIsExpanded(!isExpanded)}
 *   insightsTitle="Weekly Stats"
 * />
 *
 * @returns {JSX.Element} A collapsible section with insights cards
 *
 * @styling
 * - Container: Top border with padding
 * - Title: Bold (700) text at 0.96em size in #8DA12B color
 * - Content: 3-column grid with 8px gap
 * - Animation: Smooth height and opacity transitions
 * - Toggle headers: Centered with cursor pointer
 * - Arrow: Rotates based on expanded/collapsed state
 *
 * @features
 * - Collapsible content section with smooth animations
 * - Title appears at top when collapsed
 * - Title moves to bottom when expanded
 * - Arrow indicates current state
 * - Grid layout for insights cards
 * - Uses InsightsCard components for individual metrics
 */

const CollapseInsightPersecriptionCards = ({
  insightsList,
  isInsightsExpanded,
  toggleInsights,
  insightsTitle,
}) => {
  return (
    <>
      {/* {!isInsightsExpanded && (
        <div tw="flex justify-center items-center cursor-pointer" onClick={toggleInsights}>
          <h3 tw="font-[700] text-[0.96em] text-[#8DA12B]">{insightsTitle}</h3>
        </div>
      )} */}

      <div
        css={[
          tw`grid grid-cols-3 gap-8 overflow-hidden transition-all duration-300 `,
          isInsightsExpanded ? tw`max-h-[500px] opacity-100 mt-6` : tw`max-h-0 opacity-0`,
        ]}
      >
        {insightsList.map(item => (
          <InsightsCard
            customTitle={
              <div tw="flex gap-2 justify-center text-sm">
                <span>{item.title}</span>
                <span tw="font-medium">{item.rate}</span>
                {item.unit && <span tw="text-text_secondary">{item.unit}</span>}
              </div>
            }
            key={item.title}
            containerStyle={{ backgroundColor: item.bg_color }}
          />
        ))}
      </div>

      <div tw="flex justify-center items-center mt-3 cursor-pointer gap-1" onClick={toggleInsights}>
        <h3 tw="font-[700] text-[0.96em] text-[#8DA12B]">{insightsTitle}</h3>
        <img
          src={DropDown}
          css={[tw`transition-all`, isInsightsExpanded ? tw`rotate-180` : tw`rotate-0`]}
        />
      </div>
    </>
  );
};

export default CollapseInsightPersecriptionCards;
