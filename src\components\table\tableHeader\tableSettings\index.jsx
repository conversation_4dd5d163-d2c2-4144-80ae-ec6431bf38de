import React, { useState } from "react";
import tw from "twin.macro";
import {
  DndContext,
  closestCorners,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  rectSortingStrategy,
} from "@dnd-kit/sortable";
import SortableCard from "@components/shared/sortableCard";
import columnCardIcon from "@assets/svgs/table-settings.svg";
import columnCardHoverIcon from "@assets/svgs/table-settings-hover.svg";

/**
 * TableSettings component
 *
 * Renders a settings panel for customizing table columns and date format.
 * Allows users to reorder columns via drag-and-drop and select a date format.
 * Provides "Apply" and "Reset" actions for saving or discarding changes.
 *
 * @param {Object} props - The component props.
 * @param {Array<string>} props.initialItems - Initial array of column titles for ordering.
 * @param {string} [props.initialDateFormat] - Initial date format (default: "DD/MM/YYYY").
 * @param {Function} props.onApply - Function called with the new settings when "Apply" is clicked.
 */

const TableSettings = ({
  initialItems,
  initialDateFormat = "DD/MM/YYYY",
  onApply,
}) => {
  const [items, setItems] = useState(initialItems);
  const [dateFormat, setDateFormat] = useState(initialDateFormat);

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 5 } })
  );

  // Check if there are any changes made to settings
  const isChanged =
    items.some((item, idx) => item !== initialItems[idx]) ||
    dateFormat !== initialDateFormat;

  const dateFormats = ["DD/MM/YYYY", "MM/DD/YYYY", "YYYY/MM/DD"];

  const handleResetSettings = () => {
    setItems(initialItems);
    setDateFormat(initialDateFormat);
  };

  return (
    <>
      {/* re-arranging columns section */}
      <div css={tw`flex flex-col gap-2 pb-6 px-6`}>
        <p css={tw`font-medium text-[16px]`}>column order</p>
        <DndContext
          sensors={sensors}
          collisionDetection={closestCorners}
          onDragEnd={(event) => {
            const { active, over } = event;
            if (active.id !== over.id) {
              setItems((items) => {
                const oldIndex = items.indexOf(active.id);
                const newIndex = items.indexOf(over.id);
                return arrayMove(items, oldIndex, newIndex);
              });
            }
          }}
        >
          <SortableContext items={items} strategy={rectSortingStrategy}>
            <div
              css={tw`max-w-[80vw] flex flex-row flex-wrap gap-[4px] p-[4px] bg-neutral_300 rounded-[6px]`}
            >
              {items.map((title) => (
                <SortableCard key={title} id={title}>
                  {({ hovered, isDragging }) => (
                    <div
                      css={[
                        tw`border rounded-[6px] h-[25px] py-[2px] px-[6px] flex items-center justify-center gap-1 transition-colors`,
                        isDragging
                          ? tw`bg-Primary_50`
                          : hovered
                          ? tw`bg-neutral_300`
                          : tw`bg-white`,
                      ]}
                      style={{
                        boxShadow: isDragging
                          ? `0px 0px 0px 4px rgba(141,161,43,0.4),
                             0px 0px 0px 1px rgba(70,79,96,0.32),
                             0px 1px 1px 0px rgba(0,0,0,0.1)`
                          : "0px 1px 2px 0px rgba(0, 0, 0, 0.25),0px 0px 0px 0.5px rgba(0, 0, 0, 0.08)",
                      }}
                    >
                      <img
                        src={hovered ? columnCardHoverIcon : columnCardIcon}
                        alt="icon"
                      />
                      <span css={tw`font-medium text-[10px]`}>{title}</span>
                    </div>
                  )}
                </SortableCard>
              ))}
            </div>
          </SortableContext>
        </DndContext>
      </div>
      {/* Date format section */}
      <div css={tw`flex flex-col gap-2 pb-6 px-6`}>
        <p css={tw`font-medium text-[16px]`}>Date format</p>
        <div
          css={tw`p-[2px] gap-[1px] flex bg-neutral_200 rounded-[6px] max-w-fit h-8`}
        >
          {dateFormats.map((fmt) => (
            <button
              key={fmt}
              type="button"
              onClick={() => setDateFormat(fmt)}
              css={[
                tw`transition-colors font-medium text-[14px] text-text_secondary py-[4px] px-[10px] flex items-center justify-center`,
                dateFormat === fmt
                  ? tw`bg-white rounded-[4px] text-Primary_700`
                  : tw``,
              ]}
              style={{
                boxShadow:
                  dateFormat === fmt
                    ? `0px 2px 6px 0px rgba(0, 0, 0, 0.25),0px 0px 0px 0.5px rgba(0, 0, 0, 0.08)`
                    : "none",
              }}
            >
              {fmt}
            </button>
          ))}
        </div>
      </div>
      {/* Actions section */}
      <div css={tw`flex pt-[24px] flex justify-end gap-4 border-t px-6`}>
        <button
          type="button"
          disabled={!isChanged}
          onClick={() => onApply({ reorderableColumns: items, dateFormat })}
          css={[
            tw`px-4 py-2 rounded-[6px] border border-Primary_800 bg-Primary text-[14px] text-text_primary font-medium transition-opacity`,
            !isChanged && tw`opacity-50 cursor-not-allowed`,
          ]}
        >
          Apply
        </button>
        <button
          type="button"
          onClick={handleResetSettings}
          css={[
            tw`px-4 py-2 rounded-[6px] border border-text_secondary bg-white text-[14px] text-text_primary font-medium`,
          ]}
        >
          Reset
        </button>
      </div>
    </>
  );
};

export default TableSettings;
