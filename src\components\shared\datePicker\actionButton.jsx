import PrimaryButton from "./primaryButton";
import SecondaryButton from "./secondaryButton";
import { styles } from "./datePicker";
import "twin.macro";

// ActionButtons Component
const ActionButtons = ({
  onApply,
  onCancel,
  applyButtonText = "Apply",
  cancelButtonText = "Cancel",
  primaryColor = styles.primaryColor,
  secondaryColor = styles.secondaryColor,
  borderColor = styles.borderColor,
}) => (
  <div
    tw="flex gap-[10px] px-[20px] py-[15px] w-full justify-end text-[14px] font-[500] border-t"
    css={{ borderColor }}
  >
    <PrimaryButton
      onClick={onApply}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
    >
      {applyButtonText}
    </PrimaryButton>
    <SecondaryButton onClick={onCancel} borderColor={borderColor}>
      {cancelButtonText}
    </SecondaryButton>
  </div>
);

export default ActionButtons;
