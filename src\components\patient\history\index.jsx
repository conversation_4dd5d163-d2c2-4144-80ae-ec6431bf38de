import { useState, useRef, useEffect } from 'react';
import Calendar from './calendar';
import SessionSection from './sessionSection';
import 'twin.macro';

const History = ({ patientData }) => {
  const [prescription, setPrescription] = useState(null);
  const [parentHeight, setParentHeight] = useState(0);
  const parentRef = useRef(null);

  useEffect(() => {
    function updateHeight() {
      if (parentRef.current) {
        setParentHeight(parentRef.current.offsetHeight);
      }
    }
    // Delay the first measurement to ensure layout is complete
    const timeoutId = setTimeout(updateHeight, 100); // 100ms delay

    window.addEventListener('resize', updateHeight);
    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', updateHeight);
    };
  }, []);

  return (
    <div tw="flex gap-6 w-full">
      <Calendar ref={parentRef} {...{ setPrescription }} />
      <SessionSection
        prescription={prescription}
        parentHeight={parentHeight}
        patientData={patientData}
      />
    </div>
  );
};

export default History;
