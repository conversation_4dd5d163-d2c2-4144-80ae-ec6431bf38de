import React from 'react';
import moment from 'moment';
import tw, { css } from 'twin.macro';

/**
 * CustomDay Component
 *
 * Renders a single day cell in the calendar with custom styling and content
 *
 * @param {Object} props
 * @param {Object} props.color - Tailwind CSS color for the cell background
 * @param {string} props.icon - Icon source for the day status
 * @param {boolean} props.hasIcon - Whether to display an icon
 * @param {React.ReactNode} props.customComponent - Optional custom component to render inside the day cell
 * @param {Object} props.value - Date value of the cell
 * @param {Function} props.onSelectSlot - Function to select a date slot
 */
const CustomDay = ({
  color,
  icon,
  hasIcon,
  customComponent,
  selectedDayDate,
  currentDate,
  ...props
}) => {
  const { value } = props;

  const isToday = moment(value).isSame(moment(), 'day');
  const isCurrentMonth = moment(value).isSame(moment(currentDate), 'month');
  const isSameMonthAfterToday =
    moment(value).isSame(moment(), 'month') && moment(value).isAfter(moment(), 'day');
  const isSelectedDay = selectedDayDate === moment(value).format('YYYY-MM-DD');
  return (
    <div
      tw="flex flex-col gap-1 justify-center items-center p-1 w-full cursor-pointer text-xs font-medium "
      css={[
        // isMonthBeforeCurrentMonth && tw`bg-white`,
        isCurrentMonth ? color || tw`bg-white` : tw`bg-neutral_300 `,
        isToday && tw`bg-Primary_100`,
        isSameMonthAfterToday && tw`bg-[#F5F5F5] pointer-events-none cursor-not-allowed`,
        isSelectedDay &&
          css`
            box-shadow: inset 0 0 0 0.0625rem #6a7920;
          `,
      ]}
      // onClick={handleClick}
    >
      {hasIcon && (
        <span tw="mt-1">
          <img src={icon} width={40} alt="session-status" />
        </span>
      )}
      {customComponent && customComponent}
    </div>
  );
};
export default CustomDay;
