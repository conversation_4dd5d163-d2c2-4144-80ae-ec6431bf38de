// const { useWindowSize } = require("@uidotdev/usehooks");
// const { useState, useEffect } = require("react");
import { screenSizes } from '@/constants/constants';
import { useSidebarCollapse } from '@/zustand/sidebar-collapse';
import { useWindowSize } from '@uidotdev/usehooks';
import { useState, useEffect } from 'react';

const useCalculatePrescriptionWidth = ({ sectionRef }) => {
  const [width80, setWidth80] = useState(0);
  const { width: windowWidth } = useWindowSize();
  const { isCollapsed } = useSidebarCollapse();

  const calculateWidthPercentage = screenWidth => {
    if (screenWidth >= screenSizes['4xl']) {
      return isCollapsed ? 82 : 85;
    }
    if (screenWidth >= screenSizes.custom_xl) {
      return isCollapsed ? 79 : 79;
    }
    if (screenWidth >= screenSizes.lg) {
      return isCollapsed ? 75 : 72;
    }
    return 70;
  };

  useEffect(() => {
    if (!sectionRef.current) {
      return;
    }
    const updateWidth = () => {
      const fullWidth = sectionRef.current.offsetWidth;
      const percentage = calculateWidthPercentage(windowWidth);
      setWidth80((fullWidth * percentage) / 100);
    };
    updateWidth();
    const resizeObserver = new ResizeObserver(updateWidth);
    resizeObserver.observe(sectionRef.current);
    return () => {
      resizeObserver.disconnect();
    };
  }, [isCollapsed, windowWidth]);

  return { width80 };
};

export default useCalculatePrescriptionWidth;
