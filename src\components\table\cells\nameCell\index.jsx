import React, { useState, useRef, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import tw from 'twin.macro';
import NameLinkIcon from '@assets/svgs/Link.svg';
import OptionsIcon from '@assets/svgs/options.svg';
import Checkbox from '@/components/shared/checkbox';
import Menue from '@components/shared/menue';
import { Link } from 'react-router-dom';
import TransferPatient from '@/components/patient/profile/patiemtProfileForm/patientActions/modals/transferPatient';
import ActionModal from '@/components/actionModal';
import userRounded from '@/assets/svgs/patient/user-rounded-outline.svg';
import 'twin.macro';
import PatientModals from '@/components/patient/profile/patiemtProfileForm/patientActions/modals';
import PatientActions from './patientActions';

/**
 * NameCell component
 *
 * Displays a checkbox and the patient's || therapist's name for a table row.
 * Supports selecting/deselecting the row and provides a menu for row actions.
 *
 * @param {Object} props - Component props.
 * @param {string} props.value - The patient's name to display.
 * @param {string|number} props.rowId - Unique identifier for the row.
 * @param {Array<number>} props.selectedRows - Array of currently selected row IDs.
 * @param {Function} props.setSelectedRows - Function to update the selected rows.
 * @param {Object} props.hoverEnabledRef - Ref to control row hover state.
 * @param {Array} props.options - Array of menu options for row actions.
 */
const NameCell = ({
  value,
  rowId,
  selectedRows,
  setSelectedRows,
  hoverEnabledRef,
  baseRoute,
  children,
}) => {
  const numericId = Number(rowId);
  const isChecked = selectedRows.includes(numericId);

  const [menuOpen, setMenuOpen] = useState(false);
  const [menuPosition, setMenuPosition] = useState(null);
  const buttonRef = useRef(null);

  // Handle checkbox toggle: add/remove rowId from selectedRows
  const handleCheckboxChange = e => {
    if (e.target.checked) {
      setSelectedRows([...selectedRows, numericId]);
    } else {
      setSelectedRows(selectedRows.filter(id => id !== numericId));
    }
  };

  // When opening the menu
  const handleMenuOpen = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const menuHeight = 163; // Height of the menu
      const spaceBelow = window.innerHeight - rect.bottom; // Space from the button to the bottom of the viewport
      const renderAbove = spaceBelow < menuHeight; // If there's less than menuHeight below, render above
      setMenuPosition({
        top: renderAbove ? rect.top - menuHeight : rect.bottom,
        left: rect.left,
      });
      setMenuOpen(true);
      hoverEnabledRef.current = false; // Disable row hover
    }
  };

  // When closing the menu
  const handleMenuClose = useCallback(() => {
    setMenuOpen(false);
    setMenuPosition(null);
    hoverEnabledRef.current = true; // Enable row hover
  }, [hoverEnabledRef]);

  useEffect(() => {
    const tableId = document.getElementById('table-parent');
    if (menuOpen) {
      // Disable scrolling on body
      document.body.style.overflow = 'hidden';

      // Disable scrolling on table container if ref exists
      if (tableId) {
        tableId.style.overflow = 'hidden';
      }
    } else {
      // Enable scrolling again
      document.body.style.overflow = '';
      if (tableId) {
        tableId.style.overflow = '';
      }
    }

    return () => {
      // Cleanup on unmount
      document.body.style.overflow = '';
      if (tableId) {
        tableId.style.overflow = '';
      }
    };
  }, [menuOpen]);
  // Close menu when clicking outside of button or menu
  useEffect(() => {
    const handleClickOutside = event => {
      if (
        buttonRef.current &&
        !buttonRef.current.contains(event.target) &&
        !document.getElementById('name-cell-menu')?.contains(event.target)
      ) {
        handleMenuClose();
      }
    };

    if (menuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [menuOpen, handleMenuClose]);

  return (
    <div css={[tw`flex justify-between -translate-x-2`]}>
      <div css={tw`flex`}>
        {/* Checkbox for selecting this row */}
        <Checkbox
          checked={isChecked}
          onChange={handleCheckboxChange}
          containerStyle={tw`!py-0 !px-0 !items-center !gap-0 pr-3 !cursor-default`}
          checkboxCustomStyle={tw`!top-0`}
        />

        {/* Display the name value with link icon */}
        <Link
          to={baseRoute ? `/${baseRoute}/${rowId} ` : `${rowId}`}
          className="group"
          css={[
            tw`
            transition-colors duration-200
            text-text_tertiary
            hover:text-info
            hover:underline
            underline-offset-4
            inline-flex items-center
          `,
            isChecked && tw`text-info underline`,
          ]}
        >
          {value}
          <img
            src={NameLinkIcon}
            alt="link"
            css={[
              tw`
              w-4 h-4 ml-1 opacity-0 transition-opacity duration-200
              group-hover:opacity-100
              pointer-events-none
            `,
              isChecked && tw`opacity-100`,
            ]}
          />
        </Link>
      </div>

      {/* Three dots button to toggle dropdown menu */}
      <div
        ref={buttonRef}
        css={tw`cursor-pointer flex items-center justify-center w-5 h-5 hover:bg-neutral_300 rounded`}
        onClick={menuOpen ? handleMenuClose : handleMenuOpen}
        aria-haspopup="true"
        aria-expanded={menuOpen}
      >
        <img src={OptionsIcon} alt="options" />
      </div>

      {/* Dropdown menu rendered in a portal to avoid clipping issues */}
      {menuOpen &&
        menuPosition &&
        createPortal(
          <div
            id="name-cell-menu"
            css={tw`fixed z-[9999]`}
            style={{
              top: menuPosition.top,
              left: menuPosition.left,
              minWidth: 150,
            }}
          >
            {children({ handleMenuClose })}
          </div>,
          document.body
        )}
    </div>
  );
};

export default NameCell;
