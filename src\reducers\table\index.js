export const TableActionTypes = {
  SET_SORTING: 'SET_SORTING',
  SET_SELECTED_ROWS: 'SET_SELECTED_ROWS',
  SET_PAGE_SIZE: 'SET_PAGE_SIZE',
  SET_PAGE_INDEX: 'SET_PAGE_INDEX',
  SET_REORDERABLE_COLUMNS: 'SET_REORDERABLE_COLUMNS',
  SET_DATE_FORMAT: 'SET_DATE_FORMAT',
  SET_CURRENT_ROWS: 'SET_CURRENT_ROWS',
  SET_LOADING: 'SET_LOADING',
};

export const initialState = reorderableColumns => ({
  sorting: [],
  selectedRows: [],
  pageSize: 5,
  pageIndex: 0,
  reorderableColumns,
  dateFormat: 'DD/MM/YYYY',
  currentRows: [],
  loading: true,
});

export function reducer(state, action) {
  switch (action.type) {
    case TableActionTypes.SET_SORTING:
      return { ...state, sorting: action.payload };
    case TableActionTypes.SET_SELECTED_ROWS:
      return { ...state, selectedRows: action.payload };
    case TableActionTypes.SET_PAGE_SIZE:
      return { ...state, pageSize: action.payload };
    case TableActionTypes.SET_PAGE_INDEX:
      return { ...state, pageIndex: action.payload };
    case TableActionTypes.SET_REORDERABLE_COLUMNS:
      return { ...state, reorderableColumns: action.payload };
    case TableActionTypes.SET_DATE_FORMAT:
      return { ...state, dateFormat: action.payload };
    case TableActionTypes.SET_CURRENT_ROWS:
      return { ...state, currentRows: action.payload };
    case TableActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };
    default:
      return state;
  }
}
