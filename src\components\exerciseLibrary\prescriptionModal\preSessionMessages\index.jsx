import React, { useState } from 'react';
import tw from 'twin.macro';
import { ModalViews } from '..';
import TooltipIcon from '@/components/shared/tooltipIcon';
import VoiceIcon from '@assets/svgs/voice.svg';
import TextArea from '@/components/shared/textarea';
import PerExerciseMessage from './perExerciseMessage';
import Audio from '@/components/shared/audio'; // Import the Audio component

const PreSessionMessages = ({
  register,
  errors,
  setModalView,
  setVoiceField,
  watch,
  setValue,
  exercises,
}) => {
  const [currentlyPlayingAudioUrl, setCurrentlyPlayingAudioUrl] = useState(null); // Single source of truth for currently playing audio

  const handleRecordGeneralMessage = () => {
    setVoiceField('generalMessage.voice'); // Set the field to update
    setModalView(ModalViews.VOICE_RECORDING); // Navigate to the voice recording view
  };

  // Watch the generalMessage.voice field
  const generalMessageVoice = watch('generalMessage.voice');

  return (
    <div css={tw`flex flex-col`}>
      <div css={tw`p-6 flex-1 min-h-0 overflow-y-auto max-h-[67vh]`}>
        {/* general message section */}
        <div css={tw`flex flex-col gap-[12px] pb-6 border-b-2 border-stroke`}>
          {/* label */}
          <div css={tw`flex items-center gap-[10px]`}>
            <p css={tw`text-[18px] font-semibold`}>General Message</p>
            <TooltipIcon text="general message tooltip" isActiveOnHover />
          </div>
          <div css={tw`p-5 bg-neutral_200 flex gap-6 border border-border_stroke rounded-[10px]`}>
            {/* voice message */}
            <div css={tw`basis-[26%] flex flex-col gap-[6px]`}>
              <p css={tw`text-[16px] font-semibold text-text_secondary`}>Voice message</p>
              {generalMessageVoice ? (
                // Render the Audio component if generalMessage.voice exists
                <Audio
                  audioUrl={generalMessageVoice.audioUrl}
                  duration={generalMessageVoice.duration}
                  handleDeleteAudio={() => {
                    setValue('generalMessage.voice', null); // Remove the voice object from the form state
                  }}
                  currentlyPlayingAudioUrl={currentlyPlayingAudioUrl}
                  setCurrentlyPlayingAudioUrl={setCurrentlyPlayingAudioUrl}
                />
              ) : (
                // Render the button if generalMessage.voice does not exist
                <button
                  css={tw`px-4 py-[10px] border border-[#000] flex items-center justify-center gap-2 text-[15px] font-medium text-Primary_800 bg-Primary_100 rounded-[6px]`}
                  onClick={handleRecordGeneralMessage}
                >
                  <img src={VoiceIcon} alt="voice-button-icon" />
                  Add voice message
                </button>
              )}
            </div>
            {/* notes */}
            <div css={tw`basis-[74%] flex flex-col gap-[6px]`}>
              <div css={tw`flex items-center gap-[6px]`}>
                <p css={tw`text-[16px] font-semibold text-text_secondary`}>Notes</p>
                <TooltipIcon text="notes tooltip" isActiveOnHover />
              </div>
              <TextArea
                name="generalMessage.note"
                register={register}
                placeholder="Add notes.."
                containerSTyle={tw`p-0 rounded-[6px]`}
                textAreaStye={tw`!h-[150px] px-5 py-2 text-[15px] font-medium resize-none`}
                maxLength={255}
                errorMessage={errors?.generalMessage?.note?.message}
              />
            </div>
          </div>
        </div>
        {/* Exercise Messages */}
        <div css={tw`flex flex-col`}>
          <div css={tw`flex items-center gap-[10px] pt-6 pb-3`}>
            <p css={tw`text-[18px] font-semibold`}>Messages per exercise</p>
            <TooltipIcon text="Messages per exercise tooltip" isActiveOnHover />
          </div>
          <div css={tw`flex flex-col gap-6 max-h-[300px]`}>
            {exercises.map((exercise, idx) => (
              <PerExerciseMessage
                key={exercise.id}
                exerciseId={exercise.id}
                exerciseNumber={idx + 1}
                exerciseTitle={exercise.title}
                register={register}
                errors={errors}
                setVoiceField={setVoiceField}
                setModalView={setModalView}
                watch={watch}
                setValue={setValue}
                currentlyPlayingAudioUrl={currentlyPlayingAudioUrl}
                setCurrentlyPlayingAudioUrl={setCurrentlyPlayingAudioUrl}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreSessionMessages;
