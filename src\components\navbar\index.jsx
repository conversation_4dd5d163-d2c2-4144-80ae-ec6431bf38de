import React from 'react';
import NotificationInbox from '@/components/notificationInbox';
import { useLocation } from 'react-router-dom';
import 'twin.macro';
import tw from 'twin.macro';
import ProfileMenu from '@/components/profileMenu';

const Navbar = () => {
  const { pathname } = useLocation();

  return (
    <div
      tw="px-[24px] h-[8%] flex sticky top-0 left-0 items-center justify-end w-full py-[10px]"
      css={pathname.includes('exercise-library') ? tw`bg-white` : tw`bg-[#f8f9fa]`}
    >
      <div tw="flex gap-2 items-center">
        <NotificationInbox />
        <ProfileMenu />
      </div>
    </div>
  );
};

export default Navbar;
