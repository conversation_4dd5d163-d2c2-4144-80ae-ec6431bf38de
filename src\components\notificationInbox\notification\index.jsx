import React from 'react';
import tw from 'twin.macro';
import NotificationIcon from './notificationIcon';
import NotificationMessage from './notificationMessage';
import { useNavigate } from 'react-router-dom';

const Notification = ({ notification, isLast, setOpen }) => {
  const navigate = useNavigate();
  const handleOpenNotification = () => {
    // Check if the notification is unread and toggle it to read
    if (!notification.isRead) {
      notification.isRead = true; // Mark the notification as read
    }
    setOpen(false);
    navigate('/notifications');
  };
  return (
    <div
      css={[
        tw`px-[1.2rem] py-[0.8rem] flex gap-[0.8rem] border-b border-b-stroke cursor-pointer hover:bg-neutral_50`,
        !notification.isRead && tw`bg-Primary_50 hover:bg-Primary_100`,
        isLast && tw`border-none`,
      ]}
      onClick={handleOpenNotification}
    >
      <NotificationIcon notificationType={notification.type} />
      <div css={tw`flex flex-col gap-[0.3rem]`}>
        <NotificationMessage notification={notification} setOpen={setOpen} />
        <p css={tw`font-medium text-[0.7rem] text-[#A5ACB8]`}>{notification.timestamp}</p>
      </div>
    </div>
  );
};

export default Notification;
