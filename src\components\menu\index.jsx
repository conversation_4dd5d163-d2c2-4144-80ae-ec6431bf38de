import MenuList from './menuList';
import tw from 'twin.macro';

/**
 * A customizable menu component that displays a three-dot menu icon and a dropdown list of options.
 *
 * @param {Object} props
 * @param {Function} props.handelClose - Function to handle closing the menu
 * @param {Function} props.handleOpenMenu- Function to handle open the menu
 * @param {boolean} props.disable - Whether the menu is disabled
 * @param {boolean} props.openMenu - Whether the menu dropdown is open
 * @param {Array<{id: string|number, text: string, onClick: Function}>} props.options - Array of menu options
 *
 * @example
 * <Menu
 *   handelClose={() => setMenuOpen(false)}
 *   disable={false}
 *   openMenu={isMenuOpen}
 *   handleOpenMenu={()=>setMenuOpen(true)}
 *   options={[
 *     { id: 1, text: "Edit", onClick: () => handleEdit() },
 *     { id: 2, text: "Delete", onClick: () => handleDelete() }
 *   ]}
 * />
 *
 * @styling
 * - Container: 20% width, full height
 * - Border: 1px, rounded corners (8px)
 * - Hover state: Primary color background
 * - Disabled state: Neutral background with reduced opacity
 * - Icon: Three dots with hover effect
 * - Transitions: Smooth color changes
 *
 * @features
 * - Toggleable dropdown menu
 * - Disabled state support
 * - Hover effects
 * - Customizable options
 * - Click-away to close
 */

/**
 * Menu component...
 */
const Menu = ({ handelClose, disable, openMenu, options, handleOpenMenu, width = 'w-[20%]' }) => {
  const containerStyles = [
    tw`flex relative justify-center items-center transition-all duration-300 p-[10px] border rounded-[8px] overflow-hidden h-full cursor-pointer`,
    disable
      ? tw`bg-neutral_200 border-border_stroke opacity-70 `
      : tw`border-Primary_800 hover:bg-Primary_800`,
    openMenu && !disable && tw`bg-Primary_800`, // simulate hover when open
  ];

  const iconWrapperStyle = [
    tw`transition-colors duration-300`,
    !disable && (openMenu ? tw`text-white` : tw`text-Primary_800`),
    !disable && tw`group-hover:text-white`,
  ];

  return (
    <>
      <div
        className={`group ${width}`}
        css={containerStyles}
        onClick={disable ? null : handleOpenMenu}
      >
        {disable ? (
          <svg
            width="12"
            height="4"
            viewBox="0 0 16 4"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle opacity="0.5" cx="2" cy="2" r="2" fill="#2E2E2E" />
            <circle opacity="0.5" cx="8" cy="2" r="2" fill="#2E2E2E" />
            <circle opacity="0.5" cx="14" cy="2" r="2" fill="#2E2E2E" />
          </svg>
        ) : (
          <svg
            width="12"
            height="4"
            viewBox="0 0 16 4"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            css={iconWrapperStyle}
          >
            <circle cx="2" cy="2" r="2" fill="currentColor" />
            <circle cx="8" cy="2" r="2" fill="currentColor" />
            <circle cx="14" cy="2" r="2" fill="currentColor" />
          </svg>
        )}
      </div>

      {openMenu && <MenuList handelClose={handelClose} options={options} />}
    </>
  );
};

export default Menu;
