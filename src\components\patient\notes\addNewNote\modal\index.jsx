import 'twin.macro';
import tw from 'twin.macro';
import ModalTitle from '@/components/modalTitle';
import NoteForm from '../form';
import { useState } from 'react';
import GenericModal from '@/components/genericModal';
import { stripHtmlTags } from '@/components/editor/utils';

const AddNewNoteModal = ({
  open,
  handleClose,
  handleAddNewNote,
  handleUpdateNote,
  updateMode = false,
  noteToUpdate,
  setSelectedImage,
}) => {
  const [content, setContent] = useState(noteToUpdate?.content || '');
  const [documents, setDocuments] = useState(noteToUpdate?.documents || []);
  const [images, setImages] = useState(noteToUpdate?.images || []);

  return (
    <>
      {/* <GenericModal /> */}

      <GenericModal
        handelCloseMode={handleClose}
        openModel={open}
        title={
          <>
            {updateMode ? (
              <ModalTitle
                customStyle={tw`!px-6 border-b border-stroke `}
                title={
                  <>
                    {noteToUpdate.published_date} by {noteToUpdate.author}
                  </>
                }
                handleClose={handleClose}
              />
            ) : (
              <ModalTitle
                customStyle={tw`!px-6 border-b border-stroke `}
                title={<>Add new note</>}
                handleClose={handleClose}
              />
            )}
          </>
        }
        titleStyle={tw`rounded-t-xl overflow-hidden !pb-0`}
        element="div"
        content={
          <NoteForm
            setContent={setContent}
            setDocuments={setDocuments}
            setImages={setImages}
            content={content}
            documents={documents}
            images={images}
            setSelectedImage={setSelectedImage}
          />
        }
        typeOfPrimaryButton={'button'}
        PrimaryButtonText="Save"
        clickOnPrimaryButton={() => {
          if (updateMode)
            handleUpdateNote({
              ...noteToUpdate,
              content,
              images,
              documents,
              published_date: new Date().toDateString(),
            });
          else {
            handleAddNewNote({ id: 1, images, documents, content });
          }
          handleClose();
        }}
        contetnContainerStyle={tw`min-h-[0px] h-auto`}
        secondaryButtonText="Cancel"
        typeOfSecondaryButton="button"
        clickOnSecondaryButton={handleClose}
        PrimaryButtonStyle={tw`flex-1`}
        PrimaryButtonDisable={!stripHtmlTags(content)}
        SecondaryButtonStyle={tw`flex-1`}
        containerModalStyle={tw`  lg:!w-[42.75rem]  h-auto !rounded-xl `}
      />
    </>
  );
};

export default AddNewNoteModal;
