import Checkbox from '@/components/shared/checkbox';
import { Controller } from 'react-hook-form';

// Parent Checkbox Component
const ParentCheckbox = ({ item, control, watch, setValue, filterItems }) => (
  <Controller
    name={`marketingConsent_${item.id}`}
    control={control}
    render={({ field }) => (
      <Checkbox
        label={item.title}
        checked={
          item.items
            ? item.items.every(_val => watch(`marketingConsent_custom_${_val.id}`))
            : field.value
        }
        hasHovering
        onChange={e => {
          const checked = e.target.checked;
          const isAllChecked = watch('all');
          field.onChange(checked);

          // Handle child items if they exist
          if (item.items) {
            item.items.forEach(_val => {
              setValue(`marketingConsent_custom_${_val.id}`, checked);
            });
          }

          // Handle "All" checkbox logic
          if (isAllChecked && checked) {
            setValue('all', false);

            filterItems.forEach(i => {
              if (i.id !== 'all' && i.id !== item.id) {
                setValue(`marketingConsent_${i.id}`, true);
                if (i.items) {
                  i.items.forEach(_val => setValue(`marketingConsent_custom_${_val.id}`, true));
                }
              }
            });

            setValue(`marketingConsent_${item.id}`, false);
          }

          // Check if all items are selected to update "All" checkbox
          setTimeout(() => {
            const allChecked = filterItems.every(i => {
              if (i.id === 'all') {
                return true;
              }

              const isParentChecked = watch(`marketingConsent_${i.id}`);
              const areChildrenChecked = i.items
                ? i.items.every(_val => watch(`marketingConsent_custom_${_val.id}`))
                : true;

              return isParentChecked && areChildrenChecked;
            });

            if (allChecked) {
              setValue('all', true);

              filterItems.forEach(i => {
                if (i.id !== 'all') {
                  setValue(`marketingConsent_${i.id}`, false);
                  if (i.items) {
                    i.items.forEach(_val => setValue(`marketingConsent_custom_${_val.id}`, false));
                  }
                }
              });
            }
          }, 100);
        }}
      />
    )}
  />
);

export default ParentCheckbox;
