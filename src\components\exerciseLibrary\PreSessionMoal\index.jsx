import GenericModal from '@/components/genericModal';
import { ModalViews } from '../prescriptionModal';
import tw from 'twin.macro';
import ModalTitle from '@/components/modalTitle';

const PreSessionMessagesModal = ({ open, handleClose, children, modalView }) => {
  return (
    <GenericModal
      openModel={open}
      containerModalStyle={[
        tw`!rounded-[12px] h-fit shadow-[0px_5px_15px_0px_rgba(0,0,0,0.08),0px_15px_35px_-5px_rgba(17,24,38,0.2),0px_0px_0px_1px_rgba(152,161,178,0.1)] overflow-visible w-[65%]`,
      ]}
      title={<ModalTitle title={'Pre-session messages'} />}
      titleStyle={tw`!p-0 border-b border-stroke !rounded-t-[12px]`}
      content={children}
      handelCloseMode={handleClose}
      contetnContainerStyle={tw` flex flex-col min-h-[0px]`}
      element={'div'}
      PrimaryButtonText={'Save Changes'}
      typeOfPrimaryButton={'button'}
      hasPrimaryButton={modalView === ModalViews.PRE_SESSION_MESSAGES}
      hasSecondaryButton={modalView === ModalViews.PRE_SESSION_MESSAGES}
      PrimaryButtonStyle={[
        tw`w-[7.75rem] !py-[10px] !px-[10px] !rounded-[8px] border-Primary_800 text-Primary_800 !font-bold `,
      ]}
      clickOnPrimaryButton={handleClose}
      secondaryButtonText="Cancel"
      typeOfSecondaryButton="button"
      SecondaryButtonStyle={tw`w-[7.75rem] !py-[10px] !rounded-[8px] !font-bold border-Primary_800 text-Primary_800 text-sm`}
      clickOnSecondaryButton={handleClose}
      containerButtonsStyle={[
        modalView === ModalViews.VOICE_RECORDING || modalView === ModalViews.TEXT_TO_SPEECH
          ? tw`!p-0 border-none [display: none]`
          : tw`px-8 py-6 gap-3 !border-transparent !border-t-stroke  rounded-b-[12px] [display: none]`,
        modalView === ModalViews.PRE_SESSION_MESSAGES ? tw`justify-end w-fit` : tw`[display: none]`,
      ]}
    />
  );
};

export default PreSessionMessagesModal;
