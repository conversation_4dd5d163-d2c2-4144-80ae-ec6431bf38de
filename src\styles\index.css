@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 15px;
}

@media (max-width: 1240px) {
  html {
    font-size: 12px;
  }
}

body {
  background-color: #f8f9fa;
  color: #2e2e2e;
  font-family: 'Inter', sans-serif;
}

/* Chrome, Safari, and newer Edge */
.element::-webkit-scrollbar {
  display: none;
}

/* Firefox */
.element {
  scrollbar-width: none;
}

.editor {
  border: 1px solid #ccc;
  padding: 10px;
  min-height: 100px;
  margin: 10px 0;
}

.editor:focus {
  outline: none;
  border-color: #007bff;
}

.editor.ordered {
  counter-reset: ordered-list;
}

.editor.ordered > div {
  counter-increment: ordered-list;
  margin-left: 25px;
  position: relative;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  color: #888;
  pointer-events: none;
}
.editor.ordered > div::before {
  content: counter(ordered-list) '. ';
  position: absolute;
  left: -25px;
  width: 20px;
  text-align: right;
}

button.active {
  background-color: #007bff;
  color: white;
}

/* custom-toolbar.css */
.custom-toolbar .e-toolbar .e-tbar-btn .e-icons {
  display: none; /* Hide default icons */
}

.custom-toolbar .e-toolbar .e-tbar-btn .e-btn-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-toolbar .e-toolbar .e-tbar-btn[title='Bold'] .e-btn-icon {
  background: url('/src/assets/bold.svg') no-repeat center;
  background-size: contain;
}

/* .custom-toolbar
  .e-toolbar
  .e-tbar-btn[aria-label="Italic (Ctrl+I)"]
  .e-btn-icon {
  background: url("assets/svgs/bookmark.svg") no-repeat center;
  background-size: contain;
} */

.custom-toolbar .e-toolbar .e-tbar-btn[aria-label='Italic (Ctrl+I)'] .e-btn-icon::before {
  content: '';
  display: block;
  width: 24px;
  height: 24px;
  background: url('assets/svgs/bookmark.svg') no-repeat center;
}

/* Add similar rules for other toolbar items */

/* Target the toolbar specifically */
.custom-toolbar .e-toolbar {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 5px;
}

/* Style individual toolbar items */
.custom-toolbar .e-toolbar .e-tbar-btn {
  margin: 2px;
  border-radius: 4px;
}

/* Hover effect */
.custom-toolbar .e-toolbar .e-tbar-btn:hover {
  background-color: #e9ecef;
}

/* Basic editor styles */
.tiptap:first-child {
  margin-top: 0;
}

/* List styles */
.tiptap ul,
.tiptap ol {
  padding: 0 1rem;
  margin: 1.25rem 1rem 1.25rem 0.4rem;
}

.tiptap ul li p,
.tiptap ol li p {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

/* Heading styles */
.tiptap h1,
.tiptap h2,
.tiptap h3,
.tiptap h4,
.tiptap h5,
.tiptap h6 {
  line-height: 1.1;
  margin-top: 2.5rem;
  text-wrap: pretty;
}

.tiptap h1,
.tiptap h2 {
  margin-top: 3.5rem;
  margin-bottom: 1.5rem;
}

.tiptap h1 {
  font-size: 1.4rem;
}

.tiptap h2 {
  font-size: 1.2rem;
}

.tiptap h3 {
  font-size: 1.1rem;
}

.tiptap h4,
.tiptap h5,
.tiptap h6 {
  font-size: 1rem;
}

/* Code and preformatted text styles */
.tiptap code {
  background-color: var(--purple-light);
  border-radius: 0.4rem;
  color: var(--black);
  font-size: 0.85rem;
  padding: 0.25em 0.3em;
}

.tiptap pre {
  background: '#f00';
  border-radius: 0.5rem;
  color: var(--white);
  font-family: 'JetBrainsMono', monospace;
  margin: 1.5rem 0;
  padding: 0.75rem 1rem;
}

.tiptap pre code {
  background: none;
  color: inherit;
  font-size: 0.8rem;
  padding: 0;
}

.tiptap blockquote {
  border-left: 3px solid var(--gray-3);
  margin: 1.5rem 0;
  padding-left: 1rem;
}

.tiptap hr {
  border: none;
  border-top: 1px solid var(--gray-2);
  margin: 2rem 0;
}

/* toastify-custom.css */
.Toastify__toast--custom {
  background: #2e2e2e !important;
  color: white !important;
  border-radius: 12px !important;
  padding-inline: 20px !important;
  padding-block: 10px !important;
  position: relative;
}

.Toastify_error__toast--custom {
  background: #a72828 !important;
  color: #fff !important;
  border-radius: 12px !important;
  padding-inline: 20px !important;
  padding-block: 10px !important;
  position: relative;
}

.fadeIn {
  animation: fadeIn 0.3s ease-in;
}
.fadeOut {
  display: none;
  right: -1000px;
  transition: all ease-out 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.react-calendar {
  border: none !important;
  border-radius: 12px;
  overflow: hidden;
  font-family: 'Inter', sans-serif !important;
  width: 100% !important;
}

/* Hide arrows for a specific input with a specific class */
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield; /* For Firefox */
}

textarea:focus {
  outline: none;
}

.custom-scroll::-webkit-scrollbar {
  width: 0px;
  background: #e4e4e47f;
}

.custom-scroll::-webkit-scrollbar:horizontal {
  height: 6px;
}

.custom-scroll::-webkit-scrollbar-thumb:horizontal {
  background: #ccc;
  border-radius: 4px;
  cursor: pointer;
}

.custom-scroll {
  scrollbar-width: auto;
}

#prescription-items::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

#prescription-items::-webkit-scrollbar-thumb {
  background-color: #e4e7eb;
  border-radius: 40px;
}

.add-icon {
  background-image: url('../assets//svgs/add-icon.svg');
  width: 10px;
  height: 10px;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  display: inline-block;
  vertical-align: middle;
  margin-inline: 3px;
}

.add-template-icon {
  background-image: url('../assets//svgs/exercise-library/outline-add-template-icon.svg');
  width: 10px;
  height: 10px;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  display: inline-block;
  vertical-align: middle;
  margin-inline: 3px;
}

@keyframes pulse {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
}

.drag-preview {
  position: absolute;
  pointer-events: none;
  z-index: 1000;
  background: rgba(141, 161, 43, 0.1);
  border: 2px dashed #8da12b;
  border-radius: 20px;
  animation: pulse 1.5s infinite;
}
.ProseMirror {
  min-height: 15rem;
}
.ProseMirror ol,
.ProseMirror ul {
  /* list-style-type: none; */
  counter-reset: item;
  padding-left: 0;
}

.ProseMirror ol li,
.ProseMirror ul li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  position: relative;
}

.ProseMirror ol li::before,
.ProseMirror ul li::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #000;
  border-radius: 50%;
  margin-right: 10px;
  margin-top: 8px;
  flex-shrink: 0;
}

/* start Wavesurf styling section */
.mic-waveform {
  width: 596px;
  position: relative;
}
div::part(timeline-notch-tick) {
  display: none !important;
}
div::part(cursor) {
  height: 85% !important;
  top: 10.5% !important;
}
body.recording-active div::part(cursor)::before,
body.recording-active div::part(cursor)::after {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 5px;
  height: 5px;
  background-color: #285ff5;
  border-radius: 50%;
  z-index: 1;
}

div::part(cursor)::before {
  top: 0px;
}

div::part(cursor)::after {
  bottom: 0px;
}

/* end Wavesurf styling section */

/* Autofill styles */
input:-webkit-autofill,
input:-webkit-autofill:focus,
input:-webkit-autofill:hover,
input:-webkit-autofill:active {
  /* Cover Chrome's blue autofill background with white */
  -webkit-box-shadow: 0 0 0 1000px #fff inset !important;
  box-shadow: 0 0 0 1000px #fff inset !important;
  /* Set autofilled text color */
  -webkit-text-fill-color: #2e2e2e !important;
  /* Prevent autofill background color animation */
  transition: background-color 5000s ease-in-out 0s;
}
