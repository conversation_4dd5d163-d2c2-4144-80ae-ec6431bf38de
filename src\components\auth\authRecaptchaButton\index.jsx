import ReCAPTCHA from 'react-google-recaptcha';
import { Controller } from 'react-hook-form';
import 'twin.macro';

const siteKey = import.meta.env.VITE_SITE_KEY;

const AuthRecaptchaButton = ({ recaptchaRef, recaptchError, control, errorMessage }) => {
  return (
    <div
      style={{
        width: '100%',
        display: 'grid',
        justifyContent: 'center',
        alignItems: 'center',
        // position: "relative",
        left: '0',
        paddingTop: 10,
        zIndex: 20,
        position: 'absolute',
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: 'fit-content',
          height: 'fit-content',
        }}
      >
        <Controller
          name="recaptchaToken"
          control={control}
          render={({ field }) => (
            <ReCAPTCHA
              className={recaptchError ? 'hasError' : ''}
              ref={recaptchaRef}
              sitekey={siteKey}
              size="invisible"
              onChange={token => {
                field.onChange(token);
              }}
            />
          )}
        />
      </div>
    </div>
  );
};

export default AuthRecaptchaButton;
