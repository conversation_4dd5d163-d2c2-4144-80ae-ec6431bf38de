import React from 'react';
import InvitationIcon from '@/assets/svgs/notification/patient-add.svg';
import DeleteIcon from '@/assets/svgs/notification/patient-delete.svg';
import CompleteIcon from '@/assets/svgs/notification/complete.svg';
import ReportIcon from '@/assets/svgs/notification/report.svg';
import tw from 'twin.macro';

const NotificationIcon = ({ notificationType }) => {
  const iconMap = {
    invitation: InvitationIcon,
    discharge: DeleteIcon,
    sesssionCompletion: CompleteIcon,
    painReport: ReportIcon,
  };
  return (
    <img src={iconMap[notificationType]} alt="Notification Icon" css={tw`w-[2rem] h-[2rem]`} />
  );
};

export default NotificationIcon;
