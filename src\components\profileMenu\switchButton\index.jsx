import React from 'react';
import ClinicIcon from '../clinicIcon';
import SwitchIcon from '@assets/svgs/refresh.svg';
import tw, { css } from 'twin.macro';
import { useNavigate } from 'react-router-dom';

const SwitchButton = ({ clinicName, clinicId, setClinics, setOpen }) => {
  const navigate = useNavigate();
  const handleSwitchClinic = () => {
    setClinics(prevClinics =>
      prevClinics.map(
        clinic =>
          clinic.id === clinicId
            ? { ...clinic, active: true } // Make the clicked clinic active
            : { ...clinic, active: false } // Make all other clinics inactive
      )
    );
    setOpen(false); // Close the menu after switching
    navigate('/exercise-library');
  };
  return (
    <div
      css={tw`px-[0.8rem] py-[0.5rem] flex items-center gap-[0.6rem] cursor-pointer border-b border-stroke bg-neutral_100 hover:bg-Primary_50`}
      className="group"
      onClick={handleSwitchClinic}
    >
      <div css={tw`px-[0.5rem] py-[0.35rem] relative`}>
        <ClinicIcon clinicName={clinicName} />
        <img
          src={SwitchIcon}
          alt="switchIcon"
          css={[
            tw`absolute  inset-0 m-auto w-[full] h-[full] transition-transform duration-[300ms]`,
            tw`group-hover:rotate-180`,
            css`
              transform: rotate(0deg); // Initial state
            `,
          ]}
        />
      </div>
      <p
        css={[
          tw`w-[56%] truncate font-medium text-[0.7rem] flex gap-[0.2rem] items-center translate-x-[-3.728rem] transition-transform duration-[300ms]`,
          tw`group-hover:translate-x-0`,
        ]}
      >
        <span
          css={[
            tw`text-Primary_700 opacity-0 transition-opacity duration-[300ms] group-hover:opacity-100`,
          ]}
        >
          Switch to
        </span>
        {clinicName}
      </p>
    </div>
  );
};

export default SwitchButton;
