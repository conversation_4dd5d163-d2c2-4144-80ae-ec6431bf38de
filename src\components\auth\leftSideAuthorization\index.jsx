import Circle from '../Circle';
import { useState, useEffect, useRef } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import 'twin.macro';

const LeftSideAuthorization = ({ data = [], circles = [], logoImageUrl }) => {
  const [active, setActive] = useState(0);
  const [currentData, setCurrentData] = useState(data[0] || {});
  const timeoutRef = useRef(null);

  const startTimeout = () => {
    timeoutRef.current = setInterval(() => {
      setActive(prev => (prev + 1) % data.length);
    }, 5000);
  };

  const navigate = useNavigate();

  const clearAndRestartTimeout = () => {
    if (timeoutRef.current) {
      clearInterval(timeoutRef.current);
    }
    startTimeout();
  };

  useEffect(() => {
    startTimeout();
    return () => {
      if (timeoutRef.current) {
        clearInterval(timeoutRef.current);
      }
    };
  }, [data.length]);

  useEffect(() => {
    setCurrentData(data[active] || {});
  }, [active, data]);

  const handleCircleClick = id => {
    setActive(id);
    clearAndRestartTimeout();
  };

  return (
    <div tw="flex relative bg-gray-100">
      <AnimatePresence mode="wait">
        <motion.img
          key={active}
          src={currentData.backgroundImage}
          alt="background"
          tw="object-cover absolute top-0 left-0 z-0 w-full h-full"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        />
      </AnimatePresence>

      {logoImageUrl && (
        <img
          alt="logo"
          tw="absolute top-[5%] left-[5%] z-[10] cursor-pointer"
          src={logoImageUrl}
          onClick={() => navigate('/login', { replace: true })}
        />
      )}

      <div tw="grid gap-6 absolute bottom-[5%] left-[50%] translate-x-[-50%] z-[10] w-[72%]">
        <AnimatePresence mode="wait">
          <motion.div
            key={active}
            tw="p-6 w-full rounded-[20px] backdrop-blur-[40px] bg-[rgba(0, 0, 0, 0.3)] opacity-30 border-2 border-[#E5E5E5]"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
          >
            <h2 tw="font-['Inter'] mb-2 text-[1.25rem] font-[700] text-white">
              {currentData.title}
            </h2>
            <p tw="text-[1rem] text-white font-[400] [line-height: 130%] font-['Inter']">
              {currentData.subtitle}
            </p>
          </motion.div>
        </AnimatePresence>

        {circles?.length > 0 && (
          <div tw="flex gap-2 justify-center z-[10] items-center">
            {circles.map(item => (
              <Circle
                isActive={active === item.id}
                key={item.id}
                handelCLick={() => handleCircleClick(item.id)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default LeftSideAuthorization;
