import ActionModal from '@/components/actionModal';
import tw from 'twin.macro';
import userRounded from '@/assets/svgs/patient/user-rounded-outline.svg';
function GroupPatientAction({
  openGroupPatientsModal,
  handleCloseGroupPatientsModal,
  data,
  selectedRows,
  label,
}) {
  const numberOfPatients = selectedRows?.length > 1 ? 'thses patients' : 'this patient';

  return (
    <>
      {openGroupPatientsModal === 'discharge' && (
        <ActionModal
          open={openGroupPatientsModal === 'discharge'}
          handleClose={handleCloseGroupPatientsModal}
          primaryActionHandler={handleCloseGroupPatientsModal}
          title={`Discharge ${label}: ${data
            .filter(item => selectedRows.includes(item.id))
            .map(item => item.Name)
            .join(', ')}`}
          customHeaderStyle={tw`!bg-Primary_100 !border-Primary_50`}
          description={`Are you sure you want to discharge ${numberOfPatients}? This action cannot be undone.`}
          actionButtonText="Confirm"
          cancelButtonText="Cancel"
          SecondaryButtonStyle={tw`bg-white !border-border_stroke text-text_primary !text-base`}
          customIcon={userRounded}
          PrimaryButtonStyle={tw`flex-1 !py-2.5 !rounded-md !font-medium !bg-Primary border-Primary_600 hover:!bg-Primary_600 text-text_primary !text-base`}
        />
      )}

      {openGroupPatientsModal === 'delete' && (
        <ActionModal
          open={openGroupPatientsModal === 'delete'}
          title={`Remove ${label}: ${data
            .filter(item => selectedRows.includes(item.id))
            .map(item => item.Name)
            .join(', ')}`}
          handleClose={handleCloseGroupPatientsModal}
          primaryActionHandler={handleCloseGroupPatientsModal}
          actionButtonText="Remove"
          description={`Are you sure you want to delete ${numberOfPatients}? This action cannot be undone.`}
          SecondaryButtonStyle={tw`bg-white !border-border_stroke text-text_primary  text-base`}
          PrimaryButtonStyle={tw`flex-1 !py-2.5 !rounded-md !font-medium !bg-error_50  hover:!bg-error_100 text-error text-base`}
        />
      )}
    </>
  );
}

export default GroupPatientAction;
