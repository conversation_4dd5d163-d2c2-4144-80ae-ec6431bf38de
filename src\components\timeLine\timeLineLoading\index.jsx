import React from 'react';
import Skeleton from '@/components/shared/skeleton';
import tw from 'twin.macro';

const TimeLineLoading = ({ label, length = 4 }) => {
  return (
    <>
      {label && (
        <p tw="font-medium text-text_secondary text-[0.75rem] mb-[0.8rem] opacity-80">{label}</p>
      )}
      <div tw="flex flex-col gap-[1.33rem]">
        {Array.from({ length }).map((_, idx) => {
          return (
            <div css={tw`w-full h-[0.75rem] flex justify-between`} key={idx}>
              <div css={tw`w-[50%] h-full rounded-full overflow-hidden`}>
                <Skeleton key={idx} tw="rounded-full" />
              </div>
              <div css={tw`w-[10%] h-full rounded-full overflow-hidden`}>
                <Skeleton key={idx} tw="rounded-full" />
              </div>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default TimeLineLoading;
