import { useClickAway } from '@uidotdev/usehooks';
import 'twin.macro';

/**
 * A dropdown menu list component that displays a list of clickable options.
 * Automatically closes when clicking outside the menu.
 *
 * @param {Object} props
 * @param {Function} props.handelClose - Function to handle closing the menu list
 * @param {Array<{id: string|number, text: string, onClick: Function}>} props.options - Array of menu options to display
 *
 * @example
 * <MenuList
 *   handelClose={() => setMenuOpen(false)}
 *   options={[
 *     { id: 1, text: "Edit Profile", onClick: () => handleEditProfile() },
 *     { id: 2, text: "Settings", onClick: () => handleSettings() },
 *     { id: 3, text: "Logout", onClick: () => handleLogout() }
 *   ]}
 * />
 *
 * @styling
 * - Container: 238px width, absolute positioning
 * - Background: Neutral color
 * - Border: 1px stroke color
 * - Shadow: 4px vertical, 10px blur, 25% opacity
 * - Border radius: 6px
 * - List items: 16px horizontal padding, 12px vertical padding
 * - Hover state: Neutral background color
 * - Transitions: Smooth background color changes
 *
 * @features
 * - Click-away to close functionality
 * - Hover effects on menu items
 * - Customizable options
 * - Automatic positioning
 * - Z-index management
 */

const MenuList = ({ handelClose = () => {}, options, listContainerStyle, listItemStyle }) => {
  const ref = useClickAway(() => {
    handelClose();
  });

  return (
    <ul
      ref={ref}
      tw="absolute top-12 cursor-pointer text-center right-0 w-[238px] border bg-neutral_100 border-border_stroke [box-shadow: 0 4px 10px 0 rgba(0,0,0,0.25)] rounded-[6px] z-10"
      css={listContainerStyle}
    >
      {options.map(item => (
        <li
          key={item.id}
          onClick={item.onClick}
          tw="px-[16px] hover:(transition-all duration-300 bg-neutral_200) py-[12px] border-b border-b-border_stroke last:border-b-0"
          css={listItemStyle}
        >
          {item.text}
        </li>
      ))}
    </ul>
  );
};

export default MenuList;
