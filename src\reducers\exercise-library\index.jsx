/* eslint-disable indent */
export const exerciseLibraryActions = {
  changeCard: 'changeCard',
  selectIsFavorite: 'selectIsFavorite',
  isEditFilter: 'isEditFilter',
  toggleFilterItem: 'toggleFilterItem',
  addCustomFilter: 'addCustomFilter',
  setEditFilterId: 'setEditFilterId',
  updateCustomFilter: 'updateCustomFilter',
  deleteCustomFilter: 'deleteCustomFilter',
  openCollapseModel: 'openCollapseModel',
  openTemplateModel: 'openTemplateModel',
  selectedTemplateId: 'selectedTemplateId',
  collapseHasTab: 'collapseHasTab',
  prescriptionTabArray: 'prescriptionTabArray',
  activePrescriptionTab: 'activePrescriptionTab',
  resetPrescription: 'resetPrescription',
  openSaveTemplateModal: 'openSaveTemplateModal',
  setFirstTemplateIds: 'setFirstTemplateIds',
  resetTemplatesIds: 'resetTemplatesIds',
  openPrescriptionModal: 'openPrescriptionModal',
  cancelFilter: 'cancelFilter',
  opendDeleteModal: 'opendDeleteModal',
  setPendingPath: 'setPendingPath',
  selectedItemToDelete: 'selectedItemToDelete',
};

export const initialValues = {
  changeCard: [],
  selectIsFavorite: false,
  isEditFilter: false,
  selectedFilteredItem: [],
  customFilters: [],
  editFilterId: null,
  openCollapseModel: true,
  openTemplateModel: false,
  selectedTemplateId: null,
  collapseHasTab: false,
  openSaveTemplateModal: false,
  prescriptionTabArray: [],
  activePrescriptionTab: 0,
  firstTemplateIds: [],
  openPrescriptionModal: false,
  cancelFilter: false,
  opendDeleteModal: false,
  setPendingPath: null,
  selectedItemToDelete: null,
};

export function exerciseLibraryReducer(state, action) {
  switch (action.type) {
    case exerciseLibraryActions.isEditFilter:
      return { ...state, isEditFilter: action.payload };
    case exerciseLibraryActions.toggleFilterItem: {
      const itemExists = state.selectedFilteredItem.includes(action.payload);
      return {
        ...state,
        selectedFilteredItem: itemExists
          ? state.selectedFilteredItem.filter(item => item !== action.payload)
          : [...state.selectedFilteredItem, action.payload],
      };
    }
    case exerciseLibraryActions.addCustomFilter: {
      const { name, ids } = action.payload;
      const newFilter = {
        id: Date.now(),
        title: name,
        exerciseIds: ids,
      };
      return {
        ...state,
        customFilters: [...state.customFilters, newFilter],
        isEditFilter: false,
        selectedFilteredItem: [],
      };
    }
    case exerciseLibraryActions.setEditFilterId: {
      const editFilter = state.customFilters.find(filter => filter.id === action.payload);
      return {
        ...state,
        editFilterId: action.payload,
        selectedFilteredItem: editFilter ? editFilter.exerciseIds : [],
      };
    }
    case exerciseLibraryActions.updateCustomFilter: {
      const { id, name, ids } = action.payload;
      return {
        ...state,
        customFilters: state.customFilters.map(filter =>
          filter.id === id
            ? {
                ...filter,
                title: name,
                exerciseIds: ids,
              }
            : filter
        ),
        isEditFilter: false,
        selectedFilteredItem: [],
        editFilterId: null,
      };
    }
    case exerciseLibraryActions.deleteCustomFilter:
      return {
        ...state,
        customFilters: state.customFilters.filter(filter => filter.id !== action.payload),
        isEditFilter: false,
        selectedFilteredItem: [],
        editFilterId: null,
      };
    case exerciseLibraryActions.openCollapseModel:
      return { ...state, openCollapseModel: action.payload };
    case exerciseLibraryActions.prescriptionTabArray:
      return { ...state, prescriptionTabArray: [...state.prescriptionTabArray, ...action.payload] };
    case exerciseLibraryActions.collapseHasTab:
      return { ...state, collapseHasTab: action.payload };
    case exerciseLibraryActions.openTemplateModel:
      return { ...state, openTemplateModel: action.payload };
    case exerciseLibraryActions.activePrescriptionTab:
      return { ...state, activePrescriptionTab: action.payload };
    case exerciseLibraryActions.selectedTemplateId:
      return { ...state, selectedTemplateId: action.payload };
    case exerciseLibraryActions.openSaveTemplateModal:
      return { ...state, openSaveTemplateModal: action.payload };
    case exerciseLibraryActions.resetPrescription:
      return { ...state, prescriptionTabArray: [] };
    case exerciseLibraryActions.resetTemplatesIds:
      return { ...state, firstTemplateIds: [] };
    case exerciseLibraryActions.setFirstTemplateIds:
      return { ...state, firstTemplateIds: action.payload };
    case exerciseLibraryActions.openPrescriptionModal:
      return { ...state, openPrescriptionModal: action.payload };
    case exerciseLibraryActions.cancelFilter:
      return { ...state, cancelFilter: action.payload };
    case exerciseLibraryActions.setPendingPath:
      return { ...state, setPendingPath: action.payload };
    case exerciseLibraryActions.opendDeleteModal:
      return { ...state, opendDeleteModal: action.payload };
    case exerciseLibraryActions.selectedItemToDelete:
      return { ...state, selectedItemToDelete: action.payload };
    default:
      return state;
  }
}
