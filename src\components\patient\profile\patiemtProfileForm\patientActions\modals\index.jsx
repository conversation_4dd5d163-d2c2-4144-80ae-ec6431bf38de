import userRounded from '@/assets/svgs/patient/user-rounded-outline.svg';
import TransferPatient from './transferPatient';
import ActionModal from '@/components/actionModal';
import tw from 'twin.macro';
function PatientModals({ openModal, handleCloseModal, patient }) {
  const openModalCheck = typeof openModal === 'object' ? openModal.type : openModal;
  return (
    <>
      {openModalCheck === 'transfer' && (
        <TransferPatient
          open={openModalCheck === 'transfer'}
          handleClose={handleCloseModal}
          patient={{ patient_name: patient.patientName, patient_id: patient.patient_id }}
        />
      )}

      {openModalCheck === 'discharge' && (
        <ActionModal
          open={openModalCheck === 'discharge'}
          handleClose={handleCloseModal}
          primaryActionHandler={handleCloseModal}
          title={`Discharge patient: ${patient.patientName}`}
          customHeaderStyle={tw`!bg-Primary_100 !border-Primary_50`}
          description={
            'Are you sure you want to discharge this patient? This action cannot be undone.'
          }
          actionButtonText="Confirm"
          cancelButtonText="Cancel"
          SecondaryButtonStyle={tw`bg-white !border-border_stroke text-text_primary !text-base`}
          customIcon={userRounded}
          PrimaryButtonStyle={tw`flex-1 !py-2.5 !rounded-md !font-medium !bg-Primary border-Primary_600 hover:!bg-Primary_600 text-text_primary !text-base`}
        />
      )}

      {openModalCheck === 'delete' && (
        <ActionModal
          open={openModalCheck === 'delete'}
          title={`Remove Patient: ${patient.patientName}`}
          handleClose={handleCloseModal}
          primaryActionHandler={handleCloseModal}
          description="Are you sure you want to delete this patient? This action cannot be undone."
          actionButtonText="Remove"
          SecondaryButtonStyle={tw`bg-white !border-border_stroke text-text_primary  text-base`}
          PrimaryButtonStyle={tw`flex-1 !py-2.5 !rounded-md !font-medium !bg-error_50  hover:!bg-error_100 text-error text-base`}
        />
      )}
    </>
  );
}

export default PatientModals;
